package com.yaduo.devopstools.api.role.dto.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.yaduo.infras.core.base.bean.RequestList;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

/**
 * <AUTHOR> @date 2021-12-06 09:39:45
 */
@Data
@EqualsAndHashCode(callSuper = false)
@AllArgsConstructor
@NoArgsConstructor
@Builder
//@ApiModel(value = "RoleRequest", description = "列表请求：角色")
@Schema(description = "列表请求：角色")
public class RoleRequest extends RequestList {

    /**
     * 自增主键
     */
    //@ApiModelProperty(value = "自增主键")
    @Schema(description = "自增主键")
    @JsonProperty("id")
    private Long id;

    /**
     * 角色名称
     */
    //@ApiModelProperty(value = "角色名称")
    @Schema(description = "角色名称")
    @JsonProperty("role_name")
    private String roleName;

    /**
     * 数据状态;0，正常；1，不可用；2，过时；4，已删除
     */
    //@ApiModelProperty(value = "数据状态;0，正常；1，不可用；2，过时；4，已删除")
    @Schema(description = "数据状态;0，正常；1，不可用；2，过时；4，已删除")
    @JsonProperty("data_status")
    private Integer dataStatus;

    /**
     * 创建人
     */
    //@ApiModelProperty(value = "创建人")
    @Schema(description = "创建人")
    @JsonProperty("created_by")
    private Long createdBy;

    /**
     * 创建时间-起;
     */
    //@ApiModelProperty(value = "创建时间-起;")
    @Schema(description = "创建时间-起;")
    @JsonProperty("gmt_create_start")
    private String gmtCreateStart;

    /**
     * 创建时间-止;
     */
    //@ApiModelProperty(value = "创建时间-止;")
    @Schema(description = "创建时间-止;")
    @JsonProperty("gmt_create_end")
    private String gmtCreateEnd;

    /**
     * 更新人
     */
    //@ApiModelProperty(value = "更新人")
    @Schema(description = "更新人")
    @JsonProperty("modified_by")
    private Long modifiedBy;

    /**
     * 更新时间-起;
     */
    //@ApiModelProperty(value = "更新时间-起;")
    @Schema(description = "更新时间-起;")
    @JsonProperty("gmt_modified_start")
    private String gmtModifiedStart;

    /**
     * 更新时间-止;
     */
    //@ApiModelProperty(value = "更新时间-止;")
    @Schema(description = "更新时间-止;")
    @JsonProperty("gmt_modified_end")
    private String gmtModifiedEnd;

    /**
     * TraceID
     */
    //@ApiModelProperty(value = "TraceID")
    @Schema(description = "TraceID")
    @JsonProperty("trace_id")
    private String traceId;

    /**
     * 自动trim
     */
    public void autoTrim() {
        if (this.roleName != null) {
            this.roleName = this.roleName.trim();
        }
        if (this.traceId != null) {
            this.traceId = this.traceId.trim();
        }
    }
}
