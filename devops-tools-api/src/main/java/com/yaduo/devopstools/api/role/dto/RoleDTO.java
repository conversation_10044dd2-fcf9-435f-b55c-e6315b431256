package com.yaduo.devopstools.api.role.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

/**
 * <AUTHOR> @date 2021-12-06 09:34:44
 */
@Data
@EqualsAndHashCode(callSuper = false)
@AllArgsConstructor
@NoArgsConstructor
@Builder
//@ApiModel(value = "RoleDTO", description = "角色")
//@ApiModel(value = "RoleDTO", description = "角色")
@Schema(description = "角色")
public class RoleDTO {

    /**
     * 自增主键
     */
    //@ApiModelProperty(value = "自增主键")
    @Schema(description = "自增主键")
    private Long id;

    /**
     * 角色名称
     */
    //@ApiModelProperty(value = "角色名称")
    @Schema(description = "角色名称")
    private String roleName;

    /**
     * 数据状态;0，正常；1，不可用；2，过时；4，已删除
     */
    //@ApiModelProperty(value = "数据状态;0，正常；1，不可用；2，过时；4，已删除")
    @Schema(description = "数据状态;0，正常；1，不可用；2，过时；4，已删除")
    private Integer dataStatus;

    /**
     * 创建人
     */
    //@ApiModelProperty(value = "创建人")
    @Schema(description = "创建人")
    private Long createdBy;

    /**
     * 创建时间
     */
    //@ApiModelProperty(value = "创建时间")
    @Schema(description = "创建时间")
    private String gmtCreate;

    /**
     * 更新人
     */
    //@ApiModelProperty(value = "更新人")
    @Schema(description = "更新人")
    private Long modifiedBy;

    /**
     * 更新时间
     */
    //@ApiModelProperty(value = "更新时间")
    @Schema(description = "更新时间")
    private String gmtModified;

    /**
     * TraceID
     */
    //@ApiModelProperty(value = "TraceID")
    @Schema(description = "TraceID")
    private String traceId;

    /**
     * 自动trim
     */
    public void autoTrim() {
        if (this.roleName != null) {
            this.roleName = this.roleName.trim();
        }
        if (this.gmtCreate != null) {
            this.gmtCreate = this.gmtCreate.trim();
        }
        if (this.gmtModified != null) {
            this.gmtModified = this.gmtModified.trim();
        }
        if (this.traceId != null) {
            this.traceId = this.traceId.trim();
        }
    }
}
