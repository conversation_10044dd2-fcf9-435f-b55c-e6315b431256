plugins {
    id 'java-library'
    id 'maven-publish'
}

def testVersion = "1-SNAPSHOT"
def publishArtifactId = "test-demo-api"
def publishGroupId = "com.yaduo.one"
def releaseVersion = "1.0.0.RELEASE"
publishing {
    publications {
        maven(MavenPublication) {
            from components.java
            groupId = publishGroupId
            artifactId = publishArtifactId
        }
    }
    repositories {
        maven {
            name = 'release'
            url = "https://nexus.corp.yaduo.com/repository/maven-releases/"
            credentials {
                username = "${nexus_yaduo_user}"
                password = "${nexus_yaduo_pwd}"
            }
        }
        maven {
            name = 'snap'
            url = "https://nexus.corp.yaduo.com/repository/maven-snapshots/"
            credentials {
                username = "${nexus_yaduo_user}"
                password = "${nexus_yaduo_pwd}"
            }
        }
    }
}
tasks.register('upSnap') {
    group = 'publishing'
    version = testVersion
    dependsOn tasks.withType(PublishToMavenRepository).matching {
        it.repository == publishing.repositories.snap
    }
}
tasks.register('upRelease') {
    group = 'publishing'
    version = releaseVersion
    dependsOn tasks.withType(PublishToMavenRepository).matching {
        it.repository == publishing.repositories.release
    }
}
dependencies {
    compileOnly("com.yaduo.infras:core-lib:1.1.5.RELEASE")
    compileOnly("com.yaduo.infras:core-model:1.1.5.RELEASE")
    compileOnly("org.springframework.boot:spring-boot-starter-web:${springBootVersion}")
    compileOnly("org.springdoc:springdoc-openapi-ui:1.6.8")

}

test {
    useJUnitPlatform()
}
