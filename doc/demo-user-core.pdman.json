{"modules": [{"name": "yaduo_demo_user", "chnname": "用户核心", "entities": [{"title": "user_core", "fields": [{"name": "id", "type": "bigint unsigned", "remark": "", "chnname": "自增主键", "pk": true, "notNull": true, "autoIncrement": true, "relationNoShow": false, "uiHint": "Number"}, {"name": "user_type", "type": "short int", "remark": "", "chnname": "用户类型", "pk": false, "notNull": true, "defaultValue": "0"}, {"name": "data_status", "type": "short int", "remark": "0，正常；1，不可用；2，过时；4，已删除", "chnname": "数据状态", "notNull": true, "defaultValue": "0", "relationNoShow": false}, {"name": "created_by", "type": "bigint unsigned", "remark": "", "chnname": "创建人", "defaultValue": "0", "notNull": true, "relationNoShow": true, "uiHint": "Number"}, {"name": "gmt_create", "type": "DATETIME", "remark": "", "chnname": "创建时间", "notNull": true, "relationNoShow": true, "defaultValue": "CURRENT_TIMESTAMP", "uiHint": "DatePicker"}, {"name": "modified_by", "type": "bigint unsigned", "remark": "", "chnname": "更新人", "defaultValue": "0", "notNull": true, "relationNoShow": true, "uiHint": "Number"}, {"name": "gmt_modified", "type": "DATETIME", "remark": "", "chnname": "更新时间", "notNull": true, "relationNoShow": true, "defaultValue": "CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP", "uiHint": "DatePicker"}, {"name": "trace_id", "type": "Intro", "remark": "", "chnname": "TraceID", "relationNoShow": true, "uiHint": "Text"}], "indexs": [], "headers": [{"fieldName": "ch<PERSON>me", "relationNoShow": false}, {"fieldName": "name", "relationNoShow": false}, {"fieldName": "type", "relationNoShow": false}, {"fieldName": "dataType", "relationNoShow": true}, {"fieldName": "remark", "relationNoShow": true}, {"fieldName": "pk", "relationNoShow": false}, {"fieldName": "notNull", "relationNoShow": true}, {"fieldName": "autoIncrement", "relationNoShow": true}, {"fieldName": "defaultValue", "relationNoShow": true}, {"fieldName": "relationNoShow", "relationNoShow": true}, {"fieldName": "uiHint", "relationNoShow": true}], "chnname": "用户核心"}, {"title": "user_info", "fields": [{"name": "id", "type": "bigint unsigned", "remark": "", "chnname": "自增主键", "pk": true, "notNull": true, "autoIncrement": true, "relationNoShow": false, "uiHint": "Number"}, {"name": "uid", "type": "bigint unsigned", "remark": "", "chnname": "UID", "notNull": true, "pk": true}, {"name": "user_name", "type": "VARCHAR(32)", "remark": "", "chnname": "名字"}, {"name": "nick_name", "type": "VARCHAR(32)", "remark": "", "chnname": "昵称"}, {"name": "birthday", "type": "DATETIME", "remark": "", "chnname": "生日"}, {"name": "birthday_type", "type": "short int", "remark": "0:公历；1，农历", "chnname": "生日历法", "defaultValue": "0", "notNull": true}, {"name": "avatar", "type": "LongString", "remark": "", "chnname": "头像"}, {"name": "data_status", "type": "short int", "remark": "0，正常；1，不可用；2，过时；4，已删除", "chnname": "数据状态", "notNull": true, "defaultValue": "0", "relationNoShow": false}, {"name": "created_by", "type": "bigint unsigned", "remark": "", "chnname": "创建人", "defaultValue": "0", "notNull": true, "relationNoShow": true, "uiHint": "Number"}, {"name": "gmt_create", "type": "DATETIME", "remark": "", "chnname": "创建时间", "notNull": true, "relationNoShow": true, "defaultValue": "CURRENT_TIMESTAMP", "uiHint": "DatePicker"}, {"name": "modified_by", "type": "bigint unsigned", "remark": "", "chnname": "更新人", "defaultValue": "0", "notNull": true, "relationNoShow": true, "uiHint": "Number"}, {"name": "gmt_modified", "type": "DATETIME", "remark": "", "chnname": "更新时间", "notNull": true, "relationNoShow": true, "defaultValue": "CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP", "uiHint": "DatePicker"}, {"name": "trace_id", "type": "Intro", "remark": "", "chnname": "TraceID", "relationNoShow": true, "uiHint": "Text"}], "indexs": [], "headers": [{"fieldName": "ch<PERSON>me", "relationNoShow": false}, {"fieldName": "name", "relationNoShow": false}, {"fieldName": "type", "relationNoShow": false}, {"fieldName": "dataType", "relationNoShow": true}, {"fieldName": "remark", "relationNoShow": true}, {"fieldName": "pk", "relationNoShow": false}, {"fieldName": "notNull", "relationNoShow": true}, {"fieldName": "autoIncrement", "relationNoShow": true}, {"fieldName": "defaultValue", "relationNoShow": true}, {"fieldName": "relationNoShow", "relationNoShow": true}, {"fieldName": "uiHint", "relationNoShow": true}], "chnname": "用户信息"}, {"title": "user_passport", "fields": [{"name": "id", "type": "bigint unsigned", "remark": "", "chnname": "自增主键", "pk": true, "notNull": true, "autoIncrement": true, "relationNoShow": false, "uiHint": "Number"}, {"name": "uid", "type": "bigint unsigned", "remark": "", "chnname": "UID", "notNull": true}, {"name": "user_type", "type": "short int", "remark": "", "chnname": "账号类型", "notNull": true, "defaultValue": "0"}, {"name": "credential", "type": "VARCHAR(128)", "remark": "", "chnname": "凭证", "notNull": true}, {"name": "type", "type": "short int", "remark": "0,账号；1,邮箱；2,手机号", "chnname": "凭证类型", "notNull": true, "defaultValue": "0"}, {"name": "data_status", "type": "short int", "remark": "0，正常；1，不可用；2，过时；4，已删除", "chnname": "数据状态", "notNull": true, "defaultValue": "0", "relationNoShow": false}, {"name": "created_by", "type": "bigint unsigned", "remark": "", "chnname": "创建人", "defaultValue": "0", "notNull": true, "relationNoShow": true, "uiHint": "Number"}, {"name": "gmt_create", "type": "DATETIME", "remark": "", "chnname": "创建时间", "notNull": true, "relationNoShow": true, "defaultValue": "CURRENT_TIMESTAMP", "uiHint": "DatePicker"}, {"name": "modified_by", "type": "bigint unsigned", "remark": "", "chnname": "更新人", "defaultValue": "0", "notNull": true, "relationNoShow": true, "uiHint": "Number"}, {"name": "gmt_modified", "type": "DATETIME", "remark": "", "chnname": "更新时间", "notNull": true, "relationNoShow": true, "defaultValue": "CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP", "uiHint": "DatePicker"}, {"name": "trace_id", "type": "Intro", "remark": "", "chnname": "TraceID", "relationNoShow": true, "uiHint": "Text"}], "indexs": [{"name": "uniq_user_passport", "isUnique": true, "fields": ["user_type", "credential", "type", "data_status"]}], "headers": [{"fieldName": "ch<PERSON>me", "relationNoShow": false}, {"fieldName": "name", "relationNoShow": false}, {"fieldName": "type", "relationNoShow": false}, {"fieldName": "dataType", "relationNoShow": true}, {"fieldName": "remark", "relationNoShow": true}, {"fieldName": "pk", "relationNoShow": false}, {"fieldName": "notNull", "relationNoShow": true}, {"fieldName": "autoIncrement", "relationNoShow": true}, {"fieldName": "defaultValue", "relationNoShow": true}, {"fieldName": "relationNoShow", "relationNoShow": true}, {"fieldName": "uiHint", "relationNoShow": true}], "chnname": "认证"}, {"title": "role", "fields": [{"name": "id", "type": "bigint unsigned", "remark": "", "chnname": "自增主键", "pk": true, "notNull": true, "autoIncrement": true, "relationNoShow": false, "uiHint": "Number"}, {"name": "role_name", "type": "VARCHAR(32)", "remark": "", "chnname": "角色名称"}, {"name": "data_status", "type": "short int", "remark": "0，正常；1，不可用；2，过时；4，已删除", "chnname": "数据状态", "notNull": true, "defaultValue": "0", "relationNoShow": false}, {"name": "created_by", "type": "bigint unsigned", "remark": "", "chnname": "创建人", "defaultValue": "0", "notNull": true, "relationNoShow": true, "uiHint": "Number"}, {"name": "gmt_create", "type": "DATETIME", "remark": "", "chnname": "创建时间", "notNull": true, "relationNoShow": true, "defaultValue": "CURRENT_TIMESTAMP", "uiHint": "DatePicker"}, {"name": "modified_by", "type": "bigint unsigned", "remark": "", "chnname": "更新人", "defaultValue": "0", "notNull": true, "relationNoShow": true, "uiHint": "Number"}, {"name": "gmt_modified", "type": "DATETIME", "remark": "", "chnname": "更新时间", "notNull": true, "relationNoShow": true, "defaultValue": "CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP", "uiHint": "DatePicker"}, {"name": "trace_id", "type": "Intro", "remark": "", "chnname": "TraceID", "relationNoShow": true, "uiHint": "Text"}], "indexs": [], "headers": [{"fieldName": "ch<PERSON>me", "relationNoShow": false}, {"fieldName": "name", "relationNoShow": false}, {"fieldName": "type", "relationNoShow": false}, {"fieldName": "dataType", "relationNoShow": true}, {"fieldName": "remark", "relationNoShow": true}, {"fieldName": "pk", "relationNoShow": false}, {"fieldName": "notNull", "relationNoShow": true}, {"fieldName": "autoIncrement", "relationNoShow": true}, {"fieldName": "defaultValue", "relationNoShow": true}, {"fieldName": "relationNoShow", "relationNoShow": true}, {"fieldName": "uiHint", "relationNoShow": true}], "chnname": "角色"}, {"title": "role_user", "fields": [{"name": "id", "type": "bigint unsigned", "remark": "", "chnname": "自增主键", "pk": true, "notNull": true, "autoIncrement": true, "relationNoShow": false, "uiHint": "Number"}, {"name": "role_id", "type": "bigint unsigned", "remark": "", "chnname": "角色ID", "notNull": true}, {"name": "uid", "type": "bigint unsigned", "remark": "", "chnname": "uid", "notNull": true}, {"name": "data_status", "type": "short int", "remark": "0，正常；1，不可用；2，过时；4，已删除", "chnname": "数据状态", "notNull": true, "defaultValue": "0", "relationNoShow": false}, {"name": "created_by", "type": "bigint unsigned", "remark": "", "chnname": "创建人", "defaultValue": "0", "notNull": true, "relationNoShow": true, "uiHint": "Number"}, {"name": "gmt_create", "type": "DATETIME", "remark": "", "chnname": "创建时间", "notNull": true, "relationNoShow": true, "defaultValue": "CURRENT_TIMESTAMP", "uiHint": "DatePicker"}, {"name": "modified_by", "type": "bigint unsigned", "remark": "", "chnname": "更新人", "defaultValue": "0", "notNull": true, "relationNoShow": true, "uiHint": "Number"}, {"name": "gmt_modified", "type": "DATETIME", "remark": "", "chnname": "更新时间", "notNull": true, "relationNoShow": true, "defaultValue": "CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP", "uiHint": "DatePicker"}, {"name": "trace_id", "type": "Intro", "remark": "", "chnname": "TraceID", "relationNoShow": true, "uiHint": "Text"}], "indexs": [{"name": "uk_role_user", "isUnique": true, "fields": ["role_id", "uid", "data_status"]}], "headers": [{"fieldName": "ch<PERSON>me", "relationNoShow": false}, {"fieldName": "name", "relationNoShow": false}, {"fieldName": "type", "relationNoShow": false}, {"fieldName": "dataType", "relationNoShow": true}, {"fieldName": "remark", "relationNoShow": true}, {"fieldName": "pk", "relationNoShow": false}, {"fieldName": "notNull", "relationNoShow": true}, {"fieldName": "autoIncrement", "relationNoShow": true}, {"fieldName": "defaultValue", "relationNoShow": true}, {"fieldName": "relationNoShow", "relationNoShow": true}, {"fieldName": "uiHint", "relationNoShow": true}], "chnname": "角色关联的用户"}, {"title": "user_contact", "fields": [{"name": "id", "type": "bigint unsigned", "remark": "", "chnname": "自增主键", "pk": true, "notNull": true, "autoIncrement": true, "relationNoShow": false, "uiHint": "Number"}, {"name": "uid", "type": "bigint unsigned", "remark": "", "chnname": "UID", "notNull": true}, {"name": "contact_type", "type": "int(4)", "remark": "0:手机；1，邮箱", "chnname": "联系方式类型"}, {"name": "contact_prefix", "type": "VARCHAR(32)", "remark": "如果是手机号，这里表示区号", "chnname": "联系方式值前缀"}, {"name": "contact_value", "type": "VARCHAR(32)", "remark": "", "chnname": "联系方式值"}, {"name": "data_status", "type": "short int", "remark": "0，正常；1，不可用；2，过时；4，已删除", "chnname": "数据状态", "notNull": true, "defaultValue": "0", "relationNoShow": false}, {"name": "created_by", "type": "bigint unsigned", "remark": "", "chnname": "创建人", "defaultValue": "0", "notNull": true, "relationNoShow": true, "uiHint": "Number"}, {"name": "gmt_create", "type": "DATETIME", "remark": "", "chnname": "创建时间", "notNull": true, "relationNoShow": true, "defaultValue": "CURRENT_TIMESTAMP", "uiHint": "DatePicker"}, {"name": "modified_by", "type": "bigint unsigned", "remark": "", "chnname": "更新人", "defaultValue": "0", "notNull": true, "relationNoShow": true, "uiHint": "Number"}, {"name": "gmt_modified", "type": "DATETIME", "remark": "", "chnname": "更新时间", "notNull": true, "relationNoShow": true, "defaultValue": "CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP", "uiHint": "DatePicker"}, {"name": "trace_id", "type": "Intro", "remark": "", "chnname": "TraceID", "relationNoShow": true, "uiHint": "Text"}], "indexs": [], "headers": [{"fieldName": "ch<PERSON>me", "relationNoShow": false}, {"fieldName": "name", "relationNoShow": false}, {"fieldName": "type", "relationNoShow": false}, {"fieldName": "dataType", "relationNoShow": true}, {"fieldName": "remark", "relationNoShow": true}, {"fieldName": "pk", "relationNoShow": false}, {"fieldName": "notNull", "relationNoShow": true}, {"fieldName": "autoIncrement", "relationNoShow": true}, {"fieldName": "defaultValue", "relationNoShow": true}, {"fieldName": "relationNoShow", "relationNoShow": true}, {"fieldName": "uiHint", "relationNoShow": true}], "chnname": "用户联系方式"}, {"title": "login_history", "fields": [{"name": "id", "type": "bigint unsigned", "remark": "", "chnname": "自增主键", "pk": true, "notNull": true, "autoIncrement": true, "relationNoShow": false, "uiHint": "Number"}, {"name": "uid", "type": "bigint unsigned", "remark": "", "chnname": "UID", "notNull": true}, {"name": "login_type", "type": "short int", "remark": "0：账号密码；1，短信验证码；3，邮箱验证码；4，微信扫码；5，支付宝扫码；6，钉钉扫码", "chnname": "登录方式", "notNull": true}, {"name": "login_platform", "type": "short int", "remark": "0：官网；1，微信小程序；2，app；3，支付宝小程序", "chnname": "登录平台"}, {"name": "login_session", "type": "Intro", "remark": "", "chnname": "登录的key"}, {"name": "data_status", "type": "short int", "remark": "0，正常；1，不可用；2，过时；4，已删除", "chnname": "数据状态", "notNull": true, "defaultValue": "0", "relationNoShow": false}, {"name": "created_by", "type": "bigint unsigned", "remark": "", "chnname": "创建人", "defaultValue": "0", "notNull": true, "relationNoShow": true, "uiHint": "Number"}, {"name": "gmt_create", "type": "DATETIME", "remark": "", "chnname": "创建时间", "notNull": true, "relationNoShow": true, "defaultValue": "CURRENT_TIMESTAMP", "uiHint": "DatePicker"}, {"name": "modified_by", "type": "bigint unsigned", "remark": "", "chnname": "更新人", "defaultValue": "0", "notNull": true, "relationNoShow": true, "uiHint": "Number"}, {"name": "gmt_modified", "type": "DATETIME", "remark": "", "chnname": "更新时间", "notNull": true, "relationNoShow": true, "defaultValue": "CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP", "uiHint": "DatePicker"}, {"name": "trace_id", "type": "Intro", "remark": "", "chnname": "TraceID", "relationNoShow": true, "uiHint": "Text"}], "indexs": [], "headers": [{"fieldName": "ch<PERSON>me", "relationNoShow": false}, {"fieldName": "name", "relationNoShow": false}, {"fieldName": "type", "relationNoShow": false}, {"fieldName": "dataType", "relationNoShow": true}, {"fieldName": "remark", "relationNoShow": true}, {"fieldName": "pk", "relationNoShow": false}, {"fieldName": "notNull", "relationNoShow": true}, {"fieldName": "autoIncrement", "relationNoShow": true}, {"fieldName": "defaultValue", "relationNoShow": true}, {"fieldName": "relationNoShow", "relationNoShow": true}, {"fieldName": "uiHint", "relationNoShow": true}], "chnname": "登录历史"}, {"title": "user_pwd", "fields": [{"name": "id", "type": "bigint unsigned", "remark": "", "chnname": "自增主键", "pk": true, "notNull": true, "autoIncrement": true, "relationNoShow": false, "uiHint": "Number"}, {"name": "uid", "type": "bigint unsigned", "remark": "", "chnname": "UID"}, {"name": "pwd", "type": "<PERSON><PERSON><PERSON>(2048)", "remark": "", "chnname": "密码"}, {"name": "data_status", "type": "short int", "remark": "0，正常；1，不可用；2，过时；4，已删除", "chnname": "数据状态", "notNull": true, "defaultValue": "0", "relationNoShow": false}, {"name": "created_by", "type": "bigint unsigned", "remark": "", "chnname": "创建人", "defaultValue": "0", "notNull": true, "relationNoShow": true, "uiHint": "Number"}, {"name": "gmt_create", "type": "DATETIME", "remark": "", "chnname": "创建时间", "notNull": true, "relationNoShow": true, "defaultValue": "CURRENT_TIMESTAMP", "uiHint": "DatePicker"}, {"name": "modified_by", "type": "bigint unsigned", "remark": "", "chnname": "更新人", "defaultValue": "0", "notNull": true, "relationNoShow": true, "uiHint": "Number"}, {"name": "gmt_modified", "type": "DATETIME", "remark": "", "chnname": "更新时间", "notNull": true, "relationNoShow": true, "defaultValue": "CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP", "uiHint": "DatePicker"}, {"name": "trace_id", "type": "Intro", "remark": "", "chnname": "TraceID", "relationNoShow": true, "uiHint": "Text"}], "indexs": [], "headers": [{"fieldName": "ch<PERSON>me", "relationNoShow": false}, {"fieldName": "name", "relationNoShow": false}, {"fieldName": "type", "relationNoShow": false}, {"fieldName": "dataType", "relationNoShow": true}, {"fieldName": "remark", "relationNoShow": true}, {"fieldName": "pk", "relationNoShow": false}, {"fieldName": "notNull", "relationNoShow": true}, {"fieldName": "autoIncrement", "relationNoShow": true}, {"fieldName": "defaultValue", "relationNoShow": true}, {"fieldName": "relationNoShow", "relationNoShow": true}, {"fieldName": "uiHint", "relationNoShow": true}], "chnname": "用户密码"}], "graphCanvas": {"nodes": [], "edges": []}, "associations": []}], "dataTypeDomains": {"datatype": [{"name": "VARCHAR(32)", "code": "VARCHAR(32)", "apply": {"MYSQL": {"type": "VARCHAR(32)"}, "ORACLE": {"type": "NVARCHAR2(32)"}, "MapperXml": {"type": "VARCHAR"}, "JAVA_DO": {"type": "String"}, "JAVA_VO": {"type": "String"}, "JAVA_DTO": {"type": "String"}, "Python": {"type": "str"}, "JavaApi": {"type": "String"}, "JavaService": {"type": "String"}, "MapperApi": {"type": "String"}, "JavaServiceManager": {"type": "String"}, "RequestQuery": {"type": "String"}, "RequestBatchExacty": {"type": "String"}}}, {"name": "VARCHAR(128)", "code": "VARCHAR(128)", "apply": {"MYSQL": {"type": "VARCHAR(128)"}, "ORACLE": {"type": "NVARCHAR2(128)"}, "MapperXml": {"type": "VARCHAR"}, "JAVA_VO": {"type": "String"}, "JAVA_DO": {"type": "String"}, "JAVA_DTO": {"type": "String"}, "Python": {"type": "str"}, "JavaApi": {"type": "String"}, "JavaService": {"type": "String"}, "MapperApi": {"type": "String"}, "JavaServiceManager": {"type": "String"}, "RequestQuery": {"type": "String"}, "RequestBatchExacty": {"type": "String"}}}, {"apply": {"MYSQL": {"type": "VARCHAR(256)"}, "ORACLE": {"type": "<PERSON><PERSON><PERSON>(256)"}, "MapperXml": {"type": "VARCHAR"}, "JAVA_VO": {"type": "String"}, "JAVA_DO": {"type": "String"}, "JAVA_DTO": {"type": "String"}, "Python": {"type": "str"}, "JavaApi": {"type": "String"}, "JavaService": {"type": "String"}, "MapperApi": {"type": "String"}, "JavaServiceManager": {"type": "String"}, "RequestQuery": {"type": "String"}, "RequestBatchExacty": {"type": "String"}}, "code": "<PERSON><PERSON><PERSON>(256)", "name": "VARCHAR(256)"}, {"name": "VARCHAR(512)", "code": "Intro", "apply": {"MYSQL": {"type": "VARCHAR(512)"}, "ORACLE": {"type": "NVARCHAR2(512)"}, "MapperXml": {"type": "VARCHAR"}, "JAVA_VO": {"type": "String"}, "JAVA_DO": {"type": "String"}, "JAVA_DTO": {"type": "String"}, "Python": {"type": "str"}, "JavaApi": {"type": "String"}, "JavaService": {"type": "String"}, "MapperApi": {"type": "String"}, "JavaServiceManager": {"type": "String"}, "RequestQuery": {"type": "String"}, "RequestBatchExacty": {"type": "String"}}}, {"name": "VARCHAR(1024)", "code": "LongString", "apply": {"MYSQL": {"type": "VARCHAR(1024)"}, "ORACLE": {"type": "NVARCHAR2(1024)"}, "MapperXml": {"type": "VARCHAR"}, "JAVA_VO": {"type": "String"}, "JAVA_DO": {"type": "String"}, "JAVA_DTO": {"type": "String"}, "Python": {"type": "str"}, "JavaApi": {"type": "String"}, "JavaService": {"type": "String"}, "MapperApi": {"type": "String"}, "JavaServiceManager": {"type": "String"}, "RequestQuery": {"type": "String"}, "RequestBatchExacty": {"type": "String"}}}, {"code": "<PERSON><PERSON><PERSON>(2048)", "name": "VARCHAR(2048)", "apply": {"MYSQL": {"type": "VARCHAR(2048)"}, "ORACLE": {"type": "<PERSON><PERSON><PERSON>(2048)"}, "MapperXml": {"type": "VARCHAR"}, "JAVA_VO": {"type": "String"}, "JAVA_DO": {"type": "String"}, "JAVA_DTO": {"type": "String"}, "Python": {"type": "str"}, "JavaApi": {"type": "String"}, "JavaService": {"type": "String"}, "MapperApi": {"type": "String"}, "JavaServiceManager": {"type": "String"}, "RequestQuery": {"type": "String"}, "RequestBatchExacty": {"type": "String"}}}, {"name": "TEXT", "code": "LongText", "apply": {"MYSQL": {"type": "TEXT"}, "ORACLE": {"type": "CLOB"}, "MapperXml": {"type": "LONGVARCHAR"}, "JAVA_VO": {"type": "String"}, "JAVA_DO": {"type": "String"}, "JAVA_DTO": {"type": "String"}, "Python": {"type": "str"}, "JavaApi": {"type": "String"}, "JavaService": {"type": "String"}, "MapperApi": {"type": "String"}, "JavaServiceManager": {"type": "String"}, "RequestQuery": {"type": "String"}, "RequestBatchExacty": {"type": "String"}}}, {"name": "DECIMAL(64,16)", "code": "Double", "apply": {"MYSQL": {"type": "DECIMAL(64,16)"}, "ORACLE": {"type": "NUMBER(64,16)"}, "MapperXml": {"type": "DECIMAL"}, "JAVA_VO": {"type": "Double"}, "JAVA_DO": {"type": "Double"}, "JAVA_DTO": {"type": "Double"}, "Python": {"type": "double"}, "JavaApi": {"type": "Double"}, "JavaService": {"type": "Double"}, "MapperApi": {"type": "Double"}, "JavaServiceManager": {"type": "Double"}, "RequestQuery": {"type": "Double"}, "RequestBatchExacty": {"type": "Double"}}}, {"name": "DECIMAL(4,2)", "code": "<PERSON><PERSON>", "apply": {"MYSQL": {"type": "DECIMAL(4,2)"}, "ORACLE": {"type": "NUMBER(4,2)"}, "MapperXml": {"type": "DECIMAL"}, "JAVA_VO": {"type": "Double"}, "JAVA_DO": {"type": "Double"}, "JAVA_DTO": {"type": "Double"}, "Python": {"type": "double"}, "JavaApi": {"type": "Double"}, "JavaService": {"type": "Double"}, "MapperApi": {"type": "Double"}, "JavaServiceManager": {"type": "Double"}, "RequestQuery": {"type": "Double"}, "RequestBatchExacty": {"type": "Double"}}}, {"name": "DECIMAL(32,8)", "code": "Money", "apply": {"MYSQL": {"type": "DECIMAL(32,8)"}, "ORACLE": {"type": "NUMBER(32,8)"}, "MapperXml": {"type": "DECIMAL"}, "JAVA_VO": {"type": "Double"}, "JAVA_DO": {"type": "Double"}, "JAVA_DTO": {"type": "Double"}, "Python": {"type": "double"}, "MapperApi": {"type": "Double"}, "JavaApi": {"type": "Double"}, "JavaService": {"type": "Double"}, "JavaServiceManager": {"type": "Double"}, "RequestQuery": {"type": "Double"}, "RequestBatchExacty": {"type": "Double"}}}, {"name": "Date", "code": "Date", "apply": {"MYSQL": {"type": "DATE"}, "ORACLE": {"type": "DATE"}, "MapperXml": {"type": "TIMESTAMP"}, "JAVA_VO": {"type": "String"}, "JAVA_DO": {"type": "String"}, "JAVA_DTO": {"type": "String"}, "Python": {"type": "str"}, "JavaService": {"type": "String"}, "JavaApi": {"type": "String"}, "MapperApi": {"type": "String"}, "JavaServiceManager": {"type": "String"}, "RequestQuery": {"type": "time"}, "RequestBatchExacty": {"type": "time"}}}, {"name": "DATETIME", "code": "DATETIME", "apply": {"MYSQL": {"type": "DATETIME"}, "MapperXml": {"type": "TIMESTAMP"}, "JAVA_VO": {"type": "String"}, "JAVA_DO": {"type": "String"}, "JAVA_DTO": {"type": "String"}, "Python": {"type": "str"}, "JavaApi": {"type": "String"}, "JavaService": {"type": "String"}, "MapperApi": {"type": "String"}, "JavaServiceManager": {"type": "String"}, "RequestQuery": {"type": "time"}, "RequestBatchExacty": {"type": "time"}}}, {"name": "TIMESTAMP", "code": "TIMESTAMP", "apply": {"MYSQL": {"type": "TIMESTAMP"}, "MapperXml": {"type": "TIMESTAMP"}, "JAVA_VO": {"type": "String"}, "JAVA_DO": {"type": "String"}, "JAVA_DTO": {"type": "String"}, "Python": {"type": "str"}, "JavaApi": {"type": "String"}, "JavaService": {"type": "String"}, "MapperApi": {"type": "String"}, "JavaServiceManager": {"type": "String"}, "RequestQuery": {"type": "time"}, "RequestBatchExacty": {"type": "time"}}}, {"apply": {"MYSQL": {"type": "bigint(20) unsigned"}, "ORACLE": {"type": "bigint unsigned"}, "MapperXml": {"type": "BIGINT"}, "JAVA_VO": {"type": "<PERSON>"}, "JAVA_DO": {"type": "<PERSON>"}, "JAVA_DTO": {"type": "<PERSON>"}, "Python": {"type": "int"}, "MapperApi": {"type": "<PERSON>"}, "JavaApi": {"type": "<PERSON>"}, "JavaService": {"type": "<PERSON>"}, "JavaServiceManager": {"type": "<PERSON>"}, "RequestQuery": {"type": "<PERSON>"}, "RequestBatchExacty": {"type": "<PERSON>"}}, "name": "bigint unsigned", "code": "bigint unsigned"}, {"name": "TINYINT", "code": "short int", "apply": {"MYSQL": {"type": "TINYINT"}, "ORACLE": {"type": "short int"}, "MapperXml": {"type": "INTEGER"}, "JAVA_VO": {"type": "Integer"}, "JAVA_DO": {"type": "Integer"}, "JAVA_DTO": {"type": "Integer"}, "Python": {"type": "int"}, "JavaApi": {"type": "Integer"}, "JavaService": {"type": "Integer"}, "MapperApi": {"type": "Integer"}, "JavaServiceManager": {"type": "Integer"}, "RequestQuery": {"type": "Integer"}, "RequestBatchExacty": {"type": "Integer"}}}, {"name": "TINYINT(1)", "code": "YesNo", "apply": {"MYSQL": {"type": "TINYINT(1)"}, "ORACLE": {"type": "NUMBER(1)"}, "MapperXml": {"type": "TINYINT"}, "JAVA_VO": {"type": "Integer"}, "JAVA_DO": {"type": "Integer"}, "JAVA_DTO": {"type": "Integer"}, "Python": {"type": "int"}, "MapperApi": {"type": "Integer"}, "JavaApi": {"type": "Integer"}, "JavaService": {"type": "Integer"}, "JavaServiceManager": {"type": "Integer"}, "RequestQuery": {"type": "Integer"}, "RequestBatchExacty": {"type": "Integer"}}}, {"name": "TINYINT(3)", "code": "TINYINT_3", "apply": {"MYSQL": {"type": "TINYINT(3)"}, "MapperXml": {"type": "TINYINT"}, "JAVA_VO": {"type": "Integer"}, "JAVA_DO": {"type": "Integer"}, "JAVA_DTO": {"type": "Integer"}, "Python": {"type": "int"}, "JavaApi": {"type": "Integer"}, "JavaService": {"type": "Integer"}, "MapperApi": {"type": "Integer"}, "JavaServiceManager": {"type": "Integer"}, "RequestQuery": {"type": "Integer"}, "RequestBatchExacty": {"type": "Integer"}}}, {"name": "Integer", "code": "Integer", "apply": {"MYSQL": {"type": "INT"}, "ORACLE": {"type": "INT"}, "MapperXml": {"type": "INTEGER"}, "JAVA_VO": {"type": "Integer"}, "JAVA_DO": {"type": "Integer"}, "JAVA_DTO": {"type": "Integer"}, "Python": {"type": "int"}, "JavaApi": {"type": "Integer"}, "JavaService": {"type": "Integer"}, "MapperApi": {"type": "Integer"}, "JavaServiceManager": {"type": "Integer"}, "RequestQuery": {"type": "Integer"}, "RequestBatchExacty": {"type": "Integer"}}}, {"code": "int(4)", "apply": {"MYSQL": {"type": "int(4)"}, "ORACLE": {"type": "int(4)"}, "MapperXml": {"type": "INTEGER"}, "JAVA_VO": {"type": "Integer"}, "JAVA_DO": {"type": "Integer"}, "JAVA_DTO": {"type": "Integer"}, "Python": {"type": "int"}, "MapperApi": {"type": "Integer"}, "JavaApi": {"type": "Integer"}, "JavaService": {"type": "Integer"}, "JavaServiceManager": {"type": "Integer"}, "RequestQuery": {"type": "Integer"}, "RequestBatchExacty": {"type": "Integer"}}, "name": "INT(4)"}, {"name": "BIT_1", "code": "BIT_1", "apply": {"MYSQL": {"type": "BIT(1)"}, "MapperXml": {"type": "BIT"}, "JAVA_VO": {"type": "Integer"}, "JAVA_DO": {"type": "Integer"}, "JAVA_DTO": {"type": "Integer"}, "Python": {"type": "int"}, "MapperApi": {"type": "Integer"}, "JavaApi": {"type": "Integer"}, "JavaService": {"type": "Integer"}, "JavaServiceManager": {"type": "Integer"}, "RequestQuery": {"type": "Integer"}, "RequestBatchExacty": {"type": "Integer"}}}], "database": [{"code": "MYSQL", "template": "DROP TABLE {{=it.entity.title}};\n$blankline\nCREATE TABLE {{=it.entity.title}}(\n{{ pkList = [] ; }}\n{{~it.entity.fields:field:index}}\n    {{? field.pk }}{{ pkList.push(field.name) }}{{?}}\n    {{=field.name}} {{=field.type}} {{= field.pk ? 'NOT NULL' : '' }} COMMENT '{{=it.func.join(field.chnname,field.remark,';')}}' {{= index < it.entity.fields.length-1 ? ',' : ( pkList.length>0 ? ',' :'' ) }}\n{{~}}\n{{? pkList.length >0 }}\n    PRIMARY KEY ({{~pkList:pkName:i}}{{= pkName }}{{= i<pkList.length-1 ? ',' : '' }}{{~}})\n{{?}}\n) COMMENT = '{{=it.func.join(it.entity.chnname,it.entity.remark,';') }}'", "fileShow": true, "defaultDatabase": true, "createTableTemplate": "CREATE TABLE {{=it.module.name}}.{{=it.entity.title}}(\n{{ pkList = [] ; }}\n{{~it.entity.fields:field:index}}\n    {{? field.pk }}{{ pkList.push(field.name) }}{{?}}\n    {{=field.name}} {{=field.type}} {{= field.notNull ? 'NOT NULL' : '' }} {{= field.autoIncrement ? 'AUTO_INCREMENT' : '' }} {{= field.defaultValue ? it.func.join('DEFAULT',field.defaultValue,' ') : '' }} COMMENT '{{=it.func.join(field.chnname,field.remark,',')}}' {{= index < it.entity.fields.length-1 ? ',' : ( pkList.length>0 ? ',' :'' ) }}\n{{~}}\n{{? it.entity.indexs }}\n{{~it.entity.indexs:indexob:index }}\n    {{? indexob.isUnique}}UNIQUE{{??}}INDEX{{?}} {{=indexob.name}}({{=it.func.join(...indexob.fields,',')}}),\n{{~}}\n{{?}}\n{{? pkList.length >0 }}\n    PRIMARY KEY ({{~pkList:pkName:i}}{{= pkName }}{{= i<pkList.length-1 ? ',' : '' }}{{~}})\n{{?}}\n) ENGINE=InnoDB  DEFAULT CHARSET=utf8mb4 COMMENT = '{{=it.func.join(it.entity.chnname,it.entity.remark,' ') }}';\n\n", "deleteTableTemplate": "create schema if not exists {{=it.module.name}}  default character set utf8mb4 collate utf8mb4_unicode_ci;\nDROP TABLE IF EXISTS {{=it.module.name}}.{{=it.entity.title}};\n", "rebuildTableTemplate": "create table PDMAN_UP_{{=it.oldEntity.title}}\nas select * from {{=it.oldEntity.title}};{{=it.separator}}\n$blankline\n\ndrop table {{=it.oldEntity.title}};{{=it.separator}}\n$blankline\n\nCREATE TABLE {{=it.newEntity.title}}(\n{{ pkList = [] ; }}\n{{~it.newEntity.fields:field:index}}\n    {{? field.pk }}{{ pkList.push(field.name) }}{{?}}\n    {{=field.name}} {{=field.type}} {{= field.notNull ? 'NOT NULL' : '' }} COMMENT '{{=it.func.join(field.chnname,field.remark,';')}}' {{= index < it.newEntity.fields.length-1 ? ',' : ( pkList.length>0 ? ',' :'' ) }}\n{{~}}\n{{? pkList.length >0 }}\n    PRIMARY KEY ({{~pkList:pkName:i}}{{= pkName }}{{= i<pkList.length-1 ? ',' : '' }}{{~}})\n{{?}}\n) COMMENT = '{{=it.func.join(it.newEntity.chnname,it.newEntity.remark,';') }}';{{=it.separator}}\n$blankline\n\n{{ sameCols = it.func.intersect(it.newEntity.fields,it.newEntity.fields) ;}}\ninsert into {{=it.newEntity.title}}(\n{{~sameCols:field:index}}\n    {{=field.name}}{{? index<it.newEntity.fields.length-1}},{{?}}\n{{~}}\n) \nvalues\nselect \n{{~sameCols:field:index}}\n    {{=field.name}}{{? index<it.oldEntity.fields.length-1}},{{?}}\n{{~}}\nfrom PDMAN_UP_{{=it.oldEntity.title}};{{=it.separator}}\n$blankline\n\ndrop table PDMAN_UP_{{=it.oldEntity.title}};{{=it.separator}}", "createFieldTemplate": "ALTER TABLE {{=it.entity.title}} ADD COLUMN {{=it.field.name}} {{=it.field.type}} {{? it.field.notNull}}NOT NULL{{?}} {{? ''!=it.field.defaultValue}}DEFAULT {{? null==it.field.defaultValue}}NULL{{??}}'{{=it.field.defaultValue}}'{{?}}{{?}} {{? it.field.autoIncrement}}AUTO_INCREMENT{{?}} {{? it.field.pk}}PRIMARY KEY{{?}} {{? it.field.chnname}}COMMENT '{{=it.field.chnname}}'{{?}} {{? it.field.addAfter}}AFTER {{=it.field.addAfter}}{{?}};", "updateFieldTemplate": "ALTER TABLE {{=it.entity.title}} MODIFY COLUMN {{=it.field.name}} {{=it.field.type}} {{? it.field.notNull}}NOT NULL{{?}} {{? ''!=it.field.defaultValue}}DEFAULT {{? null==it.field.defaultValue}}NULL{{??}}'{{=it.field.defaultValue}}'{{?}}{{?}} {{? it.field.autoIncrement}}AUTO_INCREMENT{{?}} {{? it.field.chnname}}COMMENT '{{=it.field.chnname}}'{{?}};", "deleteFieldTemplate": "ALTER TABLE {{=it.entity.title}} DROP {{=it.field.name}};", "deleteIndexTemplate": "ALTER TABLE {{=it.entity.title}} DROP INDEX {{=it.index.name}};", "createIndexTemplate": "ALTER TABLE {{=it.module.name}}.{{=it.entity.title}} ADD {{? it.index.isUnique}}UNIQUE{{??}}INDEX{{?}} {{=it.index.name}}({{=it.func.join(...it.index.fields,',')}});", "updateTableComment": "ALTER TABLE {{=it.module.name}}.{{=it.entity.title}} COMMENT '{{=it.entity.chnname}}';"}, {"code": "ORACLE", "template": "DROP TABLE {{=it.entity.title}};\n$blankline\nCREATE TABLE {{=it.entity.title}}(\n{{ pkList = [] ; }}\n{{~it.entity.fields:field:index}}\n    {{? field.pk }}{{ pkList.push(field.name) }}{{?}}\n    {{=field.name}} {{=field.type}}  {{= field.pk ? 'NOT NULL' : '' }} {{= index < it.entity.fields.length-1 ? ',' : ( pkList.length>0 ? ',' :'' ) }}\n{{~}}\n{{? pkList.length >0 }}\n    PRIMARY KEY ({{~pkList:pkName:i}}{{= pkName }}{{= i<pkList.length-1 ? ',' : '' }}{{~}})\n{{?}}\n);\n$blankline\nCOMMENT ON TABLE {{=it.entity.title}} IS '{{=it.func.join(it.entity.chnname,it.entity.remark,';') }}';\n{{~it.entity.fields:field:index}}\nCOMMENT ON COLUMN {{=it.entity.title}}.{{=field.name}} IS '{{=it.func.join(field.chnname,field.remark,';')}}';\n{{~}}", "createTableTemplate": "CREATE TABLE {{=it.entity.title}}(\n{{ pkList = [] ; }}\n{{~it.entity.fields:field:index}}\n    {{? field.pk }}{{ pkList.push(field.name) }}{{?}}\n    {{=field.name}} {{=field.type}}{{? field.defaultValue}} DEFAULT {{=field.defaultValue}}{{?}}{{= field.notNull ? ' NOT NULL' : '' }}{{= index < it.entity.fields.length-1 ? ',' : ( pkList.length>0 ? ',' :'' ) }}\n{{~}}\n{{? pkList.length >0 }}\n    PRIMARY KEY ({{~pkList:pkName:i}}{{= pkName }}{{= i<pkList.length-1 ? ',' : '' }}{{~}})\n{{?}}\n);\n$blankline\n{{? it.entity.chnname || it.entity.remark}}COMMENT ON TABLE {{=it.entity.title}} IS {{? it.entity.remark}}'{{=it.entity.remark}}'{{??}}'{{=it.entity.chnname}}'{{?}};{{?}}\n{{~it.entity.fields:field:index}}\n{{? field.chnname || field.remark}}COMMENT ON COLUMN {{=it.entity.title}}.{{=field.name}} IS {{? field.remark}}'{{=field.remark}}'{{??}}'{{=field.chnname}}'{{?}};{{?}}\n{{~}}", "deleteTableTemplate": "DROP TABLE {{=it.entity.title}};/*SkipError*/\r\n$blankline", "rebuildTableTemplate": "CREATE TABLE PDMAN_UP_{{=it.oldEntity.title}}\nAS SELECT * FROM {{=it.oldEntity.title}};{{=it.separator}}\n$blankline\nDROP TABLE {{=it.oldEntity.title}};{{=it.separator}}\n$blankline\nCREATE TABLE {{=it.newEntity.title}}(\n{{ pkList = [] ; }}{{~it.newEntity.fields:field:index}}{{? field.pk }}{{ pkList.push(field.name) }}{{?}}    {{=field.name}} {{=field.type}}{{? field.defaultValue}} DEFAULT {{=field.defaultValue}}{{?}}{{= field.notNull ? ' NOT NULL' : '' }}{{= index < it.newEntity.fields.length-1 ? ',' : ( pkList.length>0 ? ',' :'' ) }}\n{{~}}{{? pkList.length >0 }}    PRIMARY KEY ({{~pkList:pkName:i}}{{= pkName }}{{= i<pkList.length-1 ? ',' : '' }}{{~}}){{?}}\n);{{=it.separator}}\n$blankline\n{{? it.newEntity.chnname || it.newEntity.remark}}COMMENT ON TABLE {{=it.newEntity.title}} IS {{? it.newEntity.remark}}'{{=it.entity.remark}}'{{??}}'{{=it.newEntity.chnname}}'{{?}};{{?}}{{=it.separator}}\n{{~it.newEntity.fields:field:index}}\n{{? field.chnname || field.remark}}COMMENT ON COLUMN {{=it.newEntity.title}}.{{=field.name}} IS {{? field.remark}}'{{=field.remark}}'{{??}}'{{=field.chnname}}'{{?}};{{?}}{{=it.separator}}\n{{~}}\n{{ sameCols = it.func.intersect(it.newEntity.fields,it.newEntity.fields) ;}}\n$blankline\nINSERT INTO {{=it.newEntity.title}}(\n{{~sameCols:field:index}}   {{=field.name}}{{? index<it.newEntity.fields.length-1}},{{?}}\n{{~}}) \nVALUES\nSELECT\n{{~sameCols:field:index}}   {{=field.name}}{{? index<it.oldEntity.fields.length-1}},{{?}}\n{{~}}FROM PDMAN_UP_{{=it.oldEntity.title}};{{=it.separator}}\n\nDROP TABLE PDMAN_UP_{{=it.oldEntity.title}};{{=it.separator}}", "createFieldTemplate": "ALTER TABLE {{=it.entity.title}} ADD({{=it.field.name}} {{=it.field.type}}{{? it.field.defaultValue}} DEFAULT {{=it.field.defaultValue}}{{?}}{{? it.field.notNull}} NOT NULL{{?}});\r\n{{? it.field.chnname || it.field.remark}}COMMENT ON COLUMN {{=it.entity.title}}.{{=it.field.name}} IS {{? it.field.remark}}'{{=it.field.remark}}'{{??}}'{{=it.field.chnname}}'{{?}};{{?}}\r\n$blankline", "updateFieldTemplate": "ALTER TABLE {{=it.entity.title}} MODIFY({{=it.field.name}} {{=it.field.type}}{{? it.field.defaultValue}} DEFAULT {{=it.field.defaultValue}}{{?}}{{? it.field.notNull}} NOT NULL{{?}});\r\n{{? it.field.chnname || it.field.remark}}COMMENT ON COLUMN {{=it.entity.title}}.{{=it.field.name}} IS {{? it.field.remark}}'{{=it.field.remark}}'{{??}}'{{=it.field.chnname}}'{{?}};{{?}}\r\n$blankline", "deleteFieldTemplate": "ALTER TABLE {{=it.entity.title}} DROP({{=it.field.name}});\r\n$blankline", "deleteIndexTemplate": "DROP INDEX {{=it.entity.title}}.{{=it.index.name}};\r\n$blankline", "createIndexTemplate": "CREATE{{? it.index.isUnique}} UNIQUE{{?}} INDEX {{=it.index.name}} ON {{=it.entity.title}}({{=it.func.join(it.index.fields,',')}});\r\n$blankline", "updateTableComment": "{{? it.entity.chnname || it.entity.remark}}COMMENT ON TABLE {{=it.entity.title}} IS {{? it.entity.remark}}'{{=it.entity.remark}}'{{??}}'{{=it.entity.chnname}}'{{?}};{{?}}\r\n$blankline"}, {"code": "JAVA_VO", "template": "package group.rober.pdman.{{=it.module.name}}.entity;\n$blankline\nimport javax.persistence.*;\nimport java.io.Serializable;\nimport java.util.Date;\n$blankline\n$blankline\n\n/** {{=it.entity.chnname}} */\n@Table(name=\"{{=it.entity.title}}\")\npublic class {{=it.func.camel(it.entity.title,true) }} implements Serializable,Cloneable{\n{{~it.entity.fields:field:index}}\n    /** {{=it.func.join(field.chnname,field.remark,';')}} */\n    {{? field.pk }}\n    @Id\n    @GeneratedValue\n    {{?}}\n    private {{=field.type}} {{=it.func.camel(field.name,false)}} ;\n{{~}}\n$blankline\n{{~it.entity.fields:field:index}}\n    /** {{=it.func.join(field.chnname,field.remark,';')}} */\n    public {{=field.type}} get{{=it.func.camel(field.name,true)}}(){\n        return this.{{=it.func.camel(field.name,false)}};\n    }\n    /** {{=it.func.join(field.chnname,field.remark,';')}} */\n    public void set{{=it.func.camel(field.name,true)}}({{=field.type}} {{= it.func.camel(field.name,false) }}){\n        this.{{=it.func.camel(field.name,false)}} = {{= it.func.camel(field.name,false) }};\n    }\n{{~}}\n}", "createTableTemplate": "{{ package_name =\"tech.tenyears.user.facade\"; }}{{dir_path ='{{=package_name }}.vo.{{=it.module.name }}';}}code_path=|{{=dir_path }}|package {{=dir_path }};\n$blankline\nimport com.fasterxml.jackson.annotation.JsonProperty;\nimport io.swagger.annotations.ApiModel;\nimport io.swagger.annotations.ApiModelProperty;\nimport lombok.*;\n$blankline\n/**\n * <AUTHOR> process.env.USERNAME || \"\"}}\n * @date {{= new Date().toJSON().slice(0, 19).replace('T',' ')}}\n */\n@Data\n@EqualsAndHashCode(callSuper = false)\n@AllArgsConstructor\n@NoArgsConstructor\n@Builder\n@ApiModel(value = \"{{=it.func.camel(it.entity.title,true) }}VO\", description = \"{{=it.func.join(it.entity.chnname,'',' ')}}{{? it.entity.remark }}{{=it.func.join(';',it.entity.remark,'',' ')}}{{?}}\")\npublic class {{=it.func.camel(it.entity.title,true) }}VO {\n    {{~it.entity.fields:field:index}}\n$blankline\n    /**\n    * {{=it.func.join(field.chnname,field.remark,';')}}\n    */\n  //  @ApiModelProperty(value = \"{{=it.func.join(field.chnname,field.remark,';')}}\")\n    @JsonProperty(\"{{=field.name}}\")\n    private {{=field.type}} {{=it.func.camel(field.name,false)}} ;\n    {{~}}\n$blankline\n    \n    /**\n     * 自动trim\n     */\n    public void autoTrim(){\n        {{~it.entity.fields:field:index}}{{? field.type==\"String\" }}\n        if (this.{{=it.func.camel(field.name,false)}} != null){\n            this.{{=it.func.camel(field.name,false)}} = this.{{=it.func.camel(field.name,false)}}.trim();\n        }{{?}}{{~}}\n    }\n}", "deleteTableTemplate": "", "rebuildTableTemplate": "", "createFieldTemplate": "", "updateFieldTemplate": "", "deleteFieldTemplate": "", "deleteIndexTemplate": "", "createIndexTemplate": "", "updateTableComment": ""}, {"code": "MapperXml", "createTableTemplate": "{{ package_name =\"com.yaduo.demo.service\"; }}code_path=|mapper|{{ package_do ='{{=package_name }}.domain.{{=it.func.camel(it.entity.title,true) }}DO'; }}{{ package_mapper = '{{=package_name }}.mapper.{{=it.func.camel(it.entity.title,true) }}Mapper';}}{{ package_request = '{{=package_name }}.api.dto.request.{{=it.func.camel(it.entity.title,true) }}Request';}}{{ package_multi_request = '{{=package_name }}.api.dto.request.{{=it.func.camel(it.entity.title,true) }}MultiRequest';}}<?xml version=\"1.0\" encoding=\"UTF-8\" ?>\n<!DOCTYPE mapper PUBLIC \"-//mybatis.org//DTD Mapper 3.0//EN\" \"http://mybatis.org/dtd/mybatis-3-mapper.dtd\">\n<mapper namespace=\"{{=package_mapper }}\">\n    <resultMap type=\"{{=package_do }}\" id=\"BaseResultMap\">{{~it.entity.fields:field:index}}\n        <{{? field.pk }}id{{??}}result{{?}} column=\"{{=field.name}}\" property=\"{{=it.func.camel(field.name,false)}}\" jdbcType=\"{{=field.type}}\"/>{{~}}\n    </resultMap>\n    <sql id=\"Base_Column_List\">\n    {{~it.entity.fields:field:index}}    `{{=field.name}}`{{?index!=(it.entity.fields.length-1)}},{{?}}\n    {{~}}</sql>\n$blankline\n    <update id=\"deletePhysically\">\n         <if test=\"ids != null and ids.size > 0\">\n        delete from\n        {{=it.entity.title}} \n        <if test=\"ids != null and ids.size > 0\">\n            where {{~it.entity.fields:field:index}}{{? field.pk }}{{=field.name}}\n        <include refid=\"inIds\"/>{{?}} {{~}}\n        </if>\n        </if>\n    </update>\n$blankline\n    <select id=\"queryPageData\" resultMap=\"BaseResultMap\"   parameterType=\"{{=package_request }}\" >\n        select  <include refid=\"Base_Column_List\"/> from {{=it.entity.title}}\n        <if test=\"request_model != null \">\n            where 1=1  {{~it.entity.fields:field:index}} \n                {{? field.type.indexOf(\"CHAR\")>-1}}<if test=\"request_model.{{=it.func.camel(field.name,false)}} != null and request_model.{{=it.func.camel(field.name,false)}} != ''\">\n                    and {{=field.name}} like concat('%',#{request_model.{{=it.func.camel(field.name,false)}},jdbcType=VARCHAR},'%')\n                </if>\n                {{?? field.type.indexOf(\"TIMESTAMP\")>-1}}<if test=\"request_model.{{=it.func.camel(field.name,false)}}Start != null and request_model.{{=it.func.camel(field.name,false)}}Start != ''\">\n                    and {{=field.name}} &gt;= #{request_model.{{=it.func.camel(field.name,false)}}Start,jdbcType={{=field.type}}}\n                </if>\n                <if test=\"request_model.{{=it.func.camel(field.name,false)}}End != null and request_model.{{=it.func.camel(field.name,false)}}End != ''\">\n                    and {{=field.name}} &lt;= #{request_model.{{=it.func.camel(field.name,false)}}End,jdbcType={{=field.type}}}\n                </if>\n                {{?? field.type.indexOf(\"INT\") >-1 }}<if test=\"request_model.{{=it.func.camel(field.name,false)}} != null\">\n                    and {{=field.name}} = #{request_model.{{=it.func.camel(field.name,false)}},jdbcType={{=field.type}}}\n                </if>{{??}}\n                <if test=\"request_model.{{=it.func.camel(field.name,false)}} != null and request_model.{{=it.func.camel(field.name,false)}} != ''\"> \n                    and {{=field.name}} = #{request_model.{{=it.func.camel(field.name,false)}},jdbcType={{=field.type}}}\n                </if>{{?}}{{~}}\n        </if>\n        <if test=\"request_model.sort != null and  request_model.sort.size()>0\">\n            order by\n            <foreach collection=\"request_model.sort\" item=\"model\" separator=\",\">\n                ${model.property} ${model.direction}\n            </foreach>\n        </if>\n        limit #{offset,jdbcType=INTEGER},#{request_model.size,jdbcType=INTEGER}\n    </select>\n$blankline\n    <select id=\"queryTotalCount\" resultType=\"int\" parameterType=\"{{=package_request }}\">\n        select  count(*) as total from {{=it.entity.title}}\n        <if test=\"request_model != null \">\n            where 1=1  {{~it.entity.fields:field:index}} \n                {{? field.type.indexOf(\"CHAR\")>-1}}<if test=\"request_model.{{=it.func.camel(field.name,false)}} != null and request_model.{{=it.func.camel(field.name,false)}} != ''\">\n                    and {{=field.name}} like concat('%',#{request_model.{{=it.func.camel(field.name,false)}},jdbcType=VARCHAR},'%')\n                </if>\n                {{?? field.type.indexOf(\"TIMESTAMP\")>-1}}<if test=\"request_model.{{=it.func.camel(field.name,false)}}Start != null and request_model.{{=it.func.camel(field.name,false)}}Start != ''\">\n                    and {{=field.name}} &gt;= #{request_model.{{=it.func.camel(field.name,false)}}Start,jdbcType={{=field.type}}}\n                </if>\n                <if test=\"request_model.{{=it.func.camel(field.name,false)}}End != null and request_model.{{=it.func.camel(field.name,false)}}End != ''\">\n                    and {{=field.name}} &lt;= #{request_model.{{=it.func.camel(field.name,false)}}End,jdbcType={{=field.type}}}\n                </if>\n                {{?? field.type.indexOf(\"INT\") >-1 }}<if test=\"request_model.{{=it.func.camel(field.name,false)}} != null\">\n                    and {{=field.name}} = #{request_model.{{=it.func.camel(field.name,false)}},jdbcType={{=field.type}}}\n                </if>{{??}}\n                <if test=\"request_model.{{=it.func.camel(field.name,false)}} != null and request_model.{{=it.func.camel(field.name,false)}} != ''\"> \n                    and {{=field.name}} = #{request_model.{{=it.func.camel(field.name,false)}},jdbcType={{=field.type}}}\n                </if> {{?}}{{~}}\n        </if>\n    </select>\n$blankline\n    <select id=\"getExactData\" resultMap=\"BaseResultMap\"   parameterType=\"{{=package_multi_request }}\" >\n        select  <include refid=\"Base_Column_List\"/> from {{=it.entity.title}}\n        <if test=\"request_model != null \">\n            where 1=1  {{~it.entity.fields:field:index}} \n               {{? field.type.indexOf(\"CHAR\")>-1}}<if test=\"request_model.{{=it.func.camel(field.name,false)}} != null and request_model.{{=it.func.camel(field.name,false)}}.size > 0\">\n                    AND  {{=field.name}} in\n                    <foreach collection=\"request_model.{{=it.func.camel(field.name,false)}}\" item=\"temp\" index=\"index\" open=\"(\" separator=\",\" close=\")\">#{temp}\n                    </foreach>\n                </if>\n                {{?? field.type.indexOf(\"TIMESTAMP\")>-1}}<if test=\"request_model.{{=it.func.camel(field.name,false)}}Start != null and request_model.{{=it.func.camel(field.name,false)}}Start != ''\">\n                    and {{=field.name}} &gt;= #{request_model.{{=it.func.camel(field.name,false)}}Start,jdbcType={{=field.type}}}\n                </if>\n                <if test=\"request_model.{{=it.func.camel(field.name,false)}}End != null and request_model.{{=it.func.camel(field.name,false)}}End != ''\">\n                    and {{=field.name}} &lt;= #{request_model.{{=it.func.camel(field.name,false)}}End,jdbcType={{=field.type}}}\n                </if>\n                {{?? field.type.indexOf(\"INT\") >-1 }}\n                <if test=\"request_model.{{=it.func.camel(field.name,false)}} != null and request_model.{{=it.func.camel(field.name,false)}}.size > 0\">\n                AND  {{=field.name}} in\n                    <foreach collection=\"request_model.{{=it.func.camel(field.name,false)}}\" item=\"temp\" index=\"index\" open=\"(\" separator=\",\" close=\")\">#{temp}\n                    </foreach>\n                </if>{{??}}\n                <if test=\"request_model.{{=it.func.camel(field.name,false)}} != null and request_model.{{=it.func.camel(field.name,false)}} != ''\"> \n                    and {{=field.name}} = #{request_model.{{=it.func.camel(field.name,false)}},jdbcType={{=field.type}}}\n                </if> {{?}}{{~}}\n        </if>\n        <if test=\"request_model.sort != null and  request_model.sort.size()>0\">\n            order by\n            <foreach collection=\"request_model.sort\" item=\"model\" separator=\",\">\n                ${model.property} ${model.direction}\n            </foreach>\n        </if>\n        limit #{offset,jdbcType=INTEGER},#{request_model.size,jdbcType=INTEGER}\n    </select>\n$blankline\n    <select id=\"getExactTotalCount\" resultType=\"int\" parameterType=\"{{=package_multi_request }}\">\n        <if test=\"request_model != null \">\n            select  count(*) as total from {{=it.entity.title}}\n            where 1=1  {{~it.entity.fields:field:index}} \n                {{? field.type.indexOf(\"CHAR\")>-1}}<if test=\"request_model.{{=it.func.camel(field.name,false)}} != null and request_model.{{=it.func.camel(field.name,false)}}.size > 0\">\n                    AND  {{=field.name}} in\n                    <foreach collection=\"request_model.{{=it.func.camel(field.name,false)}}\" item=\"temp\" index=\"index\" open=\"(\" separator=\",\" close=\")\">#{temp}\n                    </foreach>\n                </if>\n                {{?? field.type.indexOf(\"TIMESTAMP\")>-1}}<if test=\"request_model.{{=it.func.camel(field.name,false)}}Start != null and request_model.{{=it.func.camel(field.name,false)}}Start != ''\">\n                    and {{=field.name}} &gt;= #{request_model.{{=it.func.camel(field.name,false)}}Start,jdbcType={{=field.type}}}\n                </if>\n                <if test=\"request_model.{{=it.func.camel(field.name,false)}}End != null and request_model.{{=it.func.camel(field.name,false)}}End != ''\">\n                    and {{=field.name}} &lt;= #{request_model.{{=it.func.camel(field.name,false)}}End,jdbcType={{=field.type}}}\n                </if>\n                {{?? field.type.indexOf(\"INT\") >-1 }}\n                <if test=\"request_model.{{=it.func.camel(field.name,false)}} != null and request_model.{{=it.func.camel(field.name,false)}}.size > 0\">\n                AND  {{=field.name}} in\n                    <foreach collection=\"request_model.{{=it.func.camel(field.name,false)}}\" item=\"temp\" index=\"index\" open=\"(\" separator=\",\" close=\")\">#{temp}\n                    </foreach>\n                </if>{{??}}\n                <if test=\"request_model.{{=it.func.camel(field.name,false)}} != null and request_model.{{=it.func.camel(field.name,false)}} != ''\"> \n                    and {{=field.name}} = #{request_model.{{=it.func.camel(field.name,false)}},jdbcType={{=field.type}}}\n                </if> {{?}}{{~}}\n        </if>\n    </select>\n$blankline\n     <select id=\"getCountGroupByField\" resultType=\"map\" parameterType=\"{{=package_multi_request }}\">\n        <if test=\"request_model != null \">\n        select count(*) as total ,\n        <trim prefix=\"\" suffixOverrides=\",\">{{~it.entity.fields:field:index}} \n            {{? field.type.indexOf(\"TIMESTAMP\")>-1}}<if test=\"(request_model.{{=it.func.camel(field.name,false)}}Start != null and request_model.{{=it.func.camel(field.name,false)}}Start != '') or (request_model.{{=it.func.camel(field.name,false)}}End != null and request_model.{{=it.func.camel(field.name,false)}}End != '')\">\n                {{=field.name}},\n            </if>{{?? field.type.indexOf(\"CHAR\")>-1}}\n            <if test=\"request_model.{{=it.func.camel(field.name,false)}} != null and request_model.{{=it.func.camel(field.name,false)}}.size > 0\">\n                {{=field.name}},\n            </if>{{?? field.type.indexOf(\"INT\") >-1 }}\n            <if test=\"request_model.{{=it.func.camel(field.name,false)}} != null and request_model.{{=it.func.camel(field.name,false)}}.size > 0\">\n                {{=field.name}},\n            </if>{{??}}\n            <if test=\"request_model.{{=it.func.camel(field.name,false)}} != null and request_model.{{=it.func.camel(field.name,false)}} != ''\"> \n                {{=field.name}},\n            </if> {{?}}{{~}}\n        </trim>\n        from {{=it.entity.title}}\n        \n            <trim prefix=\"where\" suffixOverrides=\"and\">\n            {{~it.entity.fields:field:index}}{{? field.type.indexOf(\"CHAR\")>-1}}\n                <if test=\"request_model.{{=it.func.camel(field.name,false)}} != null and request_model.{{=it.func.camel(field.name,false)}}.size > 0\">\n                    {{=field.name}} in\n                    <foreach collection=\"request_model.{{=it.func.camel(field.name,false)}}\" item=\"temp\" index=\"index\" open=\"(\" separator=\",\" close=\")\">#{temp}</foreach> AND\n                </if>{{?? field.type.indexOf(\"TIMESTAMP\")>-1}}<if test=\"request_model.{{=it.func.camel(field.name,false)}}Start != null and request_model.{{=it.func.camel(field.name,false)}}Start != ''\">\n                    {{=field.name}} &gt;= #{request_model.{{=it.func.camel(field.name,false)}}Start,jdbcType={{=field.type}}} AND\n                </if>\n                <if test=\"request_model.{{=it.func.camel(field.name,false)}}End != null and request_model.{{=it.func.camel(field.name,false)}}End != ''\">\n                    {{=field.name}} &lt;= #{request_model.{{=it.func.camel(field.name,false)}}End,jdbcType={{=field.type}}} AND\n                </if>{{?? field.type.indexOf(\"INT\") >-1 }}\n                <if test=\"request_model.{{=it.func.camel(field.name,false)}} != null and request_model.{{=it.func.camel(field.name,false)}}.size > 0\">\n                    {{=field.name}} in\n                    <foreach collection=\"request_model.{{=it.func.camel(field.name,false)}}\" item=\"temp\" index=\"index\" open=\"(\" separator=\",\" close=\")\">#{temp}</foreach> AND\n                </if>{{??}}<if test=\"request_model.{{=it.func.camel(field.name,false)}} != null and request_model.{{=it.func.camel(field.name,false)}} != ''\"> \n                    {{=field.name}} = #{request_model.{{=it.func.camel(field.name,false)}},jdbcType={{=field.type}}} AND\n                </if> {{?}}{{~}}\n            </trim>\n            <trim prefix=\"group by\" suffixOverrides=\",\">{{~it.entity.fields:field:index}} \n                {{? field.type.indexOf(\"TIMESTAMP\")>-1}}<if test=\"(request_model.{{=it.func.camel(field.name,false)}}Start != null and request_model.{{=it.func.camel(field.name,false)}}Start != '' or request_model.{{=it.func.camel(field.name,false)}}End != null and request_model.{{=it.func.camel(field.name,false)}}End != '')\">\n                    {{=field.name}},\n                </if>{{?? field.type.indexOf(\"CHAR\")>-1}}\n                <if test=\"request_model.{{=it.func.camel(field.name,false)}} != null and request_model.{{=it.func.camel(field.name,false)}}.size > 0\">\n                    {{=field.name}},\n                </if>{{?? field.type.indexOf(\"INT\") >-1 }}\n                <if test=\"request_model.{{=it.func.camel(field.name,false)}} != null and request_model.{{=it.func.camel(field.name,false)}}.size > 0\">\n                    {{=field.name}},\n                </if>{{??}}\n                <if test=\"request_model.{{=it.func.camel(field.name,false)}} != null and request_model.{{=it.func.camel(field.name,false)}} != ''\"> \n                    {{=field.name}},\n                </if> {{?}}{{~}}\n            </trim>\n        </if>\n    </select>\n$blankline\n     <select id=\"selectDistinctByConditionModel\" resultType=\"map\" parameterType=\"{{=package_multi_request }}\">\n        <if test=\"condition_model != null \">\n        select \n        <trim prefix=\"distinct\" suffixOverrides=\",\">{{~it.entity.fields:field:index}} \n            {{? field.type.indexOf(\"CHAR\")>-1}}\n            <if test=\"request_model.{{=it.func.camel(field.name,false)}} != null and request_model.{{=it.func.camel(field.name,false)}} != ''\">\n                {{=field.name}} as {{=it.func.camel(field.name,false)}},\n            </if>{{?? field.type.indexOf(\"INT\") >-1 }}\n            <if test=\"request_model.{{=it.func.camel(field.name,false)}} != null and request_model.{{=it.func.camel(field.name,false)}} >= 0\">\n                {{=field.name}} as {{=it.func.camel(field.name,false)}},\n            </if>{{??}}\n            <if test=\"request_model.{{=it.func.camel(field.name,false)}} != null and request_model.{{=it.func.camel(field.name,false)}} != ''\"> \n                {{=field.name}} as {{=it.func.camel(field.name,false)}},\n            </if> {{?}}{{~}}\n        </trim>\n        from {{=it.entity.title}}\n            <trim prefix=\"where\" suffixOverrides=\"and\">\n            {{~it.entity.fields:field:index}}{{? field.type.indexOf(\"CHAR\")>-1}}\n                <if test=\"condition_model.{{=it.func.camel(field.name,false)}} != null and condition_model.{{=it.func.camel(field.name,false)}}.size > 0\">\n                    {{=field.name}} in\n                    <foreach collection=\"condition_model.{{=it.func.camel(field.name,false)}}\" item=\"temp\" index=\"index\" open=\"(\" separator=\",\" close=\")\">#{temp}</foreach> AND\n                </if>{{?? field.type.indexOf(\"TIMESTAMP\")>-1}}<if test=\"condition_model.{{=it.func.camel(field.name,false)}}Start != null and condition_model.{{=it.func.camel(field.name,false)}}Start != ''\">\n                    {{=field.name}} &gt;= #{condition_model.{{=it.func.camel(field.name,false)}}Start,jdbcType={{=field.type}}} AND\n                </if>\n                <if test=\"condition_model.{{=it.func.camel(field.name,false)}}End != null and condition_model.{{=it.func.camel(field.name,false)}}End != ''\">\n                    {{=field.name}} &lt;= #{condition_model.{{=it.func.camel(field.name,false)}}End,jdbcType={{=field.type}}} AND\n                </if>{{?? field.type.indexOf(\"INT\") >-1 }}\n                <if test=\"condition_model.{{=it.func.camel(field.name,false)}} != null and condition_model.{{=it.func.camel(field.name,false)}}.size > 0\">\n                    {{=field.name}} in\n                    <foreach collection=\"condition_model.{{=it.func.camel(field.name,false)}}\" item=\"temp\" index=\"index\" open=\"(\" separator=\",\" close=\")\">#{temp}</foreach> AND\n                </if>{{??}}<if test=\"condition_model.{{=it.func.camel(field.name,false)}} != null and condition_model.{{=it.func.camel(field.name,false)}} != ''\"> \n                    {{=field.name}} = #{condition_model.{{=it.func.camel(field.name,false)}},jdbcType={{=field.type}}} AND\n                </if> {{?}}{{~}}\n            </trim>\n        limit #{offset,jdbcType=INTEGER},#{condition_model.size,jdbcType=INTEGER}\n        </if>\n    </select>\n$blankline\n     <select id=\"selectDistinctCountByConditionModel\" resultType=\"int\" parameterType=\"{{=package_multi_request }}\">\n        <if test=\"condition_model != null \">\n        select count(\n            <trim prefix=\"distinct\" suffixOverrides=\",\">{{~it.entity.fields:field:index}} \n                {{? field.type.indexOf(\"CHAR\")>-1}}\n                <if test=\"request_model.{{=it.func.camel(field.name,false)}} != null and request_model.{{=it.func.camel(field.name,false)}} != ''\">\n                    {{=field.name}},\n                </if>{{?? field.type.indexOf(\"INT\") >-1 }}\n                <if test=\"request_model.{{=it.func.camel(field.name,false)}} != null and request_model.{{=it.func.camel(field.name,false)}} >= 0\">\n                    {{=field.name}},\n                </if>{{??}}\n                <if test=\"request_model.{{=it.func.camel(field.name,false)}} != null and request_model.{{=it.func.camel(field.name,false)}} != ''\"> \n                    {{=field.name}},\n                </if> {{?}}{{~}}\n            </trim>\n        )\n        from {{=it.entity.title}}\n            <trim prefix=\"where\" suffixOverrides=\"and\">\n            {{~it.entity.fields:field:index}}{{? field.type.indexOf(\"CHAR\")>-1}}\n                <if test=\"condition_model.{{=it.func.camel(field.name,false)}} != null and condition_model.{{=it.func.camel(field.name,false)}}.size > 0\">\n                    {{=field.name}} in\n                    <foreach collection=\"condition_model.{{=it.func.camel(field.name,false)}}\" item=\"temp\" index=\"index\" open=\"(\" separator=\",\" close=\")\">#{temp}</foreach> AND\n                </if>{{?? field.type.indexOf(\"TIMESTAMP\")>-1}}<if test=\"condition_model.{{=it.func.camel(field.name,false)}}Start != null and condition_model.{{=it.func.camel(field.name,false)}}Start != ''\">\n                    {{=field.name}} &gt;= #{condition_model.{{=it.func.camel(field.name,false)}}Start,jdbcType={{=field.type}}} AND\n                </if>\n                <if test=\"condition_model.{{=it.func.camel(field.name,false)}}End != null and condition_model.{{=it.func.camel(field.name,false)}}End != ''\">\n                    {{=field.name}} &lt;= #{condition_model.{{=it.func.camel(field.name,false)}}End,jdbcType={{=field.type}}} AND\n                </if>{{?? field.type.indexOf(\"INT\") >-1 }}\n                <if test=\"condition_model.{{=it.func.camel(field.name,false)}} != null and condition_model.{{=it.func.camel(field.name,false)}}.size > 0\">\n                    {{=field.name}} in\n                    <foreach collection=\"condition_model.{{=it.func.camel(field.name,false)}}\" item=\"temp\" index=\"index\" open=\"(\" separator=\",\" close=\")\">#{temp}</foreach> AND\n                </if>{{??}}<if test=\"condition_model.{{=it.func.camel(field.name,false)}} != null and condition_model.{{=it.func.camel(field.name,false)}} != ''\"> \n                    {{=field.name}} = #{condition_model.{{=it.func.camel(field.name,false)}},jdbcType={{=field.type}}} AND\n                </if> {{?}}{{~}}\n            </trim>\n        </if>\n    </select>\n$blankline\n    <select id=\"selectByPrimaryKey\" resultMap=\"BaseResultMap\">\n        select\n        <include refid=\"Base_Column_List\"/>\n        from {{=it.entity.title}} \n        where 1=1 \n        <if test=\"dataStatus != null and dataStatus >= 0\">\n            AND data_status=#{dataStatus}\n        </if>\n        <if test=\"ids != null and ids.size > 0\">\n           AND  {{~it.entity.fields:field:index}}{{? field.pk }}{{=field.name}}\n        <include refid=\"inIds\"/>{{?}} {{~}}\n        </if>\n    </select>\n$blankline\n    <insert id=\"batchInsert\" parameterType=\"{{=package_do }}\"\n            useGeneratedKeys=\"true\" keyProperty=\"id\"\n            keyColumn=\"id\" statementType=\"PREPARED\">\n        insert into {{=it.entity.title}} (\n        {{~it.entity.fields:field:index}} \n            {{=field.name}}{{?index!=(it.entity.fields.length-1)}},{{?}}\n         {{~}}\n        )\n        values\n        <foreach collection=\"list\" item=\"model\" separator=\",\">\n        (\n        {{~it.entity.fields:field:index}}  {{? field.name=='gmt_modified'||field.name=='gmt_create'}} \n            <if test=\"model.{{=it.func.camel(field.name,false)}} != null\">\n                #{model.{{=it.func.camel(field.name,false)}},jdbcType={{=field.type}}}{{?index!=(it.entity.fields.length-1)}},{{?}}\n            </if> \n            <if test=\"model.{{=it.func.camel(field.name,false)}} == null\">\n                now(){{?index!=(it.entity.fields.length-1)}},{{?}}\n            </if>{{??}}\n            #{model.{{=it.func.camel(field.name,false)}},jdbcType={{=field.type}}}{{?index!=(it.entity.fields.length-1)}},{{?}}{{?}} {{~}}\n        )\n        </foreach>\n    </insert>\n$blankline\n    <insert id=\"batchInsertOrUpdateStatus\" parameterType=\"{{=package_do }}\"\n            useGeneratedKeys=\"true\" keyProperty=\"list.id\"\n            keyColumn=\"id\" statementType=\"PREPARED\">\n        insert into {{=it.entity.title}} (\n        {{~it.entity.fields:field:index}} \n            {{=field.name}}{{?index!=(it.entity.fields.length-1)}},{{?}}\n         {{~}}\n        )\n        values\n        <foreach collection=\"list\" item=\"model\" separator=\",\">\n        (\n        {{~it.entity.fields:field:index}}  {{? field.name=='gmt_modified'||field.name=='gmt_create'}} \n            <if test=\"model.{{=it.func.camel(field.name,false)}} != null\">\n                #{model.{{=it.func.camel(field.name,false)}},jdbcType={{=field.type}}}{{?index!=(it.entity.fields.length-1)}},{{?}}\n            </if> \n            <if test=\"model.{{=it.func.camel(field.name,false)}} == null\">\n                now(){{?index!=(it.entity.fields.length-1)}},{{?}}\n            </if>{{??}}\n            #{model.{{=it.func.camel(field.name,false)}},jdbcType={{=field.type}}}{{?index!=(it.entity.fields.length-1)}},{{?}}{{?}} {{~}}\n        )\n        </foreach>\n        ON DUPLICATE KEY UPDATE\n        data_status= 0,\n        gmt_modified = now(),\n        modified_by = #{modifiedBy,jdbcType=BIGINT}\n    </insert>\n$blankline\n    <insert id=\"insertOrUpdateNonNullFields\" parameterType=\"{{=package_do }}\"\n            useGeneratedKeys=\"true\" keyProperty=\"request_model.id\"\n            keyColumn=\"id\" statementType=\"PREPARED\">\n        insert into {{=it.entity.title}} (\n        {{~it.entity.fields:field:index}} \n            {{=field.name}}{{?index!=(it.entity.fields.length-1)}},{{?}}\n         {{~}}\n        )\n        values\n        (\n        {{~it.entity.fields:field:index}}  {{? field.name=='gmt_modified'||field.name=='gmt_create'}} \n            <if test=\"request_model.{{=it.func.camel(field.name,false)}} != null\">\n                #{request_model.{{=it.func.camel(field.name,false)}},jdbcType={{=field.type}}}{{?index!=(it.entity.fields.length-1)}},{{?}}\n            </if> \n            <if test=\"request_model.{{=it.func.camel(field.name,false)}} == null\">\n                now(){{?index!=(it.entity.fields.length-1)}},{{?}}\n            </if>{{??}}\n            #{request_model.{{=it.func.camel(field.name,false)}},jdbcType={{=field.type}}}{{?index!=(it.entity.fields.length-1)}},{{?}}{{?}} {{~}}\n        )\n        ON DUPLICATE KEY UPDATE\n         {{~it.entity.fields:field:index}}  {{? field.pk }}{{?? field.name!='gmt_modified' && field.name!='gmt_create' && field.name!='created_by' && field.name!='modified_by'}} \n            <if test=\"request_model.{{=it.func.camel(field.name,false)}} != null\">\n               {{=field.name}} = #{request_model.{{=it.func.camel(field.name,false)}},jdbcType={{=field.type}}},\n            </if>{{?}} {{~}}\n        gmt_modified = now(),\n        modified_by = #{modifiedBy,jdbcType=BIGINT}\n    </insert>\n$blankline\n    <update id=\"updateNonNullFieldsByNonNullFields\">\n        update {{=it.entity.title}}\n        <trim prefix=\"set\" suffixOverrides=\",\">\n            {{~it.entity.fields:field:index}} {{? field.pk }} {{?? field.name!='gmt_modified' && field.name!='gmt_create' && field.name!='modified_by'}} \n            <if test=\"request_model.{{=it.func.camel(field.name,false)}} != null\">\n                {{=field.name}} = #{request_model.{{=it.func.camel(field.name,false)}},jdbcType={{=field.type}}},\n            </if>{{?? field.name =='modified_by'}}\n            <if test=\"modifiedBy != null\">\n                {{=field.name}} = #{modifiedBy,jdbcType={{=field.type}}},\n            </if>{{?}} {{~}}\n            gmt_modified = now()\n        </trim>\n         <trim prefix=\"where\" suffixOverrides=\"and\">\n            {{~it.entity.fields:field:index}}{{? field.type.indexOf(\"CHAR\")>-1}}\n                <if test=\"condition_model.{{=it.func.camel(field.name,false)}} != null and condition_model.{{=it.func.camel(field.name,false)}}.size > 0\">\n                    {{=field.name}} in\n                    <foreach collection=\"condition_model.{{=it.func.camel(field.name,false)}}\" item=\"temp\" index=\"index\" open=\"(\" separator=\",\" close=\")\">#{temp}</foreach> AND\n                </if>{{?? field.type.indexOf(\"TIMESTAMP\")>-1}}<if test=\"condition_model.{{=it.func.camel(field.name,false)}}Start != null and condition_model.{{=it.func.camel(field.name,false)}}Start != ''\">\n                    {{=field.name}} &gt;= #{condition_model.{{=it.func.camel(field.name,false)}}Start,jdbcType={{=field.type}}} AND\n                </if>\n                <if test=\"condition_model.{{=it.func.camel(field.name,false)}}End != null and condition_model.{{=it.func.camel(field.name,false)}}End != ''\">\n                    {{=field.name}} &lt;= #{condition_model.{{=it.func.camel(field.name,false)}}End,jdbcType={{=field.type}}} AND\n                </if>{{?? field.type.indexOf(\"INT\") >-1 }}\n                <if test=\"condition_model.{{=it.func.camel(field.name,false)}} != null and condition_model.{{=it.func.camel(field.name,false)}}.size > 0\">\n                    {{=field.name}} in\n                    <foreach collection=\"condition_model.{{=it.func.camel(field.name,false)}}\" item=\"temp\" index=\"index\" open=\"(\" separator=\",\" close=\")\">#{temp}</foreach> AND\n                </if>{{??}}<if test=\"condition_model.{{=it.func.camel(field.name,false)}} != null and condition_model.{{=it.func.camel(field.name,false)}} != ''\"> \n                    {{=field.name}} = #{condition_model.{{=it.func.camel(field.name,false)}},jdbcType={{=field.type}}} AND\n                </if> {{?}}{{~}}\n            </trim>\n    </update>\n$blankline\n    <update id=\"updateNonNullFieldsByPrimaryKey\" parameterType=\"{{=package_do }}\">\n        update {{=it.entity.title}}\n        <trim prefix=\"set\" suffixOverrides=\",\">\n            {{~it.entity.fields:field:index}} {{? field.pk }} {{?? field.name=='gmt_modified'}} \n            <if test=\"{{=it.func.camel(field.name,false)}} != null\">\n                {{=field.name}} = #{{{=it.func.camel(field.name,false)}},jdbcType={{=field.type}}},\n            </if>\n            <if test=\"{{=it.func.camel(field.name,false)}} == null\">\n                {{=field.name}} = now(),\n            </if> \n            {{??}}\n            <if test=\"{{=it.func.camel(field.name,false)}} != null\">\n                {{=field.name}} = #{{{=it.func.camel(field.name,false)}},jdbcType={{=field.type}}},\n            </if> {{?}} {{~}}\n        </trim>\n        <trim prefix=\"where\" suffixOverrides=\"and\">{{~it.entity.fields:field:index}}{{? field.pk }}\n            <if test=\"{{=it.func.camel(field.name,false)}} != null\">\n                {{=field.name}} = #{{{=it.func.camel(field.name,false)}},jdbcType={{=field.type}}} and \n            </if>    {{?}} {{~}}\n        </trim>\n    </update>\n$blankline\n    <update id=\"updateAllFieldsByPrimaryKey\" parameterType=\"{{=package_do }}\">\n        update {{=it.entity.title}}\n        <trim prefix=\"set\" suffixOverrides=\",\">\n            {{~it.entity.fields:field:index}} {{? field.pk }} {{?? field.name=='created_by'||field.name=='gmt_create'}}\n            <if test=\"{{=it.func.camel(field.name,false)}} != null\">\n                {{=field.name}} = #{{{=it.func.camel(field.name,false)}},jdbcType={{=field.type}}},\n            </if> \n            {{?? field.name=='gmt_modified'}} \n            <if test=\"{{=it.func.camel(field.name,false)}} != null\">\n                {{=field.name}} = #{{{=it.func.camel(field.name,false)}},jdbcType={{=field.type}}},\n            </if>\n            <if test=\"{{=it.func.camel(field.name,false)}} == null\">\n                {{=field.name}} = now(),\n            </if> {{??}}\n            {{=field.name}} = #{{{=it.func.camel(field.name,false)}},jdbcType={{=field.type}}}, {{?}}{{~}}\n        </trim>\n        <trim prefix=\"where\" suffixOverrides=\"and\">{{~it.entity.fields:field:index}}{{? field.pk }}\n            <if test=\"{{=it.func.camel(field.name,false)}} != null\">\n                {{=field.name}} = #{{{=it.func.camel(field.name,false)}},jdbcType={{=field.type}}} and \n            </if>    {{?}} {{~}}\n        </trim>\n    </update>\n$blankline\n    <update id=\"batchUpdateStatus\">\n        <if test=\"ids != null and ids.size > 0\">\n        update\n        {{=it.entity.title}} set data_status=#{data_status,jdbcType=INTEGER}, modified_by = #{modifiedBy,jdbcType=BIGINT}\n        where id\n        <include refid=\"inIds\"/>         \n        </if>\n    </update>\n$blankline\n    <update id=\"updateStatusByNonNullField\">\n        <if test=\"request_model != null\">\n            update\n            {{=it.entity.title}} set data_status=#{data_status,jdbcType=INTEGER}, modified_by = #{modifiedBy,jdbcType=BIGINT}\n            <trim prefix=\"where\" suffixOverrides=\"and\">\n            {{~it.entity.fields:field:index}}\n            <if test=\"request_model.{{=it.func.camel(field.name,false)}} != null\">\n                {{=field.name}} = #{request_model.{{=it.func.camel(field.name,false)}},jdbcType={{=field.type}}} and  \n            </if>{{~}}\n            </trim>\n        </if>\n    </update>\n$blankline\n    <sql id=\"inIds\">\n        in\n        <foreach collection=\"ids\" item=\"id\" index=\"index\" open=\"(\" separator=\",\" close=\")\">#{id}\n        </foreach>\n    </sql>\n</mapper>", "fileShow": false, "defaultDatabase": false}, {"code": "MapperApi", "createTableTemplate": "{{ package_name =\"com.yaduo.demo.service\"; }}{{ dir_path ='{{=package_name }}.mapper';}}code_path=|{{=dir_path }}|package {{=dir_path }};\n$blankline\n\nimport {{=package_name }}.domain.{{=it.func.camel(it.entity.title,true) }}DO;\nimport {{=package_name }}.api.dto.request.{{=it.func.camel(it.entity.title,true) }}Request;\nimport {{=package_name }}.api.dto.request.{{=it.func.camel(it.entity.title,true) }}MultiRequest;\nimport org.apache.ibatis.annotations.Param;\n$blankline\nimport java.util.Map;\nimport java.util.List;\n$blankline\n/**\n * <AUTHOR> process.env.USER || \"\"}}\n * @date {{= new Date().toJSON().slice(0, 19).replace('T',' ')}}\n */\npublic interface {{=it.func.camel(it.entity.title,true) }}Mapper {\n$blankline\n    /**\n     * 综合查询-分页\n     *\n     * @param requestModel | 请求条件\n     * @param offset | 偏移量\n     * @return List<{{=it.func.camel(it.entity.title,true) }}DO>\n     * <AUTHOR> process.env.USER || \"\"}}\n     * @date {{= new Date().toJSON().slice(0, 19).replace('T',' ')}}\n     */\n    List<{{=it.func.camel(it.entity.title,true) }}DO> queryPageData(@Param(\"request_model\") {{=it.func.camel(it.entity.title,true) }}Request requestModel,@Param(\"offset\") int offset);\n$blankline\n    /**\n     * 综合查询-总条数\n     * @param requestModel | 请求条件\n     * @return Integer\n     * <AUTHOR> process.env.USER || \"\"}}\n     * @date {{= new Date().toJSON().slice(0, 19).replace('T',' ')}}\n     */\n    Integer queryTotalCount(@Param(\"request_model\") {{=it.func.camel(it.entity.title,true) }}Request requestModel);\n$blankline\n    /**\n     * 精准匹配-分页\n     *\n     * @param requestModel | 请求条件\n     * @param offset | 偏移量\n     * @return List<{{=it.func.camel(it.entity.title,true) }}DO>\n     * <AUTHOR> process.env.USER || \"\"}}\n     * @date {{= new Date().toJSON().slice(0, 19).replace('T',' ')}}\n     */\n    List<{{=it.func.camel(it.entity.title,true) }}DO> getExactData(@Param(\"request_model\") {{=it.func.camel(it.entity.title,true) }}MultiRequest requestModel,@Param(\"offset\") int offset);\n$blankline\n    /**\n     * 精准匹配-总条数\n     * @param requestModel | 请求条件\n     * @return Integer\n     * <AUTHOR> process.env.USER || \"\"}}\n     * @date {{= new Date().toJSON().slice(0, 19).replace('T',' ')}}\n     */\n    Integer getExactTotalCount(@Param(\"request_model\") {{=it.func.camel(it.entity.title,true) }}MultiRequest requestModel);\n$blankline\n    /**\n     * GroupBy字段\n     * @param requestModel | 请求条件\n     * @return List<Map<?,?>>\n     * <AUTHOR> process.env.USER || \"\"}}\n     * @date {{= new Date().toJSON().slice(0, 19).replace('T',' ')}}\n     */\n    List<Map<?,?>> getCountGroupByField(@Param(\"request_model\") {{=it.func.camel(it.entity.title,true) }}MultiRequest requestModel);\n$blankline\n    /**\n     * Distinct字段\n     * @param record | 输出字段\n     * @param conditionModel | 条件字段\n     * @param offset | 偏移量\n     * @return List<Map<?,?>>\n     * <AUTHOR> process.env.USER || \"\"}}\n     * @date {{= new Date().toJSON().slice(0, 19).replace('T',' ')}}\n     */\n    List<Map<?,?>> selectDistinctByConditionModel(@Param(\"request_model\") {{=it.func.camel(it.entity.title,true) }}DO record,@Param(\"condition_model\") {{=it.func.camel(it.entity.title,true) }}MultiRequest conditionModel,@Param(\"offset\") int offset);\n$blankline\n    /**\n     * Distinct字段-总条数\n     * @param record | 输出字段\n     * @param conditionModel | 条件字段\n     * @return Integer\n     * <AUTHOR> process.env.USER || \"\"}}\n     * @date {{= new Date().toJSON().slice(0, 19).replace('T',' ')}}\n     */\n    Integer selectDistinctCountByConditionModel(@Param(\"request_model\") {{=it.func.camel(it.entity.title,true) }}DO record,@Param(\"condition_model\") {{=it.func.camel(it.entity.title,true) }}MultiRequest conditionModel);\n$blankline\n    /**\n     * 根据主键搜索\n     * @param ids | id列表\n     * @param dataStatus | 数据状态,see{@link com.yaduo.infras.core.base.define.DataStatus}\n     * @return List<{{=it.func.camel(it.entity.title,true) }}DO>\n     * <AUTHOR> process.env.USER || \"\"}}\n     * @date {{= new Date().toJSON().slice(0, 19).replace('T',' ')}}\n     */\n    List<{{=it.func.camel(it.entity.title,true) }}DO> selectByPrimaryKey(@Param(\"ids\") List<Long> ids,@Param(\"dataStatus\") Integer dataStatus);\n$blankline\n    /**\n     * 根据主键批量更新状态\n     * @param ids | 主键list\n     * @param dataStatus 状态，see{@link com.yaduo.infras.core.base.define.DataStatus}\n     * @param modifiedBy | 修改人的UID\n     * @return int\n     * <AUTHOR> process.env.USER || \"\"}}\n     * @date {{= new Date().toJSON().slice(0, 19).replace('T',' ')}}\n     */\n    int batchUpdateStatus(@Param(\"ids\") List<Long> ids, @Param(\"data_status\") Integer dataStatus, @Param(\"modifiedBy\") Long modifiedBy);\n$blankline\n    /**\n     * 根据非NullField更新状态\n     * @param requestModel | DTO\n     * @param dataStatus | 状态，see{@link com.yaduo.infras.core.base.define.DataStatus}\n     * @param modifiedBy | 修改人的UID\n     * @return int\n     * <AUTHOR> process.env.USER || \"\"}}\n     * @date {{= new Date().toJSON().slice(0, 19).replace('T',' ')}}\n     */\n    int updateStatusByNonNullField(@Param(\"request_model\") {{=it.func.camel(it.entity.title,true) }}DO requestModel, @Param(\"data_status\") Integer dataStatus, @Param(\"modifiedBy\") Long modifiedBy);\n$blankline\n    /**\n     * 物理删除数据\n     * @param ids | 主键list\n     * @return int\n     * <AUTHOR> process.env.USER || \"\"}}\n     * @date {{= new Date().toJSON().slice(0, 19).replace('T',' ')}}\n     */\n    int deletePhysically(@Param(\"ids\") List<Long> ids);\n$blankline\n    /**\n     * 批量插入\n     * @param list | 数组列表\n     * @return int\n     * <AUTHOR> process.env.USER || \"\"}}\n     * @date {{= new Date().toJSON().slice(0, 19).replace('T',' ')}}\n     */\n    int batchInsert(List<{{=it.func.camel(it.entity.title,true) }}DO> list);\n$blankline\n    /**\n     * 批量插入\n     * @param list | 数组列表\n     * @param modifiedBy | 修改人的UID\n     * @return int\n     * <AUTHOR> process.env.USER || \"\"}}\n     * @date {{= new Date().toJSON().slice(0, 19).replace('T',' ')}}\n     */\n    int batchInsertOrUpdateStatus(@Param(\"list\") List<{{=it.func.camel(it.entity.title,true) }}DO> list, @Param(\"modifiedBy\") Long modifiedBy);\n$blankline\n    /**\n     * 插入数据，冲突时，更新所有非主键、非null字段\n     * @param record | 更新的对象\n     * @param modifiedBy | 修改人的UID\n     * @return int\n     * <AUTHOR> process.env.USER || \"\"}}\n     * @date {{= new Date().toJSON().slice(0, 19).replace('T',' ')}}\n     */\n    int insertOrUpdateNonNullFields(@Param(\"request_model\") {{=it.func.camel(it.entity.title,true) }}DO record,@Param(\"modifiedBy\") Long modifiedBy);\n$blankline\n    /**\n     * 根据主键更新\n     * @param record | 更新的对象\n     * @return int\n     * <AUTHOR> process.env.USER || \"\"}}\n     * @date {{= new Date().toJSON().slice(0, 19).replace('T',' ')}}\n     */\n    int updateAllFieldsByPrimaryKey({{=it.func.camel(it.entity.title,true) }}DO record);\n$blankline\n    /**\n     * 根据条件model更新非null字段\n     * @param record | 更新的对象\n     * @param conditionModel | 条件对象\n     * @param modifiedBy | 修改人的UID\n     * @return int\n     * <AUTHOR> process.env.USER || \"\"}}\n     * @date {{= new Date().toJSON().slice(0, 19).replace('T',' ')}}\n     */\n    int updateNonNullFieldsByNonNullFields(@Param(\"request_model\") {{=it.func.camel(it.entity.title,true) }}DO record,@Param(\"condition_model\") {{=it.func.camel(it.entity.title,true) }}MultiRequest conditionModel,@Param(\"modifiedBy\") Long modifiedBy);\n$blankline\n    /**\n     * 根据主键更新非null值\n     * @param record | 更新的对象\n     * @return int\n     * <AUTHOR> process.env.USER || \"\"}}\n     * @date {{= new Date().toJSON().slice(0, 19).replace('T',' ')}}\n     */\n    int updateNonNullFieldsByPrimaryKey({{=it.func.camel(it.entity.title,true) }}DO record);\n\n}"}, {"code": "JAVA_DO", "createTableTemplate": "{{ package_name =\"com.yaduo.demo.service\"; }}{{dir_path ='{{=package_name }}.domain';}}code_path=|{{=dir_path }}|package {{=dir_path }};\n$blankline\n//import io.swagger.annotations.ApiModel;\nimport lombok.*;\n$blankline\n/**\n * <AUTHOR> process.env.USERNAME || \"\"}}\n * @date {{= new Date().toJSON().slice(0, 19).replace('T',' ')}}\n */\n@Data\n@EqualsAndHashCode(callSuper = false)\n@AllArgsConstructor\n@NoArgsConstructor\n@Builder\n//@ApiModel(value = \"{{=it.func.camel(it.entity.title,true) }}DO\", description = \"{{=it.func.join(it.entity.chnname,'',' ')}}{{? it.entity.remark }}{{=it.func.join(';',it.entity.remark,'',' ')}}{{?}}\")\npublic class {{=it.func.camel(it.entity.title,true) }}DO {\n    {{~it.entity.fields:field:index}}\n$blankline\n    /**\n     * {{=it.func.join(field.chnname,field.remark,';')}}\n     */\n    private {{=field.type}} {{=it.func.camel(field.name,false)}};\n    {{~}}\n$blankline\n}"}, {"code": "JAVA_DTO", "template": "package group.rober.pdman.{{=it.module.name}}.entity;\n$blankline\nimport javax.persistence.*;\nimport java.io.Serializable;\nimport java.util.Date;\n$blankline\n$blankline\n\n/** {{=it.entity.chnname}} */\n@Table(name=\"{{=it.entity.title}}\")\npublic class {{=it.func.camel(it.entity.title,true) }} implements Serializable,Cloneable{\n{{~it.entity.fields:field:index}}\n    /** {{=it.func.join(field.chnname,field.remark,';')}} */\n    {{? field.pk }}\n    @Id\n    @GeneratedValue\n    {{?}}\n    private {{=field.type}} {{=it.func.camel(field.name,false)}} ;\n{{~}}\n$blankline\n{{~it.entity.fields:field:index}}\n    /** {{=it.func.join(field.chnname,field.remark,';')}} */\n    public {{=field.type}} get{{=it.func.camel(field.name,true)}}(){\n        return this.{{=it.func.camel(field.name,false)}};\n    }\n    /** {{=it.func.join(field.chnname,field.remark,';')}} */\n    public void set{{=it.func.camel(field.name,true)}}({{=field.type}} {{= it.func.camel(field.name,false) }}){\n        this.{{=it.func.camel(field.name,false)}} = {{= it.func.camel(field.name,false) }};\n    }\n{{~}}\n}", "createTableTemplate": "{{ package_name =\"com.yaduo.demo.service\"; }}{{ dir_path ='{{=package_name }}.api.dto';}}code_path=|{{=dir_path }}|package {{=dir_path }};\n$blankline\n//import io.swagger.annotations.ApiModel;\n//import io.swagger.annotations.ApiModelProperty;\nimport io.swagger.v3.oas.annotations.media.Schema;\nimport lombok.*;\n$blankline\n/**\n * <AUTHOR> process.env.USERNAME || \"\"}}\n * @date {{= new Date().toJSON().slice(0, 19).replace('T',' ')}}\n */\n@Data\n@EqualsAndHashCode(callSuper = false)\n@AllArgsConstructor\n@NoArgsConstructor\n@Builder\n//@ApiModel(value = \"{{=it.func.camel(it.entity.title,true) }}DTO\", description = \"{{=it.func.join(it.entity.chnname,'',' ')}}{{? it.entity.remark }}{{=it.func.join(';',it.entity.remark,'',' ')}}{{?}}\")\n//@ApiModel(value = \"{{=it.func.camel(it.entity.title,true) }}DTO\", description = \"{{=it.func.join(it.entity.chnname,'',' ')}}{{? it.entity.remark }}{{=it.func.join(';',it.entity.remark,'',' ')}}{{?}}\")\n@Schema(description = \"{{=it.func.join(it.entity.chnname,'',' ')}}{{? it.entity.remark }}{{=it.func.join(';',it.entity.remark,'',' ')}}{{?}}\")\npublic class {{=it.func.camel(it.entity.title,true) }}DTO {\n    {{~it.entity.fields:field:index}}\n$blankline\n    /**\n     * {{=it.func.join(field.chnname,field.remark,';')}}\n     */\n    //@ApiModelProperty(value = \"{{=it.func.join(field.chnname,field.remark,';')}}\")\n    @Schema(description = \"{{=it.func.join(field.chnname,field.remark,';')}}\")\n    private {{=field.type}} {{=it.func.camel(field.name,false)}};\n    {{~}}\n$blankline\n        \n    /**\n     * 自动trim\n     */\n    public void autoTrim() {\n        {{~it.entity.fields:field:index}}{{? field.type==\"String\" }}\n        if (this.{{=it.func.camel(field.name,false)}} != null ){\n            this.{{=it.func.camel(field.name,false)}} = this.{{=it.func.camel(field.name,false)}}.trim();\n        }{{?}}{{~}}\n    }\n}", "deleteTableTemplate": "", "rebuildTableTemplate": "", "createFieldTemplate": "", "updateFieldTemplate": "", "deleteFieldTemplate": "", "deleteIndexTemplate": "", "createIndexTemplate": "", "updateTableComment": ""}, {"code": "Python", "createTableTemplate": "class {{=it.func.camel(it.entity.title,true) }}DO(json.JSONEncoder):\n    \"\"\"\n    {{=it.func.join(it.entity.chnname,'',' ')}}{{=it.func.join(';',it.entity.remark,'',' ')}}\n    \"\"\"\n    \n    def __init__(self, **kwargs):\n        super().__init__()\n        self.__dict__.update(kwargs)\n    $blankline\n    def __str__(self):\n        fields = ['{}={!r}'.format(k, v)\n                  for k, v in self.__dict__.items() if not k.startswith('_')]\n\n        return '{}({})'.format(self.__class__.__name__, ','.join(fields))\n\n{{~it.entity.fields:field:index}}\n\n    \"\"\"\n    {{=it.func.join(field.chnname,field.remark,';')}}\n    \"\"\"\n    {{=field.name}}: {{=field.type}} = None\n{{~}}\n$blankline\n{{~it.entity.fields:field:index}}\n{{~}}\n$blankline\n"}, {"code": "JavaApi", "createTableTemplate": "{{ package_name =\"com.yaduo.demo.service\"; }}{{dir_path ='{{=package_name }}.api.service';}}code_path=|{{=dir_path }}|package {{=dir_path }};\n\n$blankline\nimport {{=package_name}}.api.dto.{{=it.func.camel(it.entity.title,true) }}DTO;\nimport {{=package_name}}.api.dto.request.{{=it.func.camel(it.entity.title,true) }}MultiRequest;\nimport {{=package_name}}.api.dto.request.{{=it.func.camel(it.entity.title,true) }}Request;\nimport com.alibaba.fastjson.JSONObject;\nimport com.yaduo.infras.core.base.bean.AtourRequest;\nimport com.yaduo.infras.core.base.bean.AtourResponse;\nimport com.yaduo.infras.core.base.bean.ResponseList;\nimport com.yaduo.infras.core.base.define.BatchUpdateStatusRequest;\nimport com.yaduo.infras.core.base.define.SimplePairRequest;\nimport io.swagger.v3.oas.annotations.Operation;\nimport io.swagger.v3.oas.annotations.tags.Tag;\nimport org.springframework.web.bind.annotation.RequestBody;\nimport org.springframework.web.bind.annotation.RequestMapping;\nimport org.springframework.web.bind.annotation.RequestMethod;\n$blankline\nimport java.util.List;\n$blankline\n/**\n * <AUTHOR> process.env.USER || \"\"}}\n * @date {{= new Date().toJSON().slice(0, 19).replace('T',' ')}}\n */\n@Tag(name = \"{{=it.func.join(it.entity.chnname,'',' ')}}{{? it.entity.remark }}{{=it.func.join(';',it.entity.remark,'',' ')}}{{?}}\")\npublic interface I{{=it.func.camel(it.entity.title,true) }}Service {\n$blankline\n    @Operation(summary = \"全字段模糊查询\", description = \"字符串模糊匹配\")\n    @RequestMapping(value = \"/{{=it.entity.title}}/query\", method = RequestMethod.POST)\n    AtourResponse<ResponseList<{{=it.func.camel(it.entity.title,true) }}DTO>> query(@RequestBody AtourRequest<{{=it.func.camel(it.entity.title,true) }}Request> request);\n$blankline\n    @Operation(summary = \"全字段模糊查询计数\", description = \"字符串模糊匹配\")\n    @RequestMapping(value = \"/{{=it.entity.title}}/query_count\", method = RequestMethod.POST)\n    AtourResponse<Integer> queryCount(@RequestBody AtourRequest<{{=it.func.camel(it.entity.title,true) }}Request> request);\n$blankline\n    @Operation(summary = \"list字段精准查询\", description = \"字符串非模糊匹配\")\n    @RequestMapping(value = \"/{{=it.entity.title}}/query_exact\", method = RequestMethod.POST)\n    AtourResponse<ResponseList<{{=it.func.camel(it.entity.title,true) }}DTO>> queryExact(@RequestBody AtourRequest<{{=it.func.camel(it.entity.title,true) }}MultiRequest> request);\n$blankline\n    @Operation(summary = \"list字段精准查询计数\", description = \"字符串非模糊匹配\")\n    @RequestMapping(value = \"/{{=it.entity.title}}/query_exact_count\", method = RequestMethod.POST)\n    AtourResponse<Integer> queryExactCount(@RequestBody AtourRequest<{{=it.func.camel(it.entity.title,true) }}MultiRequest> request);\n$blankline\n    @Operation(summary = \"主键批量查询\", description = \"\")\n    @RequestMapping(value = \"/{{=it.entity.title}}/search_by_id\", method = RequestMethod.POST)\n    AtourResponse<List<{{=it.func.camel(it.entity.title,true) }}DTO>> searchById(@RequestBody AtourRequest<List<Long>> request);\n$blankline\n    @Operation(summary = \"根据Field返回数量\", description = \"\")\n    @RequestMapping(value = \"/{{=it.entity.title}}/get_count_group_by_field\", method = RequestMethod.POST)\n    AtourResponse<List<JSONObject>> getCountGroupByField(@RequestBody AtourRequest<{{=it.func.camel(it.entity.title,true) }}MultiRequest> request);\n$blankline\n    @Operation(summary = \"查询Distinct字段-根据条件Model\", description = \"\")\n    @RequestMapping(value = \"/{{=it.entity.title}}/select_distinct_field\", method = RequestMethod.POST)\n    AtourResponse<ResponseList<JSONObject>> selectDistinctFieldByConditionModel(@RequestBody AtourRequest<SimplePairRequest<{{=it.func.camel(it.entity.title,true) }}DTO,{{=it.func.camel(it.entity.title,true) }}MultiRequest>> request);\n$blankline\n    @Operation(summary = \"查询Distinct字段的数量-根据条件Model\", description = \"\")\n    @RequestMapping(value = \"/{{=it.entity.title}}/select_distinct_field_count\", method = RequestMethod.POST)\n    AtourResponse<Integer> selectDistinctFieldCountByConditionModel(@RequestBody AtourRequest<SimplePairRequest<{{=it.func.camel(it.entity.title,true) }}DTO,{{=it.func.camel(it.entity.title,true) }}MultiRequest>> request);\n$blankline\n    @Operation(summary = \"查询Distinct字段的数量批量更新状态\", description = \"批量删更新状态不判断id是否存在\")\n    @RequestMapping(value = \"/{{=it.entity.title}}/modify_status\", method = RequestMethod.POST)\n    AtourResponse<Boolean> modifyStatus(@RequestBody AtourRequest<BatchUpdateStatusRequest> request);\n$blankline\n    @Operation(summary = \"根据非空FieldDataStatus\", description = \"\")\n    @RequestMapping(value = \"/{{=it.entity.title}}/modify_status_by_dto\", method = RequestMethod.POST)\n    AtourResponse<Boolean> modifyStatusByDTO(@RequestBody AtourRequest<SimplePairRequest<{{=it.func.camel(it.entity.title,true) }}DTO,Integer>> request);\n$blankline\n    @Operation(summary = \"批量插入\", description = \"批量新增，返回新增成功的完成数据对象\")\n    @RequestMapping(value = \"/{{=it.entity.title}}/insert\", method = RequestMethod.POST)\n    AtourResponse<List<{{=it.func.camel(it.entity.title,true) }}DTO>> insert(@RequestBody AtourRequest<List<{{=it.func.camel(it.entity.title,true) }}DTO>> request);\n$blankline\n    @Operation(summary = \"批量插入并自动更新主键冲突的DataStatus\", description = \"\")\n    @RequestMapping(value = \"/{{=it.entity.title}}/batch_insert_or_update_status\", method = RequestMethod.POST)\n    AtourResponse<Void> batchInsertOrUpdateStatus(@RequestBody AtourRequest<List<{{=it.func.camel(it.entity.title,true) }}DTO>> request);\n$blankline\n    @Operation(summary = \"插入单条数据,冲突时，更新所有非主键且非null字段\", description = \"\")\n    @RequestMapping(value = \"/{{=it.entity.title}}/insert_or_update_none_null_field\", method = RequestMethod.POST)\n    AtourResponse<Long> insertOrUpdateNonNullField(@RequestBody AtourRequest<{{=it.func.camel(it.entity.title,true) }}DTO> request);\n$blankline\n    @Operation(summary = \"根据条件model更新所有非null字段\", description = \"\")\n    @RequestMapping(value = \"/{{=it.entity.title}}/update_dto_by_condition_model\", method = RequestMethod.POST)\n    AtourResponse<Void> updateDtoByConditionModel(@RequestBody AtourRequest<SimplePairRequest<{{=it.func.camel(it.entity.title,true) }}DTO,{{=it.func.camel(it.entity.title,true) }}MultiRequest>> request);\n$blankline\n    @Operation(summary = \"批量修改\", description = \"批量修改，返回修改成功的DataId\")\n    @RequestMapping(value = \"/{{=it.entity.title}}/update\", method = RequestMethod.POST)\n    AtourResponse<List<Long>> updateNonNullFieldsByPrimaryKey(@RequestBody AtourRequest<List<{{=it.func.camel(it.entity.title,true) }}DTO>> request);\n}"}, {"code": "JavaService", "createTableTemplate": "{{ package_domain =\"com.yaduo.demo.service\"; }}{{ package_name ='{{=package_domain }}'; }}{{dir_path ='{{=package_name }}.service';}}code_path=|{{=dir_path }}|package {{=dir_path }};\n\n$blankline\nimport {{=package_name}}.api.service.I{{=it.func.camel(it.entity.title,true) }}Service;\nimport {{=package_name}}.api.dto.{{=it.func.camel(it.entity.title,true) }}DTO;\nimport {{=package_name}}.api.dto.request.{{=it.func.camel(it.entity.title,true) }}Request;\nimport {{=package_name}}.api.dto.request.{{=it.func.camel(it.entity.title,true) }}MultiRequest;\nimport {{=package_name}}.domain.{{=it.func.camel(it.entity.title,true) }}DO;\nimport {{=package_name}}.manager.{{=it.func.camel(it.entity.title,true) }}Manager;\nimport {{=package_name}}.mapper.{{=it.func.camel(it.entity.title,true) }}Mapper;\nimport com.alibaba.fastjson.JSON;\nimport com.alibaba.fastjson.JSONObject;\nimport com.yaduo.infras.core.base.bean.AtourRequest;\nimport com.yaduo.infras.core.base.bean.AtourResponse;\nimport com.yaduo.infras.core.base.bean.ResponseList;\nimport com.yaduo.infras.core.base.bean.SortProperty;\nimport com.yaduo.infras.core.base.define.BatchUpdateStatusRequest;\nimport com.yaduo.infras.core.base.define.SimplePairRequest;\nimport com.yaduo.infras.core.base.define.DataStatus;\nimport com.yaduo.infras.core.base.exception.CodeEnum;\nimport com.yaduo.infras.core.base.util.RequestChecker;\nimport com.yaduo.infras.core.base.util.RequestSortUtils;\nimport com.yaduo.infras.core.base.util.TransferUtil;\nimport com.yaduo.infras.core.logging.Loggers;\nimport com.yaduo.infras.core.logging.annotations.RestMethod;\nimport org.springframework.dao.DuplicateKeyException;\nimport org.springframework.util.StringUtils;\nimport org.springframework.web.bind.annotation.RequestBody;\nimport org.springframework.web.bind.annotation.RestController;\n$blankline\nimport javax.annotation.Resource;\nimport java.util.*;\nimport java.util.stream.Collectors;\n\n$blankline\n/**\n * <AUTHOR> process.env.USERNAME || \"\"}}\n * @date {{= new Date().toJSON().slice(0, 19).replace('T',' ')}}\n */\n@RestController\npublic class {{=it.func.camel(it.entity.title,true) }}ServiceImpl implements I{{=it.func.camel(it.entity.title,true) }}Service {\n$blankline\n    @Resource\n    private {{=it.func.camel(it.entity.title,true) }}Mapper mapper{{=it.func.camel(it.entity.title,true) }};\n    @Resource\n    private {{=it.func.camel(it.entity.title,true) }}Manager manager{{=it.func.camel(it.entity.title,true) }};\n$blankline\n    /**\n     * 全字段模糊查询\n     **/ \n    @Override\n    @RestMethod\n    public AtourResponse<ResponseList<{{=it.func.camel(it.entity.title,true) }}DTO>> query(@RequestBody AtourRequest<{{=it.func.camel(it.entity.title,true) }}Request> request) {\n        if (request.getModel() == null) {\n            return AtourResponse.errorResponse(CodeEnum.WRONG_PARAMS);\n        }\n        if (request.getModel().getPage() == null || request.getModel().getPage() <= 0) {\n            return AtourResponse.errorResponse(\"分页的页数不正确\");\n        }\n        if (request.getModel().getSize() == null || request.getModel().getSize() < 1 || request.getModel().getSize() > 1000) {\n            return AtourResponse.errorResponse(\"分页的条数不正确\");\n        }\n        request.getModel().autoTrim();\n        //DataStatus为-1的时候，表示查询全部，此时，移除状态即可\n        if(request.getModel().getDataStatus() !=null && request.getModel().getDataStatus() == -1){\n            request.getModel().setDataStatus(null);\n        }\n        Integer totalCount = mapper{{=it.func.camel(it.entity.title,true) }}.queryTotalCount(request.getModel());\n        ResponseList<{{=it.func.camel(it.entity.title,true) }}DTO> response = new ResponseList<>();\n        response.setPage(request.getModel().getPage());\n        response.setSize(request.getModel().getSize());\n        response.setSort(request.getModel().getSort());\n        response.setTotal(totalCount);\n        response.setTotal_page(totalCount);\n        response.setIs_last(1);\n        response.setContent(Collections.EMPTY_LIST);\n        if (totalCount > 0) {\n            int maxPageCount = totalCount / (request.getModel().getSize());\n            if (request.getModel().getSize() * (maxPageCount) < totalCount) {\n                maxPageCount += 1;\n            }\n            response.setTotal_page(maxPageCount);\n            response.setIs_last(request.getModel().getPage() >= response.getTotal_page() ? 1 : 0);\n            if (request.getModel().getPage() > maxPageCount) {\n                return AtourResponse.successResponse(response);\n            }\n            //检查默认的排序条件\n            request.getModel().setSort(RequestSortUtils.autoCheckEmptySortAndAddSort(request.getModel().getSort(),new SortProperty(\"gmt_create\",\"desc\"),new SortProperty(\"id\",\"desc\")));\n            List<{{=it.func.camel(it.entity.title,true) }}DO> result = mapper{{=it.func.camel(it.entity.title,true) }}.queryPageData(request.getModel(),(request.getModel().getSize()*(request.getModel().getPage()-1)));\n            TransferUtil<{{=it.func.camel(it.entity.title,true) }}DO, {{=it.func.camel(it.entity.title,true) }}DTO> transfer = new TransferUtil<>(new {{=it.func.camel(it.entity.title,true) }}DO(), new {{=it.func.camel(it.entity.title,true) }}DTO());\n            response.setContent(transfer.convertToDst(result));\n        }\n        return AtourResponse.successResponse(response);\n    }\n$blankline\n    /**\n     * 全字段模糊查询计数\n     **/ \n    @Override\n    @RestMethod\n    public AtourResponse<Integer> queryCount(@RequestBody AtourRequest<{{=it.func.camel(it.entity.title,true) }}Request> request) {\n        if (request.getModel() == null) {\n            return AtourResponse.errorResponse(CodeEnum.WRONG_PARAMS);\n        }\n        request.getModel().autoTrim();\n        //DataStatus为-1的时候，表示查询全部，此时，移除状态即可\n        if(request.getModel().getDataStatus() !=null && request.getModel().getDataStatus() == -1){\n            request.getModel().setDataStatus(null);\n        }\n        Integer totalCount = mapper{{=it.func.camel(it.entity.title,true) }}.queryTotalCount(request.getModel());\n        return AtourResponse.successResponse(totalCount);\n    }\n$blankline\n    /**\n     * list字段精准查询\n     **/ \n    @Override\n    @RestMethod\n    public AtourResponse<ResponseList<{{=it.func.camel(it.entity.title,true) }}DTO>> queryExact(@RequestBody AtourRequest<{{=it.func.camel(it.entity.title,true) }}MultiRequest> request) {\n        if (request.getModel() == null) {\n            return AtourResponse.errorResponse(CodeEnum.WRONG_PARAMS);\n        }\n        if (request.getModel().getPage() == null || request.getModel().getPage() <= 0) {\n            return AtourResponse.errorResponse(\"分页的页数不正确\");\n        }\n        if (request.getModel().getSize() == null || request.getModel().getSize() < 1 || request.getModel().getSize() > 1000) {\n            return AtourResponse.errorResponse(\"分页的条数不正确\");\n        }\n        Integer totalCount = mapper{{=it.func.camel(it.entity.title,true) }}.getExactTotalCount(request.getModel());\n        ResponseList<{{=it.func.camel(it.entity.title,true) }}DTO> response = new ResponseList<>();\n        response.setPage(request.getModel().getPage());\n        response.setSize(request.getModel().getSize());\n        response.setSort(request.getModel().getSort());\n        response.setTotal(totalCount);\n        response.setTotal_page(totalCount);\n        response.setIs_last(1);\n        response.setContent(Collections.EMPTY_LIST);\n        if (totalCount > 0) {\n            int maxPageCount = totalCount / (request.getModel().getSize());\n            if (request.getModel().getSize() * (maxPageCount) < totalCount) {\n                maxPageCount += 1;\n            }\n            response.setTotal_page(maxPageCount);\n            response.setIs_last(request.getModel().getPage() >= response.getTotal_page() ? 1 : 0);\n            if (request.getModel().getPage() > maxPageCount) {\n                return AtourResponse.successResponse(response);\n            }\n            //检查默认的排序条件\n            request.getModel().setSort(RequestSortUtils.autoCheckEmptySortAndAddSort(request.getModel().getSort(),new SortProperty(\"gmt_create\",\"desc\"),new SortProperty(\"id\",\"desc\")));\n            List<{{=it.func.camel(it.entity.title,true) }}DO> result = mapper{{=it.func.camel(it.entity.title,true) }}.getExactData(request.getModel(),(request.getModel().getSize()*(request.getModel().getPage()-1)));\n            TransferUtil<{{=it.func.camel(it.entity.title,true) }}DO, {{=it.func.camel(it.entity.title,true) }}DTO> transfer = new TransferUtil<>(new {{=it.func.camel(it.entity.title,true) }}DO(), new {{=it.func.camel(it.entity.title,true) }}DTO());\n            response.setContent(transfer.convertToDst(result));\n        }\n        return AtourResponse.successResponse(response);\n    }\n$blankline\n    /**\n     * list字段精准查询计数\n     **/ \n    @Override\n    @RestMethod\n    public AtourResponse<Integer> queryExactCount(@RequestBody AtourRequest<{{=it.func.camel(it.entity.title,true) }}MultiRequest> request) {\n        if (request.getModel() == null) {\n            return AtourResponse.errorResponse(CodeEnum.WRONG_PARAMS);\n        }\n        Integer totalCount = mapper{{=it.func.camel(it.entity.title,true) }}.getExactTotalCount(request.getModel());\n        return AtourResponse.successResponse(totalCount);\n    }\n$blankline\n    /**\n     * 主键批量查询\n     **/ \n    @Override\n    @RestMethod\n    public AtourResponse<List<{{=it.func.camel(it.entity.title,true) }}DTO>> searchById(@RequestBody AtourRequest<List<Long>> request) {\n        if (request.getModel() == null) {\n            return AtourResponse.errorResponse(CodeEnum.WRONG_PARAMS);\n        }\n        List<{{=it.func.camel(it.entity.title,true) }}DO> result = mapper{{=it.func.camel(it.entity.title,true) }}.selectByPrimaryKey(request.getModel(),null);\n        TransferUtil<{{=it.func.camel(it.entity.title,true) }}DO, {{=it.func.camel(it.entity.title,true) }}DTO> transfer = new TransferUtil<>(new {{=it.func.camel(it.entity.title,true) }}DO(), new {{=it.func.camel(it.entity.title,true) }}DTO());\n        return AtourResponse.successResponse(transfer.convertToDst(result));\n    }\n$blankline\n    /**\n     * 根据Field返回数量\n     **/ \n    @Override\n    @RestMethod\n    public AtourResponse<List<JSONObject>> getCountGroupByField(AtourRequest<{{=it.func.camel(it.entity.title,true) }}MultiRequest> request) {\n        if (request.getModel() == null) {\n            return AtourResponse.errorResponse(CodeEnum.WRONG_PARAMS);\n        }\n        List<Map<?,?>> totalCount = mapper{{=it.func.camel(it.entity.title,true) }}.getCountGroupByField(request.getModel());\n        return AtourResponse.successResponse(JSON.parseArray(JSON.toJSONString(totalCount),JSONObject.class));\n    }\n    /**\n     * 查询Distinct字段-根据条件Model\n     */\n    @Override\n    @RestMethod\n    public AtourResponse<ResponseList<JSONObject>> selectDistinctFieldByConditionModel(@RequestBody AtourRequest<SimplePairRequest<{{=it.func.camel(it.entity.title,true) }}DTO,{{=it.func.camel(it.entity.title,true) }}MultiRequest>> request){\n        if (request.getModel() == null) {\n            return AtourResponse.errorResponse(CodeEnum.WRONG_PARAMS);\n        }\n        if (request.getModel().getK2()==null){\n            return AtourResponse.errorResponse(\"限定条件不正确\");\n        }\n        if (request.getModel().getK2().getPage() == null || request.getModel().getK2().getPage() <= 0) {\n            return AtourResponse.errorResponse(\"分页的页数不正确\");\n        }\n        if (request.getModel().getK2().getSize() == null || request.getModel().getK2().getSize() < 1 || request.getModel().getK2().getSize() > 1000) {\n            return AtourResponse.errorResponse(\"分页的条数不正确\");\n        }\n        TransferUtil<{{=it.func.camel(it.entity.title,true) }}DO, {{=it.func.camel(it.entity.title,true) }}DTO> transfer = new TransferUtil<>(new {{=it.func.camel(it.entity.title,true) }}DO(), new {{=it.func.camel(it.entity.title,true) }}DTO());\n        {{=it.func.camel(it.entity.title,true) }}DO domain=transfer.convertToSrc(request.getModel().getK());\n        Integer totalCount = mapper{{=it.func.camel(it.entity.title,true) }}.selectDistinctCountByConditionModel(domain,request.getModel().getK2());\n        ResponseList<JSONObject> response = new ResponseList<>();\n        response.setPage(request.getModel().getK2().getPage());\n        response.setSize(request.getModel().getK2().getSize());\n        response.setSort(request.getModel().getK2().getSort());\n        response.setTotal(totalCount);\n        response.setTotal_page(totalCount);\n        response.setIs_last(1);\n        response.setContent(Collections.EMPTY_LIST);\n        if (totalCount > 0) {\n            int maxPageCount = totalCount / (request.getModel().getK2().getSize());\n            if (request.getModel().getK2().getSize() * (maxPageCount) < totalCount) {\n                maxPageCount += 1;\n            }\n            response.setTotal_page(maxPageCount);\n            response.setIs_last(request.getModel().getK2().getPage() >= response.getTotal_page() ? 1 : 0);\n            if (request.getModel().getK2().getPage() > maxPageCount) {\n                return AtourResponse.successResponse(response);\n            }\n            List<Map<?,?>> result = mapper{{=it.func.camel(it.entity.title,true) }}.selectDistinctByConditionModel(domain,request.getModel().getK2(),(request.getModel().getK2().getSize()*(request.getModel().getK2().getPage()-1)));\n            response.setContent(JSON.parseArray(JSON.toJSONString(result.stream().filter(Objects::nonNull).collect(Collectors.toList())),JSONObject.class));\n        }\n        return AtourResponse.successResponse(response);\n    }\n$blankline\n    /**\n     * 查询Distinct字段的数量-根据条件Model\n     */\n    @Override\n    @RestMethod\n    public AtourResponse<Integer> selectDistinctFieldCountByConditionModel(@RequestBody AtourRequest<SimplePairRequest<{{=it.func.camel(it.entity.title,true) }}DTO,{{=it.func.camel(it.entity.title,true) }}MultiRequest>> request){\n      if (request.getModel() == null) {\n            return AtourResponse.errorResponse(CodeEnum.WRONG_PARAMS);\n        }\n        TransferUtil<{{=it.func.camel(it.entity.title,true) }}DTO, {{=it.func.camel(it.entity.title,true) }}DO> transfer = new TransferUtil<>(new {{=it.func.camel(it.entity.title,true) }}DTO(), new {{=it.func.camel(it.entity.title,true) }}DO());\n        Integer totalCount = mapper{{=it.func.camel(it.entity.title,true) }}.selectDistinctCountByConditionModel(transfer.convertToDst(request.getModel().getK()),request.getModel().getK2());\n        return AtourResponse.successResponse(totalCount);\n    }\n$blankline\n    /**\n     * 批量更新状态\n     **/ \n    @Override\n    @RestMethod\n    public AtourResponse<Boolean> modifyStatus(@RequestBody AtourRequest<BatchUpdateStatusRequest> request) {\n        RequestChecker checker = RequestChecker.start(request).withUser(request);\n        if (!checker.success()) {\n            return AtourResponse.errorResponse(checker.getError());\n        }\n        try {\n            mapper{{=it.func.camel(it.entity.title,true) }}.batchUpdateStatus(request.getModel().getIds(),request.getModel().getDataStatus(), request.getHeader().getUser_id());\n            manager{{=it.func.camel(it.entity.title,true) }}.afterModifyStatus(request.getModel());\n            return AtourResponse.successResponse(true);\n        }catch (Exception e){\n            Loggers.BIZ.error(\"{{=it.func.camel(it.entity.title,true) }}批量更新状态失败\", e);\n            return AtourResponse.errorResponse(\"更新状态失败(\"+e.getMessage()+\")\");\n        }\n    }\n$blankline\n    /**\n     * 根据非空Field更新状态\n     **/ \n    @Override\n    @RestMethod\n    public AtourResponse<Boolean> modifyStatusByDTO(@RequestBody AtourRequest<SimplePairRequest<{{=it.func.camel(it.entity.title,true) }}DTO,Integer>> request){\n        RequestChecker checker = RequestChecker.start(request).withUser(request);\n        if (!checker.success()) {\n            return AtourResponse.errorResponse(checker.getError());\n        }\n        try {\n            TransferUtil<{{=it.func.camel(it.entity.title,true) }}DTO, {{=it.func.camel(it.entity.title,true) }}DO> transfer = new TransferUtil<>(new {{=it.func.camel(it.entity.title,true) }}DTO(), new {{=it.func.camel(it.entity.title,true) }}DO());\n            mapper{{=it.func.camel(it.entity.title,true) }}.updateStatusByNonNullField(transfer.convertToDst(request.getModel().getK()),request.getModel().getK2(), request.getHeader().getUser_id());\n            return AtourResponse.successResponse(true);\n        }catch (Exception e){\n            Loggers.BIZ.error(\"{{=it.func.camel(it.entity.title,true) }}批量更新状态失败\", e);\n            return AtourResponse.errorResponse(\"更新状态失败(\"+e.getMessage()+\")\");\n        }\n    }\n$blankline\n    /**\n     * 批量插入\n     **/ \n    @Override\n    @RestMethod\n    public AtourResponse<List<{{=it.func.camel(it.entity.title,true) }}DTO>> insert(@RequestBody AtourRequest<List<{{=it.func.camel(it.entity.title,true) }}DTO>> request) {\n        RequestChecker checker = RequestChecker.start(request).withUser(request);\n        if (!checker.success()) {\n            return AtourResponse.errorResponse(checker.getError());\n        }\n        TransferUtil<{{=it.func.camel(it.entity.title,true) }}DTO, {{=it.func.camel(it.entity.title,true) }}DO> transfer = new TransferUtil<>(new {{=it.func.camel(it.entity.title,true) }}DTO(), new {{=it.func.camel(it.entity.title,true) }}DO());\n        List<{{=it.func.camel(it.entity.title,true) }}DO> dataList = transfer.convertToDst(request.getModel());\n        for ({{=it.func.camel(it.entity.title,true) }}DO temp : dataList) {\n            if(temp.getDataStatus() == null){\n                temp.setDataStatus(DataStatus.NORMAL);\n            }\n            temp.setCreatedBy(request.getHeader().getUser_id());\n            temp.setModifiedBy(request.getHeader().getUser_id());\n            temp.setTraceId(request.getHeader().getTrace_id());\n        }\n        try {\n            mapper{{=it.func.camel(it.entity.title,true) }}.batchInsert(dataList);\n            manager{{=it.func.camel(it.entity.title,true) }}.afterInsert(dataList);\n        }catch(DuplicateKeyException e){\n            return AtourResponse.errorResponse(\"保存失败，数据已存在！\");\n        }\n        return AtourResponse.successResponse(transfer.convertToSrc(dataList));    \n    }\n$blankline\n    /**\n     * 批量插入并自动更新主键冲突的DataStatus\n     **/ \n    @Override\n    @RestMethod\n    public AtourResponse<Void> batchInsertOrUpdateStatus(@RequestBody AtourRequest<List<{{=it.func.camel(it.entity.title,true) }}DTO>> request) {\n        RequestChecker checker = RequestChecker.start(request).withUser(request);\n        if (!checker.success()) {\n            return AtourResponse.errorResponse(checker.getError());\n        }\n        TransferUtil<{{=it.func.camel(it.entity.title,true) }}DTO, {{=it.func.camel(it.entity.title,true) }}DO> transfer = new TransferUtil<>(new {{=it.func.camel(it.entity.title,true) }}DTO(), new {{=it.func.camel(it.entity.title,true) }}DO());\n        List<{{=it.func.camel(it.entity.title,true) }}DO> dataList = transfer.convertToDst(request.getModel());\n        for ({{=it.func.camel(it.entity.title,true) }}DO temp : dataList) {\n            if(temp.getDataStatus() == null){\n                temp.setDataStatus(DataStatus.NORMAL);\n            }\n            temp.setCreatedBy(request.getHeader().getUser_id());\n            temp.setModifiedBy(request.getHeader().getUser_id());\n            temp.setTraceId(request.getHeader().getTrace_id());\n        }\n        try {\n            mapper{{=it.func.camel(it.entity.title,true) }}.batchInsertOrUpdateStatus(dataList,request.getHeader().getUser_id());\n            manager{{=it.func.camel(it.entity.title,true) }}.afterInsert(dataList);\n        }catch(DuplicateKeyException e){\n            return AtourResponse.errorResponse(\"保存失败，数据已存在！\");\n        }\n        return AtourResponse.successResponse();    \n    }\n$blankline\n    /**\n     * 批量插入并自动更新主键冲突的DataStatus\n     **/ \n    @Override\n    @RestMethod\n    public AtourResponse<Long> insertOrUpdateNonNullField(@RequestBody AtourRequest<{{=it.func.camel(it.entity.title,true) }}DTO> request) {\n        RequestChecker checker = RequestChecker.start(request).withUser(request);\n        if (!checker.success()) {\n            return AtourResponse.errorResponse(checker.getError());\n        }\n        TransferUtil<{{=it.func.camel(it.entity.title,true) }}DTO, {{=it.func.camel(it.entity.title,true) }}DO> transfer = new TransferUtil<>(new {{=it.func.camel(it.entity.title,true) }}DTO(), new {{=it.func.camel(it.entity.title,true) }}DO());\n        {{=it.func.camel(it.entity.title,true) }}DO dataList = transfer.convertToDst(request.getModel());\n        dataList.setCreatedBy(request.getHeader().getUser_id());\n        dataList.setModifiedBy(request.getHeader().getUser_id());\n        dataList.setTraceId(request.getHeader().getTrace_id());\n        if (dataList.getDataStatus() == null) {\n            dataList.setDataStatus(DataStatus.NORMAL);\n        }\n        try {\n            mapper{{=it.func.camel(it.entity.title,true) }}.insertOrUpdateNonNullFields(dataList,request.getHeader().getUser_id());\n        }catch(DuplicateKeyException e){\n            return AtourResponse.errorResponse(\"保存失败，数据已存在！\");\n        }\n        return AtourResponse.successResponse(dataList.getId());    \n    }\n$blankline\n    /**\n     * 批量修改\n     **/ \n    @Override\n    @RestMethod\n    public AtourResponse<List<Long>> updateNonNullFieldsByPrimaryKey(@RequestBody AtourRequest<List<{{=it.func.camel(it.entity.title,true) }}DTO>> request) {\n        RequestChecker checker = RequestChecker.start(request).withUser(request);\n        if (!checker.success()) {\n            return AtourResponse.errorResponse(checker.getError());\n        }\n        TransferUtil<{{=it.func.camel(it.entity.title,true) }}DTO, {{=it.func.camel(it.entity.title,true) }}DO> transfer = new TransferUtil<>(new {{=it.func.camel(it.entity.title,true) }}DTO(), new {{=it.func.camel(it.entity.title,true) }}DO());\n        List<{{=it.func.camel(it.entity.title,true) }}DO> dataList = transfer.convertToDst(request.getModel());\n        List<Long> successIds = new ArrayList<>();\n        try {\n            for ({{=it.func.camel(it.entity.title,true) }}DO temp : dataList) {\n                temp.setCreatedBy(request.getHeader().getUser_id());\n                temp.setModifiedBy(request.getHeader().getUser_id());\n                temp.setTraceId(request.getHeader().getTrace_id());\n                mapper{{=it.func.camel(it.entity.title,true) }}.updateNonNullFieldsByPrimaryKey(temp);\n                manager{{=it.func.camel(it.entity.title,true) }}.afterUpdate(temp);\n                successIds.add(temp.getId());\n            }\n        } catch (Exception e) {\n            Loggers.BIZ.error(\"{{=it.func.camel(it.entity.title,true) }}批量更新失败\", e);\n            return AtourResponse.errorResponse(\"批量保存失败(\"+e.getMessage()+\")\");  \n        }\n        return AtourResponse.successResponse(successIds);    \n    }\n    $blankline\n    /**\n     * 根据条件model更新所有非null字段\n     **/ \n    @Override\n    @RestMethod\n    public AtourResponse<Void> updateDtoByConditionModel(@RequestBody AtourRequest<SimplePairRequest<{{=it.func.camel(it.entity.title,true) }}DTO,{{=it.func.camel(it.entity.title,true) }}MultiRequest>> request) {\n        RequestChecker checker = RequestChecker.start(request).withUser(request);\n        if (!checker.success()) {\n            return AtourResponse.errorResponse(checker.getError());\n        }\n        \n        try {\n            TransferUtil<{{=it.func.camel(it.entity.title,true) }}DTO, {{=it.func.camel(it.entity.title,true) }}DO> transfer = new TransferUtil<>(new {{=it.func.camel(it.entity.title,true) }}DTO(), new {{=it.func.camel(it.entity.title,true) }}DO());\n            mapper{{=it.func.camel(it.entity.title,true) }}.updateNonNullFieldsByNonNullFields(transfer.convertToDst(request.getModel().getK()),request.getModel().getK2(), request.getHeader().getUser_id());\n            return AtourResponse.successResponse();\n        } catch (Exception e) {\n            Loggers.BIZ.error(\"{{=it.func.camel(it.entity.title,true) }}updateDtoByConditionModel 失败\", e);\n            return AtourResponse.errorResponse(\"保存失败(\"+e.getMessage()+\")\");  \n        }\n    }\n}"}, {"code": "RequestQuery", "createTableTemplate": "{{ package_name =\"com.yaduo.demo.service\"; }}{{ dir_path ='{{=package_name }}.api.dto.request';}}code_path=|{{=dir_path }}|package {{=dir_path }};\n\n$blankline\nimport com.yaduo.infras.core.base.bean.RequestList;\nimport com.fasterxml.jackson.annotation.JsonProperty;\n//import io.swagger.annotations.ApiModel;\n//import io.swagger.annotations.ApiModelProperty;\nimport io.swagger.v3.oas.annotations.media.Schema;\nimport lombok.*;\n$blankline\n/**\n * <AUTHOR> process.env.USERNAME || \"\"}}\n * @date {{= new Date().toJSON().slice(0, 19).replace('T',' ')}}\n */\n@Data\n@EqualsAndHashCode(callSuper = false)\n@AllArgsConstructor\n@NoArgsConstructor\n@Builder\n//@ApiModel(value = \"{{=it.func.camel(it.entity.title,true) }}Request\", description = \"列表请求：{{=it.func.join(it.entity.chnname,'',' ')}}{{? it.entity.remark }}{{=it.func.join(';',it.entity.remark,'',' ')}}{{?}}\")\n@Schema(description = \"列表请求：{{=it.func.join(it.entity.chnname,'',' ')}}{{? it.entity.remark }}{{=it.func.join(';',it.entity.remark,'',' ')}}{{?}}\")\npublic class {{=it.func.camel(it.entity.title,true) }}Request extends RequestList {\n    {{~it.entity.fields:field:index}}\n    {{? field.type==\"time\" || field.type==\"date\" || field.type==\"datetime\"  }}\n    /**\n     * {{=it.func.join(field.chnname,field.remark,';','-起')}}\n     */\n    //@ApiModelProperty(value = \"{{=it.func.join(field.chnname,field.remark,';','-起')}}\")\n    @Schema(description = \"{{=it.func.join(field.chnname,field.remark,';','-起')}}\")\n    @JsonProperty(\"{{=field.name}}_start\")\n    private String {{=it.func.camel(field.name,false)}}Start;\n$blankline\n    /**\n     * {{=it.func.join(field.chnname,field.remark,';','-止')}}\n     */    \n    //@ApiModelProperty(value = \"{{=it.func.join(field.chnname,field.remark,';','-止')}}\")\n    @Schema(description = \"{{=it.func.join(field.chnname,field.remark,';','-止')}}\")\n    @JsonProperty(\"{{=field.name}}_end\")\n    private String {{=it.func.camel(field.name,false)}}End;{{??}}\n    /**\n     * {{=it.func.join(field.chnname,field.remark,';')}}\n     */\n    //@ApiModelProperty(value = \"{{=it.func.join(field.chnname,field.remark,';')}}\")\n    @Schema(description = \"{{=it.func.join(field.chnname,field.remark,';')}}\")\n    @JsonProperty(\"{{=field.name}}\")\n    private {{=field.type}} {{=it.func.camel(field.name,false)}};{{?}}\n    {{~}}\n$blankline\n    \n    /**\n     * 自动trim\n     */\n    public void autoTrim(){\n        {{~it.entity.fields:field:index}}{{? field.type==\"String\" }}\n        if (this.{{=it.func.camel(field.name,false)}} != null){\n            this.{{=it.func.camel(field.name,false)}} = this.{{=it.func.camel(field.name,false)}}.trim();\n        }{{?}}{{~}}\n    }\n}"}, {"code": "RequestBatchExacty", "createTableTemplate": "{{ package_name =\"com.yaduo.demo.service\"; }}{{ dir_path ='{{=package_name }}.api.dto.request';}}code_path=|{{=dir_path }}|package {{=dir_path }};\n\n$blankline\nimport com.yaduo.infras.core.base.bean.RequestList;\nimport com.fasterxml.jackson.annotation.JsonProperty;\n//import io.swagger.annotations.ApiModel;\n//import io.swagger.annotations.ApiModelProperty;\nimport io.swagger.v3.oas.annotations.media.Schema;\nimport lombok.*;\nimport java.util.List;\n\n$blankline\n/**\n * <AUTHOR> process.env.USERNAME || \"\"}}\n * @date {{= new Date().toJSON().slice(0, 19).replace('T',' ')}}\n */\n@Data\n@EqualsAndHashCode(callSuper = false)\n@AllArgsConstructor\n@NoArgsConstructor\n@Builder\n//@ApiModel(value = \"{{=it.func.camel(it.entity.title,true) }}MultiRequest\", description = \"列表请求：{{=it.func.join(it.entity.chnname,'',' ')}}{{? it.entity.remark }}{{=it.func.join(';',it.entity.remark,'',' ')}}{{?}}\")\n@Schema(description = \"列表请求：{{=it.func.join(it.entity.chnname,'',' ')}}{{? it.entity.remark }}{{=it.func.join(';',it.entity.remark,'',' ')}}{{?}}\")\npublic class {{=it.func.camel(it.entity.title,true) }}MultiRequest extends RequestList {\n    {{~it.entity.fields:field:index}}\n    {{? field.type==\"time\" || field.type==\"date\" || field.type==\"datetime\"  }}\n    /**\n     * {{=it.func.join(field.chnname,field.remark,';','-起')}}\n     */\n    //@ApiModelProperty(value = \"{{=it.func.join(field.chnname,field.remark,';','-起')}}\")\n    @Schema(description = \"{{=it.func.join(field.chnname,field.remark,';','-起')}}\")\n    @JsonProperty(\"{{=field.name}}_start\")\n    private String {{=it.func.camel(field.name,false)}}Start;\n$blankline\n    /**\n     * {{=it.func.join(field.chnname,field.remark,';','-止')}}\n     */    \n    //@ApiModelProperty(value = \"{{=it.func.join(field.chnname,field.remark,';','-止')}}\")\n    @Schema(description = \"{{=it.func.join(field.chnname,field.remark,';','-止')}}\")\n    @JsonProperty(\"{{=field.name}}_end\")\n    private String {{=it.func.camel(field.name,false)}}End;\n    {{?? field.type.indexOf(\"INT\") >-1 }} /**\n     * {{=it.func.join(field.chnname,field.remark,';')}}\n     */    \n    //@ApiModelProperty(value = \"{{=it.func.join(field.chnname,field.remark,';')}}\")\n    @Schema(description = \"{{=it.func.join(field.chnname,field.remark,';')}}\")\n    @JsonProperty(\"{{=field.name}}\")\n    private List<{{=field.type}}> {{=it.func.camel(field.name,false)}};\n{{??}}\n    /**\n     * {{=it.func.join(field.chnname,field.remark,';')}}\n     */\n    //@ApiModelProperty(value = \"{{=it.func.join(field.chnname,field.remark,';')}}\")\n    @Schema(description = \"{{=it.func.join(field.chnname,field.remark,';')}}\")\n    @JsonProperty(\"{{=field.name}}\")\n    private List<{{=field.type}}> {{=it.func.camel(field.name,false)}};{{?}}\n    {{~}}\n$blankline\n    \n   \n}"}, {"code": "JavaServiceManager", "createTableTemplate": "{{ package_name =\"com.yaduo.demo.service\"; }}{{dir_path ='{{=package_name }}.manager';}}code_path=|{{=dir_path }}|package {{=dir_path }};\n\n$blankline\nimport com.yaduo.infras.core.base.define.BatchUpdateStatusRequest;\nimport {{=package_name}}.api.dto.{{=it.func.camel(it.entity.title,true) }}DTO;\nimport {{=package_name}}.domain.{{=it.func.camel(it.entity.title,true) }}DO;\nimport {{=package_name}}.mapper.{{=it.func.camel(it.entity.title,true) }}Mapper;\nimport org.springframework.stereotype.Component;\n$blankline\nimport javax.annotation.Resource;\nimport java.util.List;\n$blankline\n/**\n * <AUTHOR> process.env.USERNAME || \"\"}}\n * @date {{= new Date().toJSON().slice(0, 19).replace('T',' ')}}\n */\n@Component\npublic class {{=it.func.camel(it.entity.title,true) }}Manager {\n$blankline\n    @Resource\n    private {{=it.func.camel(it.entity.title,true) }}Mapper mapper{{=it.func.camel(it.entity.title,true) }};\n$blankline\n   \n    /**\n     * 批量更新状态\n     **/ \n    public void afterModifyStatus(BatchUpdateStatusRequest request) {\n        \n    }\n$blankline\n    /**\n     * 新增\n     **/ \n    public void afterInsert(List<{{=it.func.camel(it.entity.title,true) }}DO> dataList) {\n           \n    }\n$blankline\n    /**\n     * 修改\n     **/ \n    public void afterUpdate({{=it.func.camel(it.entity.title,true) }}DO data) {\n        \n    }\n}\n"}]}, "profile": {"defaultFields": [{"name": "id", "type": "bigint unsigned", "remark": "", "chnname": "自增主键", "pk": true, "notNull": true, "autoIncrement": true, "relationNoShow": false, "uiHint": "Number"}, {"name": "data_status", "type": "short int", "remark": "0，正常；1，不可用；2，过时；4，已删除", "chnname": "数据状态", "notNull": true, "defaultValue": "0", "relationNoShow": false}, {"name": "created_by", "type": "bigint unsigned", "remark": "", "chnname": "创建人", "defaultValue": "0", "notNull": true, "relationNoShow": true, "uiHint": "Number"}, {"name": "gmt_create", "type": "DATETIME", "remark": "", "chnname": "创建时间", "notNull": true, "relationNoShow": true, "defaultValue": "CURRENT_TIMESTAMP", "uiHint": "DatePicker"}, {"name": "modified_by", "type": "bigint unsigned", "remark": "", "chnname": "更新人", "defaultValue": "0", "notNull": true, "relationNoShow": true, "uiHint": "Number"}, {"name": "gmt_modified", "type": "DATETIME", "remark": "", "chnname": "更新时间", "notNull": true, "relationNoShow": true, "defaultValue": "CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP", "uiHint": "DatePicker"}, {"name": "trace_id", "type": "Intro", "remark": "", "chnname": "TraceID", "relationNoShow": true, "uiHint": "Text"}], "defaultFieldsType": "1", "javaConfig": {"JAVA_HOME": "/Users/<USER>/.sdkman/candidates/java/8.0.191-oracle", "DB_CONNECTOR": ""}, "sqlConfig": "/*SQL@Run*/", "dbs": [{"name": "qa-rms", "defaultDB": false, "properties": {"driver_class_name": "com.mysql.jdbc.Driver", "url": "*****************************************************************************************************", "username": "root", "password": "sr@12345"}}, {"name": "dev-user", "defaultDB": true, "properties": {"driver_class_name": "com.mysql.jdbc.Driver", "url": "***********************************************************************************************************", "username": "root", "password": "sr@12345"}}], "wordTemplateConfig": ""}}