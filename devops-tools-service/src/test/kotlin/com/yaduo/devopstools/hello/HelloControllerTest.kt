package com.yaduo.devopstools.hello

import org.junit.jupiter.api.Test
import org.junit.jupiter.api.Assertions.*

/**
 * Kotlin测试类
 * 验证Kotlin代码能够正常编译和运行
 */
class HelloControllerTest {

    @Test
    fun testKotlinBasicFeatures() {
        // 测试数据类
        val greeting = HelloController.UserGreeting(
            userId = "test123",
            message = "Hello from Kotlin Test!",
            isKotlin = true
        )
        
        assertEquals("test123", greeting.userId)
        assertEquals("Hello from Kotlin Test!", greeting.message)
        assertTrue(greeting.isKotlin)
        assertTrue(greeting.createdAt > 0)
    }

    @Test
    fun testHelloService() {
        val service = HelloService()
        
        // 测试默认参数
        val greeting1 = service.generateGreeting("Alice")
        assertEquals("Hello, Alice! Welcome to Kotlin world!", greeting1)
        
        // 测试空值处理
        val greeting2 = service.generateGreeting(null)
        assertEquals("Hello, Anonymous! Welcome to Kotlin world!", greeting2)
        
        // 测试空白字符串处理
        val greeting3 = service.generateGreeting("   ")
        assertEquals("Hello, Anonymous! Welcome to Kotlin world!", greeting3)
    }

    @Test
    fun testValidation() {
        val service = HelloService()
        
        // 测试有效名称
        val validResult = service.validateUserName("Alice")
        assertTrue(validResult.isValid)
        assertEquals("Valid name", validResult.message)
        
        // 测试无效名称
        val invalidResult1 = service.validateUserName("")
        assertFalse(invalidResult1.isValid)
        assertEquals("Name cannot be empty", invalidResult1.message)
        
        val invalidResult2 = service.validateUserName("A")
        assertFalse(invalidResult2.isValid)
        assertEquals("Name must be at least 2 characters", invalidResult2.message)
    }

    @Test
    fun testSystemInfo() {
        val service = HelloService()
        val systemInfo = service.getSystemInfo()
        
        assertNotNull(systemInfo.currentTime)
        assertNotNull(systemInfo.javaVersion)
        assertNotNull(systemInfo.kotlinVersion)
        assertNotNull(systemInfo.osName)
        assertTrue(systemInfo.availableProcessors > 0)
        
        println("System Info: $systemInfo")
    }
}
