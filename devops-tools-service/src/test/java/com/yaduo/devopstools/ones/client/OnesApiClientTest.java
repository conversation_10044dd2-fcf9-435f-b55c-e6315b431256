package com.yaduo.devopstools.ones.client;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.yaduo.devopstools.ones.model.TokenInfo.User;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * <AUTHOR>
 * @date 2022-05-27 17:32
 */
class OnesApiClientTest {
    OnesApiClient client;

    @BeforeEach
    void setUp() {
        client = new OnesApiClient("ones-uid=7ETCtS1J; ones-lt=VXQF0SKgbqZheEoXQ8t9wB7dCQKnpyObdaEok3yfN2mH7Ws5Kfz4jl7ubeK3cxWu; sensorsdata2015jssdkcross=%7B%22distinct_id%22%3A%227ETCtS1J%22%2C%22first_id%22%3A%2217f3f957664b15-0c62915ddcc26-3962720e-2073600-17f3f957665a63%22%2C%22props%22%3A%7B%22%24latest_traffic_source_type%22%3A%22%E7%9B%B4%E6%8E%A5%E6%B5%81%E9%87%8F%22%2C%22%24latest_search_keyword%22%3A%22%E6%9C%AA%E5%8F%96%E5%88%B0%E5%80%BC_%E7%9B%B4%E6%8E%A5%E6%89%93%E5%BC%80%22%2C%22%24latest_referrer%22%3A%22%22%7D%2C%22%24device_id%22%3A%2217f3f957664b15-0c62915ddcc26-3962720e-2073600-17f3f957665a63%22%7D;  ct=1f7f7b2fefbef14293da9130431d5fefae9d96831da0b201a28f61e0a5b43bd3");
    }

    @Test
    void users() {
        List<User> users = client.users();
        System.out.println(JSON.toJSONString(users, true));
    }
}