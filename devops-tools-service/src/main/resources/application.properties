#spring-boot
##指定微服务的名称后续在调用的时候只需要使用该名称就可以进行服务的访问
app.id=devops-tools
apollo.bootstrap.enabled=true
env=DEV
apollo.meta=https://qa-apollo-config.at-our.com

application_prefix=
spring.application.name=${application_prefix}${app.id}
##配置端口号
#server.port=${app.id}
#springdoc.swagger-ui.path=/doc.html
#上报周期
eureka.client.instance-info-replication-interval-seconds=40
#eureka client刷新本地缓存时间,默认30s
eureka.client.registry-fetch-interval-seconds=10
eureka.client.service-url.defaultZone=https://qa-eureka.at-our.com/eureka
eureka.client.healthcheck.enabled=true
##应用程序向eureka注册时，使用IP地址而不是其主机名
eureka.instance.preferIpAddress=true
# zipkin
#spring.zipkin.enabled=true

#默认30s
ribbon.ServerListRefreshInterval=10000
server.servlet.context-parameters.appName=${spring.application.name}
spring.jackson.default-property-inclusion=non_null


redis.maxIdle=300
redis.testOnBorrow=true
debug_url_9901=

spring.servlet.multipart.enabled=true
spring.servlet.multipart.file-size-threshold=0
spring.servlet.multipart.max-file-size=1000MB
spring.servlet.multipart.max-request-size=1000MB

# 可以转发doc.html
server.forward-headers-strategy=framework
# 暴露健康、信息和下线接口
management.endpoints.web.exposure.include=*
management.endpoint.health.enabled=true
management.health.elasticsearch.enabled=false
management.endpoint.health.show-details=always
management.endpoint.info.enabled=true
management.endpoint.shutdown.enabled=true

mysql.driver=com.mysql.cj.jdbc.Driver
mysql.devops-tools.url=****************************************************************************************************************************************************************
mysql.devops-tools.username=yaduodb
mysql.devops-tools.password=$^mvObKz6mIfnsWbBd6vYVjl
mysql.devops-tools.initialSize=0
mysql.devops-tools.maxActive=20
mysql.devops-tools.minIdle=5
mysql.devops-tools.maxWait=6000

ones.cookie=language=zh; ones-lang=zh; ones-tz=Asia%2FShanghai; ones-region-uuid=default; ones-org-uuid=U8co1EDY; service_ticket_OMS=2120769ebe8049cd9af8591f5889055f; timezone=Asia/Shanghai; hotelManageLoginCookie=e4ae5383fad8491fa5cc54682e51c4f0; asso_token=f9222a1cfe2a43b8b662c26e8b88ac46; asso_token_change_flag=0334920614573060bad16c13b2d317f1; atour-token=f9222a1cfe2a43b8b662c26e8b88ac46; service_ticket_HLM=a99b917a3e4949e092cbd5ba9f4ae92b; global_token=0716bb282edd4d388e458d3f851a00ce; service_ticket_RBAC=0716bb282edd4d388e458d3f851a00ce; ones-ids-sid=ff46ef75-3833-47f8-7dda-23be64fc6a5c; ones-lt=eyJhbGciOiJSUzI1NiIsImtpZCI6ImU0NTU2MzMzLWQ4MzYtNDM0NC02ZjU4LWUwODg1YTcwZjcyZCIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.16q65ucr6O9aVs2w5j1SLFRYpJeKn015QCjbTFuXE8a2ta1UZuB8Q2dRrrz6mqQCmPYeGdmXB0vkf05EhQPMcfpw4jncZMK8RWxLUGGmelkZt25ioPItE_dmBhwjEjPO3W6IgOgZyl8WjEqsJhTntQAtlR6ir8d2rpNZkjjiBXyxWZDWm-GTQcKsqrhRuGn0fkv5VfaPPCi_t_SqxoeAkF1JO4ATq628tVR9OwihF634W22hP_uYMR-ASxqk1jrRLFDDFH-75TwoL8AH-R9tTSRywKZHXz4D1o1c8VKPoXszOerIQcpyvYSiz0yxxB0qb8HuXJSSNHfor6y-bQg2Ew; ct=54491bd146243c3338d052b9d7a44103cae00a9e14664cd69ca3569b823b9f92; SERVERID=09fff2ecdbb6fdc29005cc765d12142d|1734679690|1734658627

application-api-name=application-center-beta
#spring.mvc.format.date-time=yyyy-MM-dd HH:mm:ss
spring.jackson.date-format=yyyy-MM-dd HH:mm:ss
logging.level.root=info

fastdep.redis.redis-asso.database=1
fastdep.redis.redis-asso.host=qa-redis.at-our.com
fastdep.redis.redis-asso.port=6379
fastdep.redis.redis-asso.password=Atour2017
fastdep.redis.redis-asso.lettuce.shutdown-timeout=100
fastdep.redis.redis-asso.lettuce.pool.max-active=18
fastdep.redis.redis-asso.lettuce.pool.max-idle=8
fastdep.redis.redis-asso.lettuce.pool.max-wait=30
fastdep.redis.redis-asso.lettuce.pool.min-idle=0

