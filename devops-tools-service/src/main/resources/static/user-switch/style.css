/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* 头部样式 */
.header {
    text-align: center;
    margin-bottom: 40px;
    color: white;
}

.header h1 {
    font-size: 2.5rem;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.header .subtitle {
    font-size: 1.1rem;
    opacity: 0.9;
}

/* 主内容区域 */
.main-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
    margin-bottom: 30px;
}

/* 选择区域样式 */
.selection-area {
    background: white;
    padding: 30px;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.form-group {
    margin-bottom: 25px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #555;
    font-size: 0.95rem;
}

.form-group label i {
    margin-right: 8px;
    color: #667eea;
}

/* 搜索容器 */
.search-container {
    position: relative;
}

.search-container input {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #e1e5e9;
    border-radius: 8px;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.search-container input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* 搜索结果 */
.search-results {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid #e1e5e9;
    border-top: none;
    border-radius: 0 0 8px 8px;
    max-height: 200px;
    overflow-y: auto;
    z-index: 1000;
    display: none;
}

.search-result-item {
    padding: 12px 16px;
    cursor: pointer;
    border-bottom: 1px solid #f0f0f0;
    transition: background-color 0.2s ease;
}

.search-result-item:hover {
    background-color: #f8f9fa;
}

.search-result-item:last-child {
    border-bottom: none;
}

.user-name {
    font-weight: 600;
    color: #333;
}

.user-details {
    font-size: 0.85rem;
    color: #666;
    margin-top: 2px;
}

/* 选择框样式 */
select {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #e1e5e9;
    border-radius: 8px;
    font-size: 1rem;
    background-color: white;
    cursor: pointer;
    transition: all 0.3s ease;
}

select:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* 输入框样式 */
input[type="text"] {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #e1e5e9;
    border-radius: 8px;
    font-size: 1rem;
    transition: all 0.3s ease;
}

input[type="text"]:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* 添加按钮 */
.add-button {
    width: 100%;
    padding: 15px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.add-button:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.add-button:disabled {
    background: #ccc;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

/* 功能区域样式 */
.function-area {
    background: white;
    padding: 30px;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.function-area h2 {
    margin-bottom: 25px;
    color: #333;
    font-size: 1.5rem;
}

.function-area h2 i {
    margin-right: 10px;
    color: #667eea;
}

/* 按钮容器 */
.buttons-container {
    min-height: 200px;
}

.empty-state {
    text-align: center;
    color: #999;
    padding: 60px 20px;
}

.empty-state i {
    font-size: 3rem;
    margin-bottom: 15px;
    opacity: 0.5;
}

.empty-state p {
    margin-bottom: 5px;
}

.empty-state .hint {
    font-size: 0.9rem;
    opacity: 0.7;
}

/* 快捷按钮样式 */
.shortcut-button {
    display: inline-block;
    margin: 8px;
    padding: 12px 20px;
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    color: white;
    border: none;
    border-radius: 25px;
    font-size: 0.95rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    text-decoration: none;
    box-shadow: 0 4px 15px rgba(79, 172, 254, 0.3);
    min-width: 120px;
}

.button-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 2px;
}

.button-name {
    font-weight: 600;
}

.button-remark {
    font-size: 0.75rem;
    opacity: 0.8;
    font-weight: 400;
    font-style: italic;
}

.shortcut-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(79, 172, 254, 0.4);
}

.shortcut-button .button-actions {
    position: absolute;
    top: -8px;
    right: -8px;
    display: none;
}

.shortcut-button:hover .button-actions {
    display: block;
}

.action-btn {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    border: none;
    margin-left: 4px;
    cursor: pointer;
    font-size: 0.7rem;
    transition: all 0.2s ease;
}

.edit-btn {
    background: #ffc107;
    color: white;
}

.delete-btn {
    background: #dc3545;
    color: white;
}

.action-btn:hover {
    transform: scale(1.1);
}

/* 加载覆盖层 */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.loading-spinner {
    background: white;
    padding: 30px;
    border-radius: 10px;
    text-align: center;
}

.loading-spinner i {
    font-size: 2rem;
    color: #667eea;
    margin-bottom: 15px;
}

/* 消息提示 */
.message-toast {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 15px 20px;
    border-radius: 8px;
    color: white;
    font-weight: 500;
    z-index: 10000;
    transform: translateX(400px);
    transition: transform 0.3s ease;
}

.message-toast.show {
    transform: translateX(0);
}

.message-toast.success {
    background: #28a745;
}

.message-toast.error {
    background: #dc3545;
}

.message-toast.warning {
    background: #ffc107;
    color: #333;
}

/* 模态框样式 */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 10000;
}

.modal-content {
    background: white;
    border-radius: 10px;
    width: 90%;
    max-width: 500px;
    max-height: 90vh;
    overflow-y: auto;
}

.modal-header {
    padding: 20px;
    border-bottom: 1px solid #e1e5e9;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    margin: 0;
    color: #333;
}

.close-btn {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: #999;
}

.modal-body {
    padding: 20px;
}

.modal-footer {
    padding: 20px;
    border-top: 1px solid #e1e5e9;
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

.btn-primary, .btn-secondary {
    padding: 10px 20px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-weight: 500;
}

.btn-primary {
    background: #667eea;
    color: white;
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

/* URL提示样式 */
.url-hint {
    display: block;
    margin-top: 5px;
    font-size: 0.8rem;
    color: #666;
    font-style: italic;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .main-content {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .header h1 {
        font-size: 2rem;
    }
    
    .selection-area,
    .function-area {
        padding: 20px;
    }
}
