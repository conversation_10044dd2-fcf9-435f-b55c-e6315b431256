// 全局变量
let selectedUser = null;
let searchTimeout = null;
let shortcutButtons = [];
let currentEditingButton = null;

// DOM 元素
const userSearchInput = document.getElementById('userSearch');
const searchResults = document.getElementById('searchResults');
const systemSelect = document.getElementById('systemSelect');
const buttonNameInput = document.getElementById('buttonName');
const buttonRemarkInput = document.getElementById('buttonRemark');
const addButton = document.getElementById('addButton');
const buttonsContainer = document.getElementById('buttonsContainer');
const loadingOverlay = document.getElementById('loadingOverlay');
const messageToast = document.getElementById('messageToast');
const editModal = document.getElementById('editModal');
const editButtonName = document.getElementById('editButtonName');
const editRedirectUrl = document.getElementById('editRedirectUrl');
const editButtonRemark = document.getElementById('editButtonRemark');

// 初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeEventListeners();
    loadSavedButtons();
    loadSystemOptions();
});

// 初始化事件监听器
function initializeEventListeners() {
    // 用户搜索
    userSearchInput.addEventListener('input', handleUserSearch);
    userSearchInput.addEventListener('blur', () => {
        setTimeout(() => hideSearchResults(), 200);
    });

    // 系统选择和按钮名称变化
    systemSelect.addEventListener('change', updateAddButtonState);
    buttonNameInput.addEventListener('input', updateAddButtonState);
    buttonRemarkInput.addEventListener('input', updateAddButtonState);

    // 添加按钮
    addButton.addEventListener('click', handleAddButton);

    // 模态框事件
    document.getElementById('closeModal').addEventListener('click', closeEditModal);
    document.getElementById('cancelEdit').addEventListener('click', closeEditModal);
    document.getElementById('saveEdit').addEventListener('click', saveButtonEdit);

    // 点击模态框外部关闭
    editModal.addEventListener('click', (e) => {
        if (e.target === editModal) {
            closeEditModal();
        }
    });
}

// 处理用户搜索
function handleUserSearch() {
    const keyword = userSearchInput.value.trim();
    
    if (searchTimeout) {
        clearTimeout(searchTimeout);
    }

    if (keyword.length < 2) {
        hideSearchResults();
        selectedUser = null;
        updateAddButtonState();
        return;
    }

    searchTimeout = setTimeout(() => {
        searchUsers(keyword);
    }, 300);
}

// 搜索用户
async function searchUsers(keyword) {
    try {
        showLoading(true);
        
        // 模拟API调用 - 实际环境中替换为真实API
        const response = await fetch(`/api/user/query-kt?keyword=${encodeURIComponent(keyword)}`);
        
        if (!response.ok) {
            throw new Error('搜索失败');
        }

        const users = await response.json();
        displaySearchResults(users || []);
        
    } catch (error) {
        console.error('搜索用户失败:', error);
        // 使用模拟数据
        const mockUsers = generateMockUsers(keyword);
        displaySearchResults(mockUsers);
    } finally {
        showLoading(false);
    }
}

// 生成模拟用户数据
function generateMockUsers(keyword) {
    const mockUsers = [
        { userName: 'admin', employeeId: 'E001', aliasKeyWord: '管理员', flowerName: '管理员' },
        { userName: 'test001', employeeId: 'E002', aliasKeyWord: '测试用户1', flowerName: '小测' },
        { userName: 'dev001', employeeId: 'E003', aliasKeyWord: '开发用户1', flowerName: '小开' },
        { userName: 'manager', employeeId: 'E004', aliasKeyWord: '经理', flowerName: '老板' },
        { userName: 'operator', employeeId: 'E005', aliasKeyWord: '操作员', flowerName: '小操' }
    ];

    return mockUsers.filter(user =>
        user.userName.toLowerCase().includes(keyword.toLowerCase()) ||
        user.employeeId.toLowerCase().includes(keyword.toLowerCase()) ||
        user.aliasKeyWord.toLowerCase().includes(keyword.toLowerCase()) ||
        (user.flowerName && user.flowerName.toLowerCase().includes(keyword.toLowerCase()))
    );
}

// 显示搜索结果
function displaySearchResults(users) {
    if (users.length === 0) {
        hideSearchResults();
        return;
    }

    const resultsHtml = users.map(user => {
        // 优先使用flowerName，如果没有则使用aliasKeyWord作为备选
        const displayName = user.flowerName || user.aliasKeyWord || '未知';
        return `
            <div class="search-result-item" onclick="selectUser('${user.userName}', '${user.employeeId}', '${user.aliasKeyWord || ''}', '${user.flowerName || ''}')">
                <div class="user-name">${user.userName}</div>
                <div class="user-details">员工ID: ${user.employeeId} | 花名: ${displayName}</div>
            </div>
        `;
    }).join('');

    searchResults.innerHTML = resultsHtml;
    searchResults.style.display = 'block';
}

// 隐藏搜索结果
function hideSearchResults() {
    searchResults.style.display = 'none';
}

// 选择用户
function selectUser(userName, employeeId, aliasKeyWord, flowerName) {
    selectedUser = { userName, employeeId, aliasKeyWord, flowerName };
    // 优先显示flowerName，如果没有则使用aliasKeyWord
    const displayName = flowerName || aliasKeyWord || '未知';
    userSearchInput.value = `${userName} (${displayName})`;
    hideSearchResults();
    updateAddButtonState();
}

// 更新添加按钮状态
function updateAddButtonState() {
    const hasUser = selectedUser !== null;
    const hasSystem = systemSelect.value !== '';
    
    addButton.disabled = !(hasUser && hasSystem);
}

// 处理添加按钮点击
function handleAddButton() {
    if (!selectedUser || !systemSelect.value) {
        showMessage('请选择用户和系统', 'warning');
        return;
    }

    const selectedOption = systemSelect.options[systemSelect.selectedIndex];
    // 优先使用花名，如果没有则使用别名
    const userDisplayName = selectedUser.flowerName || selectedUser.aliasKeyWord || selectedUser.userName;
    const buttonName = buttonNameInput.value.trim() ||
                      `${userDisplayName} - ${selectedOption.text}`;
    const buttonRemark = buttonRemarkInput.value.trim();

    const shortcutButton = {
        id: Date.now(),
        name: buttonName,
        remark: buttonRemark,
        user: selectedUser,
        system: systemSelect.value,
        systemName: selectedOption.text,
        systemHomePage: selectedOption.getAttribute('data-homepage') || '',
        redirectUrl: ''
    };

    shortcutButtons.push(shortcutButton);
    saveButtonsToStorage();
    renderButtons();
    clearForm();
    showMessage('快捷按钮添加成功', 'success');
}

// 清空表单
function clearForm() {
    userSearchInput.value = '';
    systemSelect.value = '';
    buttonNameInput.value = '';
    buttonRemarkInput.value = '';
    selectedUser = null;
    updateAddButtonState();
}

// 渲染按钮
function renderButtons() {
    if (shortcutButtons.length === 0) {
        buttonsContainer.innerHTML = `
            <div class="empty-state">
                <i class="fas fa-mouse-pointer"></i>
                <p>暂无快捷按钮</p>
                <p class="hint">请在上方添加用户和系统组合</p>
            </div>
        `;
        return;
    }

    const buttonsHtml = shortcutButtons.map(button => `
        <div class="shortcut-button" onclick="handleQuickLogin('${button.id}')" title="${button.remark || '点击一键登录'}">
            <div class="button-content">
                <span class="button-name">${button.name}</span>
                ${button.remark ? `<small class="button-remark">${button.remark}</small>` : ''}
            </div>
            <div class="button-actions">
                <button class="action-btn edit-btn" onclick="event.stopPropagation(); editButton('${button.id}')" title="编辑">
                    <i class="fas fa-edit"></i>
                </button>
                <button class="action-btn delete-btn" onclick="event.stopPropagation(); deleteButton('${button.id}')" title="删除">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        </div>
    `).join('');

    buttonsContainer.innerHTML = buttonsHtml;
}

// 处理快速登录
async function handleQuickLogin(buttonId) {
    const button = shortcutButtons.find(b => b.id == buttonId);
    if (!button) return;

    try {
        showLoading(true);
        showMessage('正在登录...', 'warning');

        // 构建登录URL
        const loginUrl = `/user_switch_kt/${button.system}/${button.user.userName}`;

        // 确定重定向URL的优先级：自定义URL > 系统主页 > 无重定向
        let redirectUrl = button.redirectUrl;
        if (!redirectUrl && button.systemHomePage) {
            redirectUrl = button.systemHomePage;
        }

        const redirectParam = redirectUrl ? `?url=${encodeURIComponent(redirectUrl)}` : '';

        // 在新标签页中打开
        const newWindow = window.open(loginUrl + redirectParam, '_blank');

        if (newWindow) {
            showMessage('登录请求已发送，请查看新标签页', 'success');
        } else {
            showMessage('请允许弹出窗口以完成登录', 'warning');
        }

    } catch (error) {
        console.error('登录失败:', error);
        showMessage('登录失败: ' + error.message, 'error');
    } finally {
        showLoading(false);
    }
}

// 编辑按钮
function editButton(buttonId) {
    const button = shortcutButtons.find(b => b.id == buttonId);
    if (!button) return;

    console.log('编辑按钮数据:', button); // 调试信息

    currentEditingButton = button;
    editButtonName.value = button.name;
    editButtonRemark.value = button.remark || '';

    // 回显跳转URL：优先显示自定义URL，如果没有则显示系统主页
    let displayUrl = '';
    let placeholderText = '自定义跳转URL，留空则使用系统默认主页';

    if (button.redirectUrl) {
        // 如果有自定义URL，显示自定义URL
        displayUrl = button.redirectUrl;
    } else if (button.systemHomePage) {
        // 如果没有自定义URL但有系统主页，显示系统主页
        displayUrl = button.systemHomePage;
        placeholderText = `当前使用系统主页: ${button.systemHomePage}`;
    }

    editRedirectUrl.value = displayUrl;
    editRedirectUrl.placeholder = placeholderText;

    console.log('回显URL:', displayUrl, '占位符:', placeholderText); // 调试信息

    editModal.style.display = 'flex';
}

// 删除按钮
function deleteButton(buttonId) {
    if (confirm('确定要删除这个快捷按钮吗？')) {
        shortcutButtons = shortcutButtons.filter(b => b.id != buttonId);
        saveButtonsToStorage();
        renderButtons();
        showMessage('快捷按钮已删除', 'success');
    }
}

// 关闭编辑模态框
function closeEditModal() {
    editModal.style.display = 'none';
    currentEditingButton = null;
}

// 保存按钮编辑
function saveButtonEdit() {
    if (!currentEditingButton) return;

    const newName = editButtonName.value.trim();
    if (!newName) {
        showMessage('按钮名称不能为空', 'warning');
        return;
    }

    const newRedirectUrl = editRedirectUrl.value.trim();
    const newRemark = editButtonRemark.value.trim();
    console.log('保存编辑 - 新名称:', newName, '新URL:', newRedirectUrl, '新备注:', newRemark); // 调试信息

    currentEditingButton.name = newName;
    currentEditingButton.redirectUrl = newRedirectUrl;
    currentEditingButton.remark = newRemark;

    console.log('更新后的按钮数据:', currentEditingButton); // 调试信息

    saveButtonsToStorage();
    renderButtons();
    closeEditModal();
    showMessage('按钮更新成功', 'success');
}

// 保存按钮到本地存储
function saveButtonsToStorage() {
    localStorage.setItem('userSwitchButtons', JSON.stringify(shortcutButtons));
}

// 从本地存储加载按钮
function loadSavedButtons() {
    const saved = localStorage.getItem('userSwitchButtons');
    if (saved) {
        try {
            shortcutButtons = JSON.parse(saved);
            renderButtons();
        } catch (error) {
            console.error('加载保存的按钮失败:', error);
            shortcutButtons = [];
        }
    }
}

// 显示/隐藏加载状态
function showLoading(show) {
    loadingOverlay.style.display = show ? 'flex' : 'none';
}

// 加载系统选项
async function loadSystemOptions() {
    try {
        showLoading(true);

        const response = await fetch('/api/system/list');
        if (!response.ok) {
            throw new Error('获取系统列表失败');
        }

        const result = await response.json();
        if (result.success && result.data) {
            populateSystemSelect(result.data);
        } else {
            throw new Error(result.message || '获取系统列表失败');
        }

    } catch (error) {
        console.error('加载系统列表失败:', error);
        // 使用默认系统列表作为备选
        const defaultSystems = [
            { system: 'HLM', homePage: 'https://qa-hlm.corp.at-our.com' },
            { system: 'PMS', homePage: 'https://qa-pms.corp.at-our.com' },
            { system: 'CRS', homePage: 'https://qa-crs.corp.at-our.com' },
            { system: 'OTA', homePage: 'https://qa-ota.corp.at-our.com' },
            { system: 'FINANCE', homePage: 'https://qa-finance.corp.at-our.com' },
            { system: 'HR', homePage: 'https://qa-hr.corp.at-our.com' },
            { system: 'ADMIN', homePage: 'https://qa-admin.corp.at-our.com' }
        ];
        populateSystemSelect(defaultSystems);
        showMessage('使用默认系统列表', 'warning');
    } finally {
        showLoading(false);
    }
}

// 填充系统选择框
function populateSystemSelect(systems) {
    // 清空现有选项（保留默认选项）
    systemSelect.innerHTML = '<option value="">请选择系统</option>';

    // 添加从数据库获取的系统选项
    systems.forEach(system => {
        const option = document.createElement('option');
        option.value = system.system;
        option.textContent = `${system.system}`;
        option.setAttribute('data-homepage', system.homePage || '');
        systemSelect.appendChild(option);
    });

    console.log(`已加载 ${systems.length} 个系统选项`);
}

// 获取选中系统的主页地址
function getSelectedSystemHomePage() {
    const selectedOption = systemSelect.options[systemSelect.selectedIndex];
    return selectedOption ? selectedOption.getAttribute('data-homepage') || '' : '';
}

// 显示消息提示
function showMessage(message, type = 'success') {
    messageToast.textContent = message;
    messageToast.className = `message-toast ${type}`;
    messageToast.classList.add('show');

    setTimeout(() => {
        messageToast.classList.remove('show');
    }, 3000);
}
