<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户切换工具</title>
    <link rel="stylesheet" href="style.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container">
        <header class="header">
            <h1><i class="fas fa-user-cog"></i> 用户切换工具</h1>
            <p class="subtitle">选择用户和系统，一键自动登录并跳转</p>
        </header>

        <div class="main-content">
            <!-- 用户选择区域 -->
            <div class="selection-area">
                <div class="form-group">
                    <label for="userSearch">
                        <i class="fas fa-search"></i> 用户搜索
                    </label>
                    <div class="search-container">
                        <input type="text" 
                               id="userSearch" 
                               placeholder="输入用户名、员工ID或别名进行搜索..."
                               autocomplete="off">
                        <div class="search-results" id="searchResults"></div>
                    </div>
                </div>

                <div class="form-group">
                    <label for="systemSelect">
                        <i class="fas fa-desktop"></i> 系统选择
                    </label>
                    <select id="systemSelect">
                        <option value="">请选择系统</option>
                        <option value="HLM">酒店生命周期管理系统</option>
                        <option value="PMS">酒店管理系统</option>
                        <option value="CRS">中央预订系统</option>
                        <option value="OTA">在线旅行代理系统</option>
                        <option value="FINANCE">财务系统</option>
                        <option value="HR">人力资源系统</option>
                        <option value="ADMIN">后台管理系统</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="buttonName">
                        <i class="fas fa-tag"></i> 按钮名称
                    </label>
                    <input type="text"
                           id="buttonName"
                           placeholder="自定义按钮名称（可选）">
                </div>

                <div class="form-group">
                    <label for="buttonRemark">
                        <i class="fas fa-comment"></i> 备注
                    </label>
                    <input type="text"
                           id="buttonRemark"
                           placeholder="添加备注信息（可选）">
                </div>

                <button class="add-button" id="addButton" disabled>
                    <i class="fas fa-plus"></i> 添加快捷按钮
                </button>
            </div>

            <!-- 功能模块区域 -->
            <div class="function-area">
                <h2><i class="fas fa-rocket"></i> 快捷登录</h2>
                <div class="buttons-container" id="buttonsContainer">
                    <div class="empty-state">
                        <i class="fas fa-mouse-pointer"></i>
                        <p>暂无快捷按钮</p>
                        <p class="hint">请在上方添加用户和系统组合</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 加载提示 -->
        <div class="loading-overlay" id="loadingOverlay">
            <div class="loading-spinner">
                <i class="fas fa-spinner fa-spin"></i>
                <p>正在处理...</p>
            </div>
        </div>

        <!-- 消息提示 -->
        <div class="message-toast" id="messageToast"></div>
    </div>

    <!-- 编辑按钮模态框 -->
    <div class="modal" id="editModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3><i class="fas fa-edit"></i> 编辑按钮</h3>
                <button class="close-btn" id="closeModal">&times;</button>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label for="editButtonName">按钮名称</label>
                    <input type="text" id="editButtonName" placeholder="输入新的按钮名称">
                </div>
                <div class="form-group">
                    <label for="editRedirectUrl">
                        <i class="fas fa-link"></i> 跳转URL（可选，默认使用系统主页）
                    </label>
                    <input type="text" id="editRedirectUrl" placeholder="自定义跳转URL，留空则使用系统默认主页">
                    <small class="url-hint">当前显示的是实际使用的跳转地址</small>
                </div>
                <div class="form-group">
                    <label for="editButtonRemark">
                        <i class="fas fa-comment"></i> 备注
                    </label>
                    <input type="text" id="editButtonRemark" placeholder="添加备注信息（可选）">
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn-secondary" id="cancelEdit">取消</button>
                <button class="btn-primary" id="saveEdit">保存</button>
            </div>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>
