<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:aop="http://www.springframework.org/schema/aop"
       xmlns="http://www.springframework.org/schema/beans"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
       http://www.springframework.org/schema/beans/spring-beans.xsd
       http://www.springframework.org/schema/aop
       http://www.springframework.org/schema/aop/spring-aop-4.0.xsd">
    <aop:aspectj-autoproxy/>
    <!-- <context:property-placeholder location="classpath:application.properties"/> -->

    <bean id="AspectUtil" class="com.yaduo.infras.core.logging.aspect.LoggingHandler"/>

    <aop:config>
        <aop:aspect id="userAccess" ref="AspectUtil" order="-1">
            <aop:around method="handleRestMethod"
                        pointcut="(execution(* com.yaduo.devopstools.*.controller..*.*(..))) and
                        (@annotation(restMethod))"/>

        </aop:aspect>
    </aop:config>


</beans>