<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns="http://www.springframework.org/schema/beans"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
           http://www.springframework.org/schema/beans/spring-beans.xsd
           http://www.springframework.org/schema/context
           http://www.springframework.org/schema/context/spring-context-4.2.xsd">

     <context:property-placeholder location="classpath:application.properties"/>

    <bean id="dataSourceDevopsTools" class="com.alibaba.druid.pool.DruidDataSource" init-method="init" destroy-method="close">
        <property name="driverClassName" value="${mysql.driver}"/>
        <property name="url" value="${mysql.devops-tools.url}"/>
        <property name="username" value="${mysql.devops-tools.username}"/>
        <property name="password" value="${mysql.devops-tools.password}"/>
        <property name="initialSize" value="${mysql.devops-tools.initialSize}"/>
        <property name="maxActive" value="${mysql.devops-tools.maxActive}"/>
        <property name="minIdle" value="${mysql.devops-tools.minIdle}"/>
        <property name="maxWait" value="${mysql.devops-tools.maxWait}"/>
        <property name="filters" value="stat"/>
    </bean>

    <bean id="sqlSessionFactoryDevopsTools" class="org.mybatis.spring.SqlSessionFactoryBean">
        <property name="dataSource" ref="dataSourceDevopsTools"/>
        <!--mapping.xml 文件 -->
        <property name="mapperLocations" value="classpath:mapper/devopstools/*.xml"/>
<!--        <property name="configLocation" value="classpath:mybatis-config.xml"/>-->
        <property name="plugins">
            <array>
                <bean id="mybatisInterceptor" class="com.yaduo.infras.core.logging.mybatis.SQLExecInterceptor"/>
            </array>
        </property>
    </bean>

    <!-- mapper接口所在包名 -->
    <bean class="org.mybatis.spring.mapper.MapperScannerConfigurer">
        <property name="basePackage" value="com.yaduo.devopstools.app.mapper,com.yaduo.devopstools.system.mapper"/>
        <property name="sqlSessionFactoryBeanName" value="sqlSessionFactoryDevopsTools"/>
    </bean>

</beans>