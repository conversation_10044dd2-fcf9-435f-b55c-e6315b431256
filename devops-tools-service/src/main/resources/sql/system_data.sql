-- 系统表创建和测试数据
-- 创建系统表
CREATE TABLE IF NOT EXISTS `system`
(
    id        int auto_increment comment 'Id'
        primary key,
    `system`  varchar(20)  not null comment '系统code',
    home_page varchar(200) not null comment '主页地址'
)
    comment '系统';

-- 插入测试数据
INSERT INTO `system` (`system`, home_page) VALUES 
('HLM', 'https://qa-hlm.corp.at-our.com'),
('PMS', 'https://qa-pms.corp.at-our.com'),
('CRS', 'https://qa-crs.corp.at-our.com'),
('OTA', 'https://qa-ota.corp.at-our.com'),
('FINANCE', 'https://qa-finance.corp.at-our.com'),
('HR', 'https://qa-hr.corp.at-our.com'),
('ADMIN', 'https://qa-admin.corp.at-our.com'),
('ASSO', 'https://qa-asso.corp.at-our.com'),
('RBAC', 'https://qa-rbac.corp.at-our.com'),
('ONES', 'https://ones.corp.yaduo.com')
ON DUPLICATE KEY UPDATE 
    home_page = VALUES(home_page);
