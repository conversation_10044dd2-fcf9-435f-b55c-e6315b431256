<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yaduo.devopstools.role.mapper.RoleMapper">
    <resultMap type="com.yaduo.devopstools.role.domain.RoleDO" id="BaseResultMap">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="role_name" property="roleName" jdbcType="VARCHAR"/>
        <result column="data_status" property="dataStatus" jdbcType="INTEGER"/>
        <result column="created_by" property="createdBy" jdbcType="BIGINT"/>
        <result column="gmt_create" property="gmtCreate" jdbcType="TIMESTAMP"/>
        <result column="modified_by" property="modifiedBy" jdbcType="BIGINT"/>
        <result column="gmt_modified" property="gmtModified" jdbcType="TIMESTAMP"/>
        <result column="trace_id" property="traceId" jdbcType="VARCHAR"/>
    </resultMap>
    <sql id="Base_Column_List">
        `id`,
        `role_name`,
        `data_status`,
        `created_by`,
        `gmt_create`,
        `modified_by`,
        `gmt_modified`,
        `trace_id`
    </sql>

    <update id="deletePhysically">
         <if test="ids != null and ids.size > 0">
        delete from
        role 
        <if test="ids != null and ids.size > 0">
            where id
        <include refid="inIds"/>        
        </if>
        </if>
    </update>

    <select id="queryPageData" resultMap="BaseResultMap"   parameterType="com.yaduo.devopstools.api.role.dto.request.RoleRequest" >
        select  <include refid="Base_Column_List"/> from role
        <if test="request_model != null ">
            where 1=1   
                <if test="request_model.id != null">
                    and id = #{request_model.id,jdbcType=BIGINT}
                </if> 
                <if test="request_model.roleName != null and request_model.roleName != ''">
                    and role_name like concat('%',#{request_model.roleName,jdbcType=VARCHAR},'%')
                </if>
                <if test="request_model.dataStatus != null">
                    and data_status = #{request_model.dataStatus,jdbcType=INTEGER}
                </if> 
                <if test="request_model.createdBy != null">
                    and created_by = #{request_model.createdBy,jdbcType=BIGINT}
                </if> 
                <if test="request_model.gmtCreateStart != null and request_model.gmtCreateStart != ''">
                    and gmt_create &gt;= #{request_model.gmtCreateStart,jdbcType=TIMESTAMP}
                </if>
                <if test="request_model.gmtCreateEnd != null and request_model.gmtCreateEnd != ''">
                    and gmt_create &lt;= #{request_model.gmtCreateEnd,jdbcType=TIMESTAMP}
                </if>
                <if test="request_model.modifiedBy != null">
                    and modified_by = #{request_model.modifiedBy,jdbcType=BIGINT}
                </if> 
                <if test="request_model.gmtModifiedStart != null and request_model.gmtModifiedStart != ''">
                    and gmt_modified &gt;= #{request_model.gmtModifiedStart,jdbcType=TIMESTAMP}
                </if>
                <if test="request_model.gmtModifiedEnd != null and request_model.gmtModifiedEnd != ''">
                    and gmt_modified &lt;= #{request_model.gmtModifiedEnd,jdbcType=TIMESTAMP}
                </if>
                <if test="request_model.traceId != null and request_model.traceId != ''">
                    and trace_id like concat('%',#{request_model.traceId,jdbcType=VARCHAR},'%')
                </if>
        </if>
        <if test="request_model.sort != null and  request_model.sort.size()>0">
            order by
            <foreach collection="request_model.sort" item="model" separator=",">
                ${model.property} ${model.direction}
            </foreach>
        </if>
        limit #{offset,jdbcType=INTEGER},#{request_model.size,jdbcType=INTEGER}
    </select>

    <select id="queryTotalCount" resultType="int" parameterType="com.yaduo.devopstools.api.role.dto.request.RoleRequest">
        select  count(*) as total from role
        <if test="request_model != null ">
            where 1=1   
                <if test="request_model.id != null">
                    and id = #{request_model.id,jdbcType=BIGINT}
                </if> 
                <if test="request_model.roleName != null and request_model.roleName != ''">
                    and role_name like concat('%',#{request_model.roleName,jdbcType=VARCHAR},'%')
                </if>
                <if test="request_model.dataStatus != null">
                    and data_status = #{request_model.dataStatus,jdbcType=INTEGER}
                </if> 
                <if test="request_model.createdBy != null">
                    and created_by = #{request_model.createdBy,jdbcType=BIGINT}
                </if> 
                <if test="request_model.gmtCreateStart != null and request_model.gmtCreateStart != ''">
                    and gmt_create &gt;= #{request_model.gmtCreateStart,jdbcType=TIMESTAMP}
                </if>
                <if test="request_model.gmtCreateEnd != null and request_model.gmtCreateEnd != ''">
                    and gmt_create &lt;= #{request_model.gmtCreateEnd,jdbcType=TIMESTAMP}
                </if>
                <if test="request_model.modifiedBy != null">
                    and modified_by = #{request_model.modifiedBy,jdbcType=BIGINT}
                </if> 
                <if test="request_model.gmtModifiedStart != null and request_model.gmtModifiedStart != ''">
                    and gmt_modified &gt;= #{request_model.gmtModifiedStart,jdbcType=TIMESTAMP}
                </if>
                <if test="request_model.gmtModifiedEnd != null and request_model.gmtModifiedEnd != ''">
                    and gmt_modified &lt;= #{request_model.gmtModifiedEnd,jdbcType=TIMESTAMP}
                </if>
                <if test="request_model.traceId != null and request_model.traceId != ''">
                    and trace_id like concat('%',#{request_model.traceId,jdbcType=VARCHAR},'%')
                </if>
        </if>
    </select>

    <select id="getExactData" resultMap="BaseResultMap"   parameterType="com.yaduo.devopstools.api.role.dto.request.RoleMultiRequest" >
        select  <include refid="Base_Column_List"/> from role
        <if test="request_model != null ">
            where 1=1   
                <if test="request_model.id != null and request_model.id.size > 0">
                AND  id in
                    <foreach collection="request_model.id" item="temp" index="index" open="(" separator="," close=")">#{temp}
                    </foreach>
                </if> 
               <if test="request_model.roleName != null and request_model.roleName.size > 0">
                    AND  role_name in
                    <foreach collection="request_model.roleName" item="temp" index="index" open="(" separator="," close=")">#{temp}
                    </foreach>
                </if>
               
                <if test="request_model.dataStatus != null and request_model.dataStatus.size > 0">
                AND  data_status in
                    <foreach collection="request_model.dataStatus" item="temp" index="index" open="(" separator="," close=")">#{temp}
                    </foreach>
                </if> 
                <if test="request_model.createdBy != null and request_model.createdBy.size > 0">
                AND  created_by in
                    <foreach collection="request_model.createdBy" item="temp" index="index" open="(" separator="," close=")">#{temp}
                    </foreach>
                </if> 
               <if test="request_model.gmtCreateStart != null and request_model.gmtCreateStart != ''">
                    and gmt_create &gt;= #{request_model.gmtCreateStart,jdbcType=TIMESTAMP}
                </if>
                <if test="request_model.gmtCreateEnd != null and request_model.gmtCreateEnd != ''">
                    and gmt_create &lt;= #{request_model.gmtCreateEnd,jdbcType=TIMESTAMP}
                </if>
               
                <if test="request_model.modifiedBy != null and request_model.modifiedBy.size > 0">
                AND  modified_by in
                    <foreach collection="request_model.modifiedBy" item="temp" index="index" open="(" separator="," close=")">#{temp}
                    </foreach>
                </if> 
               <if test="request_model.gmtModifiedStart != null and request_model.gmtModifiedStart != ''">
                    and gmt_modified &gt;= #{request_model.gmtModifiedStart,jdbcType=TIMESTAMP}
                </if>
                <if test="request_model.gmtModifiedEnd != null and request_model.gmtModifiedEnd != ''">
                    and gmt_modified &lt;= #{request_model.gmtModifiedEnd,jdbcType=TIMESTAMP}
                </if>
               <if test="request_model.traceId != null and request_model.traceId.size > 0">
                    AND  trace_id in
                    <foreach collection="request_model.traceId" item="temp" index="index" open="(" separator="," close=")">#{temp}
                    </foreach>
                </if>
        </if>
        <if test="request_model.sort != null and  request_model.sort.size()>0">
            order by
            <foreach collection="request_model.sort" item="model" separator=",">
                ${model.property} ${model.direction}
            </foreach>
        </if>
        limit #{offset,jdbcType=INTEGER},#{request_model.size,jdbcType=INTEGER}
    </select>

    <select id="getExactTotalCount" resultType="int" parameterType="com.yaduo.devopstools.api.role.dto.request.RoleMultiRequest">
        <if test="request_model != null ">
            select  count(*) as total from role
            where 1=1   
                <if test="request_model.id != null and request_model.id.size > 0">
                AND  id in
                    <foreach collection="request_model.id" item="temp" index="index" open="(" separator="," close=")">#{temp}
                    </foreach>
                </if> 
                <if test="request_model.roleName != null and request_model.roleName.size > 0">
                    AND  role_name in
                    <foreach collection="request_model.roleName" item="temp" index="index" open="(" separator="," close=")">#{temp}
                    </foreach>
                </if>
                
                <if test="request_model.dataStatus != null and request_model.dataStatus.size > 0">
                AND  data_status in
                    <foreach collection="request_model.dataStatus" item="temp" index="index" open="(" separator="," close=")">#{temp}
                    </foreach>
                </if> 
                <if test="request_model.createdBy != null and request_model.createdBy.size > 0">
                AND  created_by in
                    <foreach collection="request_model.createdBy" item="temp" index="index" open="(" separator="," close=")">#{temp}
                    </foreach>
                </if> 
                <if test="request_model.gmtCreateStart != null and request_model.gmtCreateStart != ''">
                    and gmt_create &gt;= #{request_model.gmtCreateStart,jdbcType=TIMESTAMP}
                </if>
                <if test="request_model.gmtCreateEnd != null and request_model.gmtCreateEnd != ''">
                    and gmt_create &lt;= #{request_model.gmtCreateEnd,jdbcType=TIMESTAMP}
                </if>
                
                <if test="request_model.modifiedBy != null and request_model.modifiedBy.size > 0">
                AND  modified_by in
                    <foreach collection="request_model.modifiedBy" item="temp" index="index" open="(" separator="," close=")">#{temp}
                    </foreach>
                </if> 
                <if test="request_model.gmtModifiedStart != null and request_model.gmtModifiedStart != ''">
                    and gmt_modified &gt;= #{request_model.gmtModifiedStart,jdbcType=TIMESTAMP}
                </if>
                <if test="request_model.gmtModifiedEnd != null and request_model.gmtModifiedEnd != ''">
                    and gmt_modified &lt;= #{request_model.gmtModifiedEnd,jdbcType=TIMESTAMP}
                </if>
                <if test="request_model.traceId != null and request_model.traceId.size > 0">
                    AND  trace_id in
                    <foreach collection="request_model.traceId" item="temp" index="index" open="(" separator="," close=")">#{temp}
                    </foreach>
                </if>
        </if>
    </select>

     <select id="getCountGroupByField" resultType="map" parameterType="com.yaduo.devopstools.api.role.dto.request.RoleMultiRequest">
        <if test="request_model != null ">
        select count(*) as total ,
        <trim prefix="" suffixOverrides=","> 
            <if test="request_model.id != null and request_model.id.size > 0">
                id,
            </if> 
            <if test="request_model.roleName != null and request_model.roleName.size > 0">
                role_name,
            </if> 
            <if test="request_model.dataStatus != null and request_model.dataStatus.size > 0">
                data_status,
            </if> 
            <if test="request_model.createdBy != null and request_model.createdBy.size > 0">
                created_by,
            </if> 
            <if test="(request_model.gmtCreateStart != null and request_model.gmtCreateStart != '') or (request_model.gmtCreateEnd != null and request_model.gmtCreateEnd != '')">
                gmt_create,
            </if> 
            <if test="request_model.modifiedBy != null and request_model.modifiedBy.size > 0">
                modified_by,
            </if> 
            <if test="(request_model.gmtModifiedStart != null and request_model.gmtModifiedStart != '') or (request_model.gmtModifiedEnd != null and request_model.gmtModifiedEnd != '')">
                gmt_modified,
            </if> 
            <if test="request_model.traceId != null and request_model.traceId.size > 0">
                trace_id,
            </if>
        </trim>
        from role
            <trim prefix="where" suffixOverrides="and">
                <if test="request_model.id != null and request_model.id.size > 0">
                    id in
                    <foreach collection="request_model.id" item="temp" index="index" open="(" separator="," close=")">#{temp}</foreach> AND
                </if>
                <if test="request_model.roleName != null and request_model.roleName.size > 0">
                    role_name in
                    <foreach collection="request_model.roleName" item="temp" index="index" open="(" separator="," close=")">#{temp}</foreach> AND
                </if>
                <if test="request_model.dataStatus != null and request_model.dataStatus.size > 0">
                    data_status in
                    <foreach collection="request_model.dataStatus" item="temp" index="index" open="(" separator="," close=")">#{temp}</foreach> AND
                </if>
                <if test="request_model.createdBy != null and request_model.createdBy.size > 0">
                    created_by in
                    <foreach collection="request_model.createdBy" item="temp" index="index" open="(" separator="," close=")">#{temp}</foreach> AND
                </if><if test="request_model.gmtCreateStart != null and request_model.gmtCreateStart != ''">
                    gmt_create &gt;= #{request_model.gmtCreateStart,jdbcType=TIMESTAMP} AND
                </if>
                <if test="request_model.gmtCreateEnd != null and request_model.gmtCreateEnd != ''">
                    gmt_create &lt;= #{request_model.gmtCreateEnd,jdbcType=TIMESTAMP} AND
                </if>
                <if test="request_model.modifiedBy != null and request_model.modifiedBy.size > 0">
                    modified_by in
                    <foreach collection="request_model.modifiedBy" item="temp" index="index" open="(" separator="," close=")">#{temp}</foreach> AND
                </if><if test="request_model.gmtModifiedStart != null and request_model.gmtModifiedStart != ''">
                    gmt_modified &gt;= #{request_model.gmtModifiedStart,jdbcType=TIMESTAMP} AND
                </if>
                <if test="request_model.gmtModifiedEnd != null and request_model.gmtModifiedEnd != ''">
                    gmt_modified &lt;= #{request_model.gmtModifiedEnd,jdbcType=TIMESTAMP} AND
                </if>
                <if test="request_model.traceId != null and request_model.traceId.size > 0">
                    trace_id in
                    <foreach collection="request_model.traceId" item="temp" index="index" open="(" separator="," close=")">#{temp}</foreach> AND
                </if>
            </trim>
            <trim prefix="group by" suffixOverrides=","> 
                <if test="request_model.id != null and request_model.id.size > 0">
                    id,
                </if> 
                <if test="request_model.roleName != null and request_model.roleName.size > 0">
                    role_name,
                </if> 
                <if test="request_model.dataStatus != null and request_model.dataStatus.size > 0">
                    data_status,
                </if> 
                <if test="request_model.createdBy != null and request_model.createdBy.size > 0">
                    created_by,
                </if> 
                <if test="(request_model.gmtCreateStart != null and request_model.gmtCreateStart != '' or request_model.gmtCreateEnd != null and request_model.gmtCreateEnd != '')">
                    gmt_create,
                </if> 
                <if test="request_model.modifiedBy != null and request_model.modifiedBy.size > 0">
                    modified_by,
                </if> 
                <if test="(request_model.gmtModifiedStart != null and request_model.gmtModifiedStart != '' or request_model.gmtModifiedEnd != null and request_model.gmtModifiedEnd != '')">
                    gmt_modified,
                </if> 
                <if test="request_model.traceId != null and request_model.traceId.size > 0">
                    trace_id,
                </if>
            </trim>
        </if>
    </select>

     <select id="selectDistinctByConditionModel" resultType="map" parameterType="com.yaduo.devopstools.api.role.dto.request.RoleMultiRequest">
        <if test="condition_model != null ">
        select 
        <trim prefix="distinct" suffixOverrides=","> 
            <if test="request_model.id != null and request_model.id >= 0">
                id as id,
            </if> 
            <if test="request_model.roleName != null and request_model.roleName != ''">
                role_name as roleName,
            </if> 
            <if test="request_model.dataStatus != null and request_model.dataStatus >= 0">
                data_status as dataStatus,
            </if> 
            <if test="request_model.createdBy != null and request_model.createdBy >= 0">
                created_by as createdBy,
            </if> 
            <if test="request_model.gmtCreate != null and request_model.gmtCreate != ''"> 
                gmt_create as gmtCreate,
            </if>  
            <if test="request_model.modifiedBy != null and request_model.modifiedBy >= 0">
                modified_by as modifiedBy,
            </if> 
            <if test="request_model.gmtModified != null and request_model.gmtModified != ''"> 
                gmt_modified as gmtModified,
            </if>  
            <if test="request_model.traceId != null and request_model.traceId != ''">
                trace_id as traceId,
            </if>
        </trim>
        from role
            <trim prefix="where" suffixOverrides="and">
                <if test="condition_model.id != null and condition_model.id.size > 0">
                    id in
                    <foreach collection="condition_model.id" item="temp" index="index" open="(" separator="," close=")">#{temp}</foreach> AND
                </if>
                <if test="condition_model.roleName != null and condition_model.roleName.size > 0">
                    role_name in
                    <foreach collection="condition_model.roleName" item="temp" index="index" open="(" separator="," close=")">#{temp}</foreach> AND
                </if>
                <if test="condition_model.dataStatus != null and condition_model.dataStatus.size > 0">
                    data_status in
                    <foreach collection="condition_model.dataStatus" item="temp" index="index" open="(" separator="," close=")">#{temp}</foreach> AND
                </if>
                <if test="condition_model.createdBy != null and condition_model.createdBy.size > 0">
                    created_by in
                    <foreach collection="condition_model.createdBy" item="temp" index="index" open="(" separator="," close=")">#{temp}</foreach> AND
                </if><if test="condition_model.gmtCreateStart != null and condition_model.gmtCreateStart != ''">
                    gmt_create &gt;= #{condition_model.gmtCreateStart,jdbcType=TIMESTAMP} AND
                </if>
                <if test="condition_model.gmtCreateEnd != null and condition_model.gmtCreateEnd != ''">
                    gmt_create &lt;= #{condition_model.gmtCreateEnd,jdbcType=TIMESTAMP} AND
                </if>
                <if test="condition_model.modifiedBy != null and condition_model.modifiedBy.size > 0">
                    modified_by in
                    <foreach collection="condition_model.modifiedBy" item="temp" index="index" open="(" separator="," close=")">#{temp}</foreach> AND
                </if><if test="condition_model.gmtModifiedStart != null and condition_model.gmtModifiedStart != ''">
                    gmt_modified &gt;= #{condition_model.gmtModifiedStart,jdbcType=TIMESTAMP} AND
                </if>
                <if test="condition_model.gmtModifiedEnd != null and condition_model.gmtModifiedEnd != ''">
                    gmt_modified &lt;= #{condition_model.gmtModifiedEnd,jdbcType=TIMESTAMP} AND
                </if>
                <if test="condition_model.traceId != null and condition_model.traceId.size > 0">
                    trace_id in
                    <foreach collection="condition_model.traceId" item="temp" index="index" open="(" separator="," close=")">#{temp}</foreach> AND
                </if>
            </trim>
        limit #{offset,jdbcType=INTEGER},#{condition_model.size,jdbcType=INTEGER}
        </if>
    </select>

     <select id="selectDistinctCountByConditionModel" resultType="int" parameterType="com.yaduo.devopstools.api.role.dto.request.RoleMultiRequest">
        <if test="condition_model != null ">
        select count(
            <trim prefix="distinct" suffixOverrides=","> 
                <if test="request_model.id != null and request_model.id >= 0">
                    id,
                </if> 
                <if test="request_model.roleName != null and request_model.roleName != ''">
                    role_name,
                </if> 
                <if test="request_model.dataStatus != null and request_model.dataStatus >= 0">
                    data_status,
                </if> 
                <if test="request_model.createdBy != null and request_model.createdBy >= 0">
                    created_by,
                </if> 
                <if test="request_model.gmtCreate != null and request_model.gmtCreate != ''"> 
                    gmt_create,
                </if>  
                <if test="request_model.modifiedBy != null and request_model.modifiedBy >= 0">
                    modified_by,
                </if> 
                <if test="request_model.gmtModified != null and request_model.gmtModified != ''"> 
                    gmt_modified,
                </if>  
                <if test="request_model.traceId != null and request_model.traceId != ''">
                    trace_id,
                </if>
            </trim>
        )
        from role
            <trim prefix="where" suffixOverrides="and">
                <if test="condition_model.id != null and condition_model.id.size > 0">
                    id in
                    <foreach collection="condition_model.id" item="temp" index="index" open="(" separator="," close=")">#{temp}</foreach> AND
                </if>
                <if test="condition_model.roleName != null and condition_model.roleName.size > 0">
                    role_name in
                    <foreach collection="condition_model.roleName" item="temp" index="index" open="(" separator="," close=")">#{temp}</foreach> AND
                </if>
                <if test="condition_model.dataStatus != null and condition_model.dataStatus.size > 0">
                    data_status in
                    <foreach collection="condition_model.dataStatus" item="temp" index="index" open="(" separator="," close=")">#{temp}</foreach> AND
                </if>
                <if test="condition_model.createdBy != null and condition_model.createdBy.size > 0">
                    created_by in
                    <foreach collection="condition_model.createdBy" item="temp" index="index" open="(" separator="," close=")">#{temp}</foreach> AND
                </if><if test="condition_model.gmtCreateStart != null and condition_model.gmtCreateStart != ''">
                    gmt_create &gt;= #{condition_model.gmtCreateStart,jdbcType=TIMESTAMP} AND
                </if>
                <if test="condition_model.gmtCreateEnd != null and condition_model.gmtCreateEnd != ''">
                    gmt_create &lt;= #{condition_model.gmtCreateEnd,jdbcType=TIMESTAMP} AND
                </if>
                <if test="condition_model.modifiedBy != null and condition_model.modifiedBy.size > 0">
                    modified_by in
                    <foreach collection="condition_model.modifiedBy" item="temp" index="index" open="(" separator="," close=")">#{temp}</foreach> AND
                </if><if test="condition_model.gmtModifiedStart != null and condition_model.gmtModifiedStart != ''">
                    gmt_modified &gt;= #{condition_model.gmtModifiedStart,jdbcType=TIMESTAMP} AND
                </if>
                <if test="condition_model.gmtModifiedEnd != null and condition_model.gmtModifiedEnd != ''">
                    gmt_modified &lt;= #{condition_model.gmtModifiedEnd,jdbcType=TIMESTAMP} AND
                </if>
                <if test="condition_model.traceId != null and condition_model.traceId.size > 0">
                    trace_id in
                    <foreach collection="condition_model.traceId" item="temp" index="index" open="(" separator="," close=")">#{temp}</foreach> AND
                </if>
            </trim>
        </if>
    </select>

    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from role 
        where 1=1 
        <if test="dataStatus != null and dataStatus >= 0">
            AND data_status=#{dataStatus}
        </if>
        <if test="ids != null and ids.size > 0">
           AND  id
        <include refid="inIds"/>        
        </if>
    </select>

    <insert id="batchInsert" parameterType="com.yaduo.devopstools.role.domain.RoleDO"
            useGeneratedKeys="true" keyProperty="id"
            keyColumn="id" statementType="PREPARED">
        insert into role (
            id,
            role_name,
            data_status,
            created_by,
            gmt_create,
            modified_by,
            gmt_modified,
            trace_id
        )
        values
        <foreach collection="list" item="model" separator=",">
        (
            #{model.id,jdbcType=BIGINT},   
            #{model.roleName,jdbcType=VARCHAR},   
            #{model.dataStatus,jdbcType=INTEGER},   
            #{model.createdBy,jdbcType=BIGINT},    
            <if test="model.gmtCreate != null">
                #{model.gmtCreate,jdbcType=TIMESTAMP},
            </if> 
            <if test="model.gmtCreate == null">
                now(),
            </if>   
            #{model.modifiedBy,jdbcType=BIGINT},    
            <if test="model.gmtModified != null">
                #{model.gmtModified,jdbcType=TIMESTAMP},
            </if> 
            <if test="model.gmtModified == null">
                now(),
            </if>   
            #{model.traceId,jdbcType=VARCHAR} 
        )
        </foreach>
    </insert>

    <insert id="batchInsertOrUpdateStatus" parameterType="com.yaduo.devopstools.role.domain.RoleDO"
            useGeneratedKeys="true" keyProperty="list.id"
            keyColumn="id" statementType="PREPARED">
        insert into role (
            id,
            role_name,
            data_status,
            created_by,
            gmt_create,
            modified_by,
            gmt_modified,
            trace_id
        )
        values
        <foreach collection="list" item="model" separator=",">
        (
            #{model.id,jdbcType=BIGINT},   
            #{model.roleName,jdbcType=VARCHAR},   
            #{model.dataStatus,jdbcType=INTEGER},   
            #{model.createdBy,jdbcType=BIGINT},    
            <if test="model.gmtCreate != null">
                #{model.gmtCreate,jdbcType=TIMESTAMP},
            </if> 
            <if test="model.gmtCreate == null">
                now(),
            </if>   
            #{model.modifiedBy,jdbcType=BIGINT},    
            <if test="model.gmtModified != null">
                #{model.gmtModified,jdbcType=TIMESTAMP},
            </if> 
            <if test="model.gmtModified == null">
                now(),
            </if>   
            #{model.traceId,jdbcType=VARCHAR} 
        )
        </foreach>
        ON DUPLICATE KEY UPDATE
        data_status= 0,
        gmt_modified = now(),
        modified_by = #{modifiedBy,jdbcType=BIGINT}
    </insert>

    <insert id="insertOrUpdateNonNullFields" parameterType="com.yaduo.devopstools.role.domain.RoleDO"
            useGeneratedKeys="true" keyProperty="request_model.id"
            keyColumn="id" statementType="PREPARED">
        insert into role (
            id,
            role_name,
            data_status,
            created_by,
            gmt_create,
            modified_by,
            gmt_modified,
            trace_id
        )
        values
        (
            #{request_model.id,jdbcType=BIGINT},   
            #{request_model.roleName,jdbcType=VARCHAR},   
            #{request_model.dataStatus,jdbcType=INTEGER},   
            #{request_model.createdBy,jdbcType=BIGINT},    
            <if test="request_model.gmtCreate != null">
                #{request_model.gmtCreate,jdbcType=TIMESTAMP},
            </if> 
            <if test="request_model.gmtCreate == null">
                now(),
            </if>   
            #{request_model.modifiedBy,jdbcType=BIGINT},    
            <if test="request_model.gmtModified != null">
                #{request_model.gmtModified,jdbcType=TIMESTAMP},
            </if> 
            <if test="request_model.gmtModified == null">
                now(),
            </if>   
            #{request_model.traceId,jdbcType=VARCHAR} 
        )
        ON DUPLICATE KEY UPDATE
            <if test="request_model.roleName != null">
               role_name = #{request_model.roleName,jdbcType=VARCHAR},
            </if>    
            <if test="request_model.dataStatus != null">
               data_status = #{request_model.dataStatus,jdbcType=INTEGER},
            </if>                
            <if test="request_model.traceId != null">
               trace_id = #{request_model.traceId,jdbcType=VARCHAR},
            </if> 
        gmt_modified = now(),
        modified_by = #{modifiedBy,jdbcType=BIGINT}
    </insert>

    <update id="updateNonNullFieldsByNonNullFields">
        update role
        <trim prefix="set" suffixOverrides=",">
            <if test="request_model.roleName != null">
                role_name = #{request_model.roleName,jdbcType=VARCHAR},
            </if>   
            <if test="request_model.dataStatus != null">
                data_status = #{request_model.dataStatus,jdbcType=INTEGER},
            </if>   
            <if test="request_model.createdBy != null">
                created_by = #{request_model.createdBy,jdbcType=BIGINT},
            </if>    
            <if test="modifiedBy != null">
                modified_by = #{modifiedBy,jdbcType=BIGINT},
            </if>     
            <if test="request_model.traceId != null">
                trace_id = #{request_model.traceId,jdbcType=VARCHAR},
            </if> 
            gmt_modified = now()
        </trim>
         <trim prefix="where" suffixOverrides="and">
                <if test="condition_model.id != null and condition_model.id.size > 0">
                    id in
                    <foreach collection="condition_model.id" item="temp" index="index" open="(" separator="," close=")">#{temp}</foreach> AND
                </if>
                <if test="condition_model.roleName != null and condition_model.roleName.size > 0">
                    role_name in
                    <foreach collection="condition_model.roleName" item="temp" index="index" open="(" separator="," close=")">#{temp}</foreach> AND
                </if>
                <if test="condition_model.dataStatus != null and condition_model.dataStatus.size > 0">
                    data_status in
                    <foreach collection="condition_model.dataStatus" item="temp" index="index" open="(" separator="," close=")">#{temp}</foreach> AND
                </if>
                <if test="condition_model.createdBy != null and condition_model.createdBy.size > 0">
                    created_by in
                    <foreach collection="condition_model.createdBy" item="temp" index="index" open="(" separator="," close=")">#{temp}</foreach> AND
                </if><if test="condition_model.gmtCreateStart != null and condition_model.gmtCreateStart != ''">
                    gmt_create &gt;= #{condition_model.gmtCreateStart,jdbcType=TIMESTAMP} AND
                </if>
                <if test="condition_model.gmtCreateEnd != null and condition_model.gmtCreateEnd != ''">
                    gmt_create &lt;= #{condition_model.gmtCreateEnd,jdbcType=TIMESTAMP} AND
                </if>
                <if test="condition_model.modifiedBy != null and condition_model.modifiedBy.size > 0">
                    modified_by in
                    <foreach collection="condition_model.modifiedBy" item="temp" index="index" open="(" separator="," close=")">#{temp}</foreach> AND
                </if><if test="condition_model.gmtModifiedStart != null and condition_model.gmtModifiedStart != ''">
                    gmt_modified &gt;= #{condition_model.gmtModifiedStart,jdbcType=TIMESTAMP} AND
                </if>
                <if test="condition_model.gmtModifiedEnd != null and condition_model.gmtModifiedEnd != ''">
                    gmt_modified &lt;= #{condition_model.gmtModifiedEnd,jdbcType=TIMESTAMP} AND
                </if>
                <if test="condition_model.traceId != null and condition_model.traceId.size > 0">
                    trace_id in
                    <foreach collection="condition_model.traceId" item="temp" index="index" open="(" separator="," close=")">#{temp}</foreach> AND
                </if>
            </trim>
    </update>

    <update id="updateNonNullFieldsByPrimaryKey" parameterType="com.yaduo.devopstools.role.domain.RoleDO">
        update role
        <trim prefix="set" suffixOverrides=",">
            <if test="roleName != null">
                role_name = #{roleName,jdbcType=VARCHAR},
            </if>   
            <if test="dataStatus != null">
                data_status = #{dataStatus,jdbcType=INTEGER},
            </if>   
            <if test="createdBy != null">
                created_by = #{createdBy,jdbcType=BIGINT},
            </if>   
            <if test="gmtCreate != null">
                gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
            </if>   
            <if test="modifiedBy != null">
                modified_by = #{modifiedBy,jdbcType=BIGINT},
            </if>    
            <if test="gmtModified != null">
                gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtModified == null">
                gmt_modified = now(),
            </if> 
            <if test="traceId != null">
                trace_id = #{traceId,jdbcType=VARCHAR},
            </if>  
        </trim>
        <trim prefix="where" suffixOverrides="and">
            <if test="id != null">
                id = #{id,jdbcType=BIGINT} and 
            </if>            
        </trim>
    </update>

    <update id="updateAllFieldsByPrimaryKey" parameterType="com.yaduo.devopstools.role.domain.RoleDO">
        update role
        <trim prefix="set" suffixOverrides=",">
            role_name = #{roleName,jdbcType=VARCHAR},  
            data_status = #{dataStatus,jdbcType=INTEGER},  
            <if test="createdBy != null">
                created_by = #{createdBy,jdbcType=BIGINT},
            </if> 
            <if test="gmtCreate != null">
                gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
            </if> 
            modified_by = #{modifiedBy,jdbcType=BIGINT},   
            <if test="gmtModified != null">
                gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtModified == null">
                gmt_modified = now(),
            </if>  
            trace_id = #{traceId,jdbcType=VARCHAR}, 
        </trim>
        <trim prefix="where" suffixOverrides="and">
            <if test="id != null">
                id = #{id,jdbcType=BIGINT} and 
            </if>            
        </trim>
    </update>

    <update id="batchUpdateStatus">
        <if test="ids != null and ids.size > 0">
        update
        role set data_status=#{data_status,jdbcType=INTEGER}, modified_by = #{modifiedBy,jdbcType=BIGINT}
        where id
        <include refid="inIds"/>         
        </if>
    </update>

    <update id="updateStatusByNonNullField">
        <if test="request_model != null">
            update
            role set data_status=#{data_status,jdbcType=INTEGER}, modified_by = #{modifiedBy,jdbcType=BIGINT}
            <trim prefix="where" suffixOverrides="and">
            <if test="request_model.id != null">
                id = #{request_model.id,jdbcType=BIGINT} and  
            </if>
            <if test="request_model.roleName != null">
                role_name = #{request_model.roleName,jdbcType=VARCHAR} and  
            </if>
            <if test="request_model.dataStatus != null">
                data_status = #{request_model.dataStatus,jdbcType=INTEGER} and  
            </if>
            <if test="request_model.createdBy != null">
                created_by = #{request_model.createdBy,jdbcType=BIGINT} and  
            </if>
            <if test="request_model.gmtCreate != null">
                gmt_create = #{request_model.gmtCreate,jdbcType=TIMESTAMP} and  
            </if>
            <if test="request_model.modifiedBy != null">
                modified_by = #{request_model.modifiedBy,jdbcType=BIGINT} and  
            </if>
            <if test="request_model.gmtModified != null">
                gmt_modified = #{request_model.gmtModified,jdbcType=TIMESTAMP} and  
            </if>
            <if test="request_model.traceId != null">
                trace_id = #{request_model.traceId,jdbcType=VARCHAR} and  
            </if>
            </trim>
        </if>
    </update>

    <sql id="inIds">
        in
        <foreach collection="ids" item="id" index="index" open="(" separator="," close=")">#{id}
        </foreach>
    </sql>
</mapper>
