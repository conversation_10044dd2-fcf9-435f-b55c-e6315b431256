<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yaduo.devopstools.system.mapper.SystemMapper">
  
  <resultMap id="BaseResultMap" type="com.yaduo.devopstools.system.entity.SystemEntity">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="system" jdbcType="VARCHAR" property="system" />
    <result column="home_page" jdbcType="VARCHAR" property="homePage" />
  </resultMap>
  
  <sql id="Base_Column_List">
    id, `system`, home_page
  </sql>
  
  <!-- 查询所有系统 -->
  <select id="selectAll" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from `system`
    order by id
  </select>
  
  <!-- 根据系统code查询 -->
  <select id="selectBySystem" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from `system`
    where `system` = #{system,jdbcType=VARCHAR}
  </select>
  
  <!-- 根据主键查询 -->
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from `system`
    where id = #{id,jdbcType=INTEGER}
  </select>
  
  <!-- 插入系统信息 -->
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.yaduo.devopstools.system.entity.SystemEntity" useGeneratedKeys="true">
    insert into `system`
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="system != null and system != ''">
        `system`,
      </if>
      <if test="homePage != null and homePage != ''">
        home_page,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="system != null and system != ''">
        #{system,jdbcType=VARCHAR},
      </if>
      <if test="homePage != null and homePage != ''">
        #{homePage,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  
  <!-- 更新系统信息 -->
  <update id="updateByPrimaryKeySelective" parameterType="com.yaduo.devopstools.system.entity.SystemEntity">
    update `system`
    <set>
      <if test="system != null and system != ''">
        `system` = #{system,jdbcType=VARCHAR},
      </if>
      <if test="homePage != null and homePage != ''">
        home_page = #{homePage,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  
  <!-- 删除系统信息 -->
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from `system`
    where id = #{id,jdbcType=INTEGER}
  </delete>
  
</mapper>
