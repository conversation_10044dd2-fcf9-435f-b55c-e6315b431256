<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yaduo.devopstools.app.mapper.AppDeployMapper">
  <resultMap id="BaseResultMap" type="com.yaduo.devopstools.app.entity.AppDeployEntity">
    <!--@mbg.generated-->
    <!--@Table app_deploy-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="app_id" jdbcType="VARCHAR" property="appId" />
    <result column="env" jdbcType="VARCHAR" property="env" />
    <result column="branch" jdbcType="VARCHAR" property="branch" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="operate_user" jdbcType="VARCHAR" property="operateUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, app_id, env, branch, remark, operate_user, create_time, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from app_deploy
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from app_deploy
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.yaduo.devopstools.app.entity.AppDeployEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into app_deploy
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="appId != null and appId != ''">
        app_id,
      </if>
      <if test="env != null and env != ''">
        env,
      </if>
      <if test="branch != null and branch != ''">
        branch,
      </if>
      <if test="remark != null and remark != ''">
        remark,
      </if>
      <if test="operateUser != null and operateUser != ''">
        operate_user,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="appId != null and appId != ''">
        #{appId,jdbcType=VARCHAR},
      </if>
      <if test="env != null and env != ''">
        #{env,jdbcType=VARCHAR},
      </if>
      <if test="branch != null and branch != ''">
        #{branch,jdbcType=VARCHAR},
      </if>
      <if test="remark != null and remark != ''">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="operateUser != null and operateUser != ''">
        #{operateUser,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.yaduo.devopstools.app.entity.AppDeployEntity">
    <!--@mbg.generated-->
    update app_deploy
    <set>
      <if test="appId != null and appId != ''">
        app_id = #{appId,jdbcType=VARCHAR},
      </if>
      <if test="env != null and env != ''">
        env = #{env,jdbcType=VARCHAR},
      </if>
      <if test="branch != null and branch != ''">
        branch = #{branch,jdbcType=VARCHAR},
      </if>
      <if test="remark != null and remark != ''">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="operateUser != null and operateUser != ''">
        operate_user = #{operateUser,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <select id="selectOneByAppIdAndEnv" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List" />
        from app_deploy
        where app_id=#{appId,jdbcType=VARCHAR} and env=#{env,jdbcType=VARCHAR}
    </select>
  <select id="selectAllOrderByUpdateTimeDesc" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List" />
        from app_deploy order by update_time desc
    </select>
  <insert id="insertOrUpdate" keyColumn="id" keyProperty="id" parameterType="com.yaduo.devopstools.app.entity.AppDeployEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into app_deploy
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      app_id,
      env,
      branch,
      remark,
      operate_user,
      create_time,
      update_time,
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      #{appId,jdbcType=VARCHAR},
      #{env,jdbcType=VARCHAR},
      #{branch,jdbcType=VARCHAR},
      #{remark,jdbcType=VARCHAR},
      #{operateUser,jdbcType=VARCHAR},
      #{createTime,jdbcType=TIMESTAMP},
      #{updateTime,jdbcType=TIMESTAMP},
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="id != null">
        id = #{id,jdbcType=BIGINT},
      </if>
      app_id = #{appId,jdbcType=VARCHAR},
      env = #{env,jdbcType=VARCHAR},
      branch = #{branch,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      operate_user = #{operateUser,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
    </trim>
  </insert>
  <insert id="insertOrUpdateSelective" keyColumn="id" keyProperty="id" parameterType="com.yaduo.devopstools.app.entity.AppDeployEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into app_deploy
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="appId != null and appId != ''">
        app_id,
      </if>
      <if test="env != null and env != ''">
        env,
      </if>
      <if test="branch != null and branch != ''">
        branch,
      </if>
      <if test="remark != null and remark != ''">
        remark,
      </if>
      <if test="operateUser != null and operateUser != ''">
        operate_user,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="appId != null and appId != ''">
        #{appId,jdbcType=VARCHAR},
      </if>
      <if test="env != null and env != ''">
        #{env,jdbcType=VARCHAR},
      </if>
      <if test="branch != null and branch != ''">
        #{branch,jdbcType=VARCHAR},
      </if>
      <if test="remark != null and remark != ''">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="operateUser != null and operateUser != ''">
        #{operateUser,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="id != null">
        id = #{id,jdbcType=BIGINT},
      </if>
      <if test="appId != null and appId != ''">
        app_id = #{appId,jdbcType=VARCHAR},
      </if>
      <if test="env != null and env != ''">
        env = #{env,jdbcType=VARCHAR},
      </if>
      <if test="branch != null and branch != ''">
        branch = #{branch,jdbcType=VARCHAR},
      </if>
      <if test="remark != null and remark != ''">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="operateUser != null and operateUser != ''">
        operate_user = #{operateUser,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
</mapper>