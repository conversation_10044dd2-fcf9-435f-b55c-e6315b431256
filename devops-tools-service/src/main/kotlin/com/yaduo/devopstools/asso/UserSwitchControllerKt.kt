package com.yaduo.devopstools.asso

import cn.hutool.json.JSON
import cn.hutool.json.JSONUtil
import com.yaduo.devopstools.client.OldRbacApi
import com.yaduo.infras.core.base.bean.AtourRequest
import com.yaduo.infras.core.base.bean.AtourResponse
import com.yaduo.infras.core.base.bean.RequestList
import com.yaduo.infras.core.logging.util.RPCContext
import com.yaduo.user.biz.api.dto.old.OldRbacUserBO
import com.yaduo.user.biz.api.query.old.RbacV1UserQuery
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.RequestBody.Companion.toRequestBody
import okhttp3.Response
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.redis.core.StringRedisTemplate
import org.springframework.validation.annotation.Validated
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController
import java.time.Duration
import javax.annotation.Resource
import javax.servlet.http.Cookie
import javax.servlet.http.HttpServletResponse

/**
 * 用户切换控制器 - Kotlin版本
 * 
 * 这是原始Java UserSwitchController的Kotlin重写版本，
 * 展示了Kotlin的语言特性和与Java的互操作性
 * 
 * <AUTHOR>
 */
@RestController
@Validated
class UserSwitchControllerKt {

    companion object {
        private val log = LoggerFactory.getLogger(UserSwitchControllerKt::class.java)
        
        // 常量定义 - 使用Kotlin的const val
        const val REDIS_TIME_OUT = 3 * 30 * 24 * 60 * 60
        const val COOKIE_ASSO_TOKEN = "asso_token"
        
        // API URLs - 使用Kotlin的字符串常量
        private const val VERIFICATION_CODE_URL = "https://qa-api-asso.corp.at-our.com/verificationCode/verificationCodeForLogin"
        private const val LOGIN_URL = "https://qa-api-asso.corp.at-our.com/login/loginByVerificationCode/v2"
        private const val ORIGIN_URL = "https://qa-asso.corp.at-our.com"
        private const val USER_AGENT = "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36"
    }

    @Resource
    private lateinit var oldRbacApi: OldRbacApi
    
    @Autowired
    private lateinit var redisAssoStringRedisTemplate: StringRedisTemplate

    /**
     * 查询用户 - Kotlin版本
     * 使用Kotlin的空安全、扩展函数和简洁语法
     */
    @GetMapping("api/user/query-kt")
    fun queryUser(@RequestParam keyword: String): List<OldRbacUserBO>? {
        log.info("查询用户，关键词: {}", keyword)
        
        // 使用Kotlin的let和takeIf进行空安全处理
        return queryByAliasKeyword(keyword)
            ?: queryByUserName(keyword)
            ?: queryByEmployeeId(keyword)
    }

    /**
     * 根据别名关键词查询
     */
    private fun queryByAliasKeyword(keyword: String): List<OldRbacUserBO>? {
        val query = createUserQuery { aliasKeyWord(keyword) }
        return executeUserQuery(query)
    }

    /**
     * 根据用户名查询
     */
    private fun queryByUserName(keyword: String): List<OldRbacUserBO>? {
        val query = createUserQuery { userName(keyword) }
        return executeUserQuery(query)
    }

    /**
     * 根据员工ID查询
     */
    private fun queryByEmployeeId(keyword: String): List<OldRbacUserBO>? {
        val query = createUserQuery { employeeId(listOf(keyword)) }
        return executeUserQuery(query)
    }

    /**
     * 创建用户查询请求 - 使用高阶函数简化代码
     */
    private fun createUserQuery(builderAction: RbacV1UserQuery.RbacV1UserQueryBuilder.() -> Unit): AtourRequest<RequestList<RbacV1UserQuery>> {
        val queryBuilder = RbacV1UserQuery.builder()
        queryBuilder.builderAction()
        return RPCContext.createRequest(RequestList.create(queryBuilder.build()))
    }

    /**
     * 执行用户查询 - 使用Kotlin的空安全操作符
     */
    private fun executeUserQuery(query: AtourRequest<RequestList<RbacV1UserQuery>>): List<OldRbacUserBO>? {
        return oldRbacApi.queryOldUser(query)
            .data
            ?.content
            ?.takeIf { it.isNotEmpty() }
    }

    /**
     * 用户切换功能 - Kotlin版本
     * 使用Kotlin的字符串模板、作用域函数和异常处理
     */
    @GetMapping("user_switch_kt/{system}/{user}")
    fun switchToUser(
        @PathVariable("user") loginUser: String,
        @RequestParam(value = "url", required = false) url: String?,
        @PathVariable("system") system: String,
        response: HttpServletResponse
    ): Any {
        log.info("开始切换用户: {} 到系统: {}", loginUser, system)
        
        return try {
            val client = createHttpClient()
            
            // 步骤1: 获取验证码
            val verificationCode = getVerificationCode(client, loginUser, system)
                ?: return AtourResponse.successResponse("获取验证码失败")
            
            // 步骤2: 使用验证码登录
            val loginResult = performLogin(client, loginUser, verificationCode, system)
            
            // 步骤3: 处理登录结果
            handleLoginResult(loginResult, response, url, loginUser)
            return JSONUtil.parseObj(loginResult)
        } catch (e: Exception) {
            log.error("用户切换失败", e)
            AtourResponse.successResponse("错误: ${e.message}")
        }
    }

    //    POST /login/loginByVerificationCode
    @RequestMapping("/login/loginByVerificationCode")
    private fun performLogin(identification: String, system: String, response: HttpServletResponse): Any {
        return switchToUser(identification, "", system, response)
    }


    /**
     * 创建HTTP客户端 - 使用Kotlin的apply作用域函数
     */
    private fun createHttpClient(): OkHttpClient {
        return OkHttpClient.Builder().apply {
            connectTimeout(Duration.ofSeconds(10))
            readTimeout(Duration.ofSeconds(30))
            writeTimeout(Duration.ofSeconds(30))
        }.build()
    }

    /**
     * 获取验证码
     */
    private fun getVerificationCode(client: OkHttpClient, loginUser: String, system: String): String? {
        val request = Request.Builder()
            .url("$VERIFICATION_CODE_URL?identification=$loginUser&system=$system")
            .addCommonHeaders()
            .build()

        return client.newCall(request).execute().use { response ->
            log.info("验证码响应: {}", response.body?.string())
            // 从Redis获取验证码
            redisAssoStringRedisTemplate.opsForValue()
                .get("verification_code_${loginUser}_LOGIN")
        }
    }

    /**
     * 执行登录
     */
    private fun performLogin(client: OkHttpClient, loginUser: String, verificationCode: String, system: String): Response {
        val loginJson = """{"identification":"$loginUser","verificationCode":"$verificationCode","system":"$system"}"""
        val requestBody = loginJson.toRequestBody("application/json;charset=UTF-8".toMediaType())

        val request = Request.Builder()
            .url("$LOGIN_URL?system=HLM")
            .addCommonHeaders()
            .addHeader("content-type", "application/json;charset=UTF-8")
            .post(requestBody)
            .build()

        return client.newCall(request).execute().also { response ->
            log.info("登录响应: {}", response.body?.string())
        }
    }

    /**
     * 处理登录结果
     */
    private fun handleLoginResult(
        loginResponse: Response, 
        httpResponse: HttpServletResponse, 
        redirectUrl: String?, 
        loginUser: String
    ): AtourResponse<String> {
        val cookies = loginResponse.headers("Set-Cookie")
        
        return if (cookies.isNotEmpty()) {
            log.info("获取到cookies: {}", cookies)
            parseCookiesAndAddToResponse(httpResponse, cookies)
            
            // 如果有重定向URL，执行重定向
            redirectUrl?.let { httpResponse.sendRedirect(it) }
            
            AtourResponse.successResponse("成功切换到用户: $loginUser")
        } else {
            AtourResponse.successResponse("用户切换失败")
        }
    }

    /**
     * 为请求添加通用头部 - 使用Kotlin扩展函数
     */
    private fun Request.Builder.addCommonHeaders(): Request.Builder = apply {
        addHeader("accept", "application/json, text/plain, */*")
        addHeader("origin", ORIGIN_URL)
        addHeader("referer", "$ORIGIN_URL/")
        addHeader("user-agent", USER_AGENT)
    }

    /**
     * 创建Cookie - 使用Kotlin的apply作用域函数
     */
    private fun createCookie(httpServletResponse: HttpServletResponse, sessionKey: String) {
        val assoToken = Cookie(COOKIE_ASSO_TOKEN, sessionKey).apply {
            domain = "at-our.com"
            maxAge = REDIS_TIME_OUT
            path = "/"
            isHttpOnly = true
            secure = true
        }
        httpServletResponse.addCookie(assoToken)
    }

    /**
     * 解析Cookie并添加到响应 - 使用Kotlin的集合操作和字符串处理
     */
    private fun parseCookiesAndAddToResponse(response: HttpServletResponse, cookieHeaders: List<String>) {
        if (cookieHeaders.isEmpty()) return

        cookieHeaders.forEach { cookieHeader ->
            parseSingleCookie(cookieHeader)?.let { cookie ->
                response.addCookie(cookie)
                log.debug("添加Cookie: {}", cookie.name)
            }
        }
    }

    /**
     * 解析单个Cookie - 使用Kotlin的字符串处理和数据类
     */
    private fun parseSingleCookie(cookieHeader: String): Cookie? {
        val cookieParts = cookieHeader.split(";").map { it.trim() }
        if (cookieParts.isEmpty()) return null

        // 解析Cookie名称和值
        val nameValue = cookieParts[0].split("=", limit = 2)
        if (nameValue.size != 2) return null

        val (cookieName, cookieValue) = nameValue
        val cookie = Cookie(cookieName.trim(), cookieValue.trim())

        // 处理Cookie属性 - 使用when表达式
        cookieParts.drop(1).forEach { part ->
            when {
                part.startsWith("Domain=") -> cookie.domain = part.substringAfter("Domain=")
                part.startsWith("Path=") -> cookie.path = part.substringAfter("Path=")
                part.startsWith("Max-Age=") -> {
                    part.substringAfter("Max-Age=").toIntOrNull()?.let { maxAge ->
                        cookie.maxAge = maxAge
                    } ?: log.warn("无效的Max-Age: {}", part)
                }
                part.equals("Secure", ignoreCase = true) -> cookie.secure = false
                part.equals("HttpOnly", ignoreCase = true) -> cookie.isHttpOnly = false
            }
        }

        // 设置默认值
        cookie.secure = false
        cookie.isHttpOnly = false

        return cookie
    }
}
