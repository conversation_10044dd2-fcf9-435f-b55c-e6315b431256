package com.yaduo.devopstools.hello

import org.springframework.stereotype.Service
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter

/**
 * Kotlin服务类示例
 * 展示Kotlin语言特性与Spring的集成
 */
@Service
class HelloService {

    /**
     * 生成问候消息
     */
    fun generateGreeting(name: String?, language: String = "Kotlin"): String {
        val actualName = name?.takeIf { it.isNotBlank() } ?: "Anonymous"
        return "Hello, $actualName! Welcome to $language world!"
    }

    /**
     * 获取系统信息
     */
    fun getSystemInfo(): SystemInfo {
        return SystemInfo(
            currentTime = LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME),
            javaVersion = System.getProperty("java.version"),
            kotlinVersion = KotlinVersion.CURRENT.toString(),
            osName = System.getProperty("os.name"),
            availableProcessors = Runtime.getRuntime().availableProcessors()
        )
    }

    /**
     * 演示Kotlin的扩展函数
     */
    fun String.isValidName(): Boolean {
        return this.isNotBlank() && this.length >= 2 && this.all { it.isLetter() || it.isWhitespace() }
    }

    /**
     * 验证用户名
     */
    fun validateUserName(name: String): ValidationResult {
        return when {
            name.isBlank() -> ValidationResult(false, "Name cannot be empty")
            name.length < 2 -> ValidationResult(false, "Name must be at least 2 characters")
            !name.isValidName() -> ValidationResult(false, "Name can only contain letters and spaces")
            else -> ValidationResult(true, "Valid name")
        }
    }

    /**
     * 系统信息数据类
     */
    data class SystemInfo(
        val currentTime: String,
        val javaVersion: String,
        val kotlinVersion: String,
        val osName: String,
        val availableProcessors: Int
    )

    /**
     * 验证结果数据类
     */
    data class ValidationResult(
        val isValid: Boolean,
        val message: String
    )
}
