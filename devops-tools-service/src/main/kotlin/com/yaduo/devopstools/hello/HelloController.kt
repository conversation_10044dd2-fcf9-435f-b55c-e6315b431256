package com.yaduo.devopstools.hello

import com.yaduo.infras.core.base.bean.AtourResponse
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController

/**
 * Kotlin Hello World Controller
 * 演示Kotlin与Spring Boot的集成
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/hello")
class HelloController {

    @Autowired
    private lateinit var helloService: HelloService

    /**
     * 简单的Hello World接口
     */
    @GetMapping("/world")
    fun helloWorld(): AtourResponse<String> {
        return AtourResponse.successResponse("Hello World from Kotlin!")
    }

    /**
     * 带参数的Hello接口
     */
    @GetMapping("/greet")
    fun greet(@RequestParam(defaultValue = "World") name: String): AtourResponse<Map<String, Any>> {
        val response = mapOf(
            "message" to "Hello, $name!",
            "language" to "Kotlin",
            "framework" to "Spring Boot",
            "timestamp" to System.currentTimeMillis()
        )
        return AtourResponse.successResponse(response)
    }

    /**
     * 路径参数示例
     */
    @GetMapping("/user/{userId}")
    fun greetUser(@PathVariable userId: String): AtourResponse<UserGreeting> {
        val greeting = UserGreeting(
            userId = userId,
            message = "Hello, User $userId!",
            isKotlin = true
        )
        return AtourResponse.successResponse(greeting)
    }

    /**
     * 获取系统信息
     */
    @GetMapping("/system")
    fun getSystemInfo(): AtourResponse<HelloService.SystemInfo> {
        return AtourResponse.successResponse(helloService.getSystemInfo())
    }

    /**
     * 验证用户名
     */
    @PostMapping("/validate")
    fun validateName(@RequestBody request: NameValidationRequest): AtourResponse<HelloService.ValidationResult> {
        return AtourResponse.successResponse(helloService.validateUserName(request.name))
    }

    /**
     * 使用服务生成问候语
     */
    @GetMapping("/service-greet")
    fun serviceGreet(@RequestParam(required = false) name: String?): AtourResponse<String> {
        val greeting = helloService.generateGreeting(name)
        return AtourResponse.successResponse(greeting)
    }

    /**
     * 展示Kotlin数据类的使用
     */
    data class UserGreeting(
        val userId: String,
        val message: String,
        val isKotlin: Boolean,
        val createdAt: Long = System.currentTimeMillis()
    )

    /**
     * 名称验证请求
     */
    data class NameValidationRequest(
        val name: String
    )
}
