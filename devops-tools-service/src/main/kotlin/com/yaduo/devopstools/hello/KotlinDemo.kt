package com.yaduo.devopstools.hello

/**
 * Kotlin演示程序
 * 展示Kotlin语言特性和与Java的互操作性
 */
fun main() {
    println("🎉 Kotlin集成成功！")
    println("=".repeat(50))
    
    // 演示Kotlin基本特性
    demonstrateKotlinFeatures()
    
    println("=".repeat(50))
    println("✅ Kotlin演示完成！")
}

/**
 * 演示Kotlin语言特性
 */
fun demonstrateKotlinFeatures() {
    println("📚 Kotlin语言特性演示:")
    
    // 1. 数据类
    println("\n1. 数据类 (Data Classes):")
    val user = User("Alice", 25, "<EMAIL>")
    println("   用户信息: $user")
    println("   复制用户: ${user.copy(age = 26)}")
    
    // 2. 空安全
    println("\n2. 空安全 (Null Safety):")
    val nullableString: String? = null
    val safeLength = nullableString?.length ?: 0
    println("   空字符串长度: $safeLength")
    
    // 3. 扩展函数
    println("\n3. 扩展函数 (Extension Functions):")
    println("   'Hello'.repeat(3) = ${"Hello".repeat(3)}")
    
    // 4. 高阶函数和Lambda
    println("\n4. 高阶函数和Lambda:")
    val numbers = listOf(1, 2, 3, 4, 5)
    val doubled = numbers.map { it * 2 }
    val evenNumbers = numbers.filter { it % 2 == 0 }
    println("   原始数字: $numbers")
    println("   翻倍结果: $doubled")
    println("   偶数筛选: $evenNumbers")
    
    // 5. when表达式
    println("\n5. when表达式 (When Expression):")
    for (i in 1..3) {
        val description = when (i) {
            1 -> "第一个"
            2 -> "第二个"
            else -> "其他"
        }
        println("   数字 $i: $description")
    }
    
    // 6. 字符串模板
    println("\n6. 字符串模板 (String Templates):")
    val name = "Kotlin"
    val version = "1.8.22"
    println("   欢迎使用 $name 版本 $version!")
    
    // 7. 集合操作
    println("\n7. 集合操作 (Collection Operations):")
    val fruits = listOf("苹果", "香蕉", "橙子", "葡萄")
    println("   水果列表: $fruits")
    println("   第一个水果: ${fruits.first()}")
    println("   最后一个水果: ${fruits.last()}")
    println("   包含苹果: ${"苹果" in fruits}")
    
    // 8. 解构声明
    println("\n8. 解构声明 (Destructuring Declarations):")
    val (name2, age, email) = user
    println("   解构后 - 姓名: $name2, 年龄: $age, 邮箱: $email")
    
    // 9. 范围表达式
    println("\n9. 范围表达式 (Range Expressions):")
    println("   1到5的范围: ${(1..5).toList()}")
    println("   倒序范围: ${(5 downTo 1).toList()}")
    
    // 10. 密封类演示
    println("\n10. 密封类 (Sealed Classes):")
    val results = listOf(
        Result.Success("操作成功"),
        Result.Error("网络错误", 404),
        Result.Loading
    )
    
    results.forEach { result ->
        val message = when (result) {
            is Result.Success -> "✅ ${result.data}"
            is Result.Error -> "❌ ${result.message} (代码: ${result.code})"
            Result.Loading -> "⏳ 加载中..."
        }
        println("   $message")
    }
}

/**
 * 数据类示例
 */
data class User(
    val name: String,
    val age: Int,
    val email: String
)

/**
 * 密封类示例
 */
sealed class Result {
    data class Success(val data: String) : Result()
    data class Error(val message: String, val code: Int) : Result()
    object Loading : Result()
}

/**
 * 扩展函数示例
 */
fun String.repeat(times: Int): String {
    return (1..times).joinToString("") { this }
}

/**
 * 中缀函数示例
 */
infix fun String.times(count: Int): String = repeat(count)
