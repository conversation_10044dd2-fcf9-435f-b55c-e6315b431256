package com.yaduo.devopstools.web

import org.springframework.stereotype.Controller
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.RequestMapping

/**
 * 页面控制器
 * 提供静态页面访问
 * 
 * <AUTHOR>
 */
@Controller
@RequestMapping("/page")
class PageController {

    /**
     * 用户切换页面
     */
    @GetMapping("/user-switch")
    fun userSwitchPage(): String {
        return "redirect:/user-switch/index.html"
    }

    /**
     * 根路径重定向到用户切换页面
     */
    @GetMapping("/")
    fun indexPage(): String {
        return "redirect:/user-switch/index.html"
    }
}
