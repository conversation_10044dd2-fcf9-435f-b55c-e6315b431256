package com.yaduo.devopstools.system.controller;

import com.yaduo.devopstools.system.entity.SystemEntity;
import com.yaduo.devopstools.system.service.SystemService;
import com.yaduo.infras.core.base.bean.AtourResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 系统管理控制器
 * 
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/api/system")
@Validated
public class SystemController {
    
    @Resource
    private SystemService systemService;
    
    /**
     * 获取所有系统列表
     * 
     * @return 系统列表
     */
    @GetMapping("/list")
    public AtourResponse<List<SystemEntity>> getAllSystems() {
        log.info("获取所有系统列表");
        try {
            List<SystemEntity> systems = systemService.getAllSystems();
            return AtourResponse.successResponse(systems);
        } catch (Exception e) {
            log.error("获取系统列表失败", e);
            return AtourResponse.errorResponse("获取系统列表失败: " + e.getMessage());
        }
    }
    
    /**
     * 根据系统code获取系统信息
     * 
     * @param systemCode 系统code
     * @return 系统信息
     */
    @GetMapping("/{systemCode}")
    public AtourResponse<SystemEntity> getSystemByCode(@PathVariable String systemCode) {
        log.info("根据系统code获取系统信息: {}", systemCode);
        try {
            SystemEntity system = systemService.getSystemByCode(systemCode);
            if (system != null) {
                return AtourResponse.successResponse(system);
            } else {
                return AtourResponse.errorResponse("系统不存在: " + systemCode);
            }
        } catch (Exception e) {
            log.error("获取系统信息失败: {}", systemCode, e);
            return AtourResponse.errorResponse("获取系统信息失败: " + e.getMessage());
        }
    }
    
    /**
     * 添加系统
     * 
     * @param system 系统信息
     * @return 添加结果
     */
    @PostMapping("/add")
    public AtourResponse<SystemEntity> addSystem(@RequestBody SystemEntity system) {
        log.info("添加系统: {}", system.getSystem());
        try {
            SystemEntity result = systemService.addSystem(system);
            return AtourResponse.successResponse(result);
        } catch (Exception e) {
            log.error("添加系统失败: {}", system.getSystem(), e);
            return AtourResponse.errorResponse("添加系统失败: " + e.getMessage());
        }
    }
    
    /**
     * 更新系统信息
     * 
     * @param system 系统信息
     * @return 更新结果
     */
    @PutMapping("/update")
    public AtourResponse<SystemEntity> updateSystem(@RequestBody SystemEntity system) {
        log.info("更新系统: {}", system.getId());
        try {
            SystemEntity result = systemService.updateSystem(system);
            return AtourResponse.successResponse(result);
        } catch (Exception e) {
            log.error("更新系统失败: {}", system.getId(), e);
            return AtourResponse.errorResponse("更新系统失败: " + e.getMessage());
        }
    }
    
    /**
     * 删除系统
     * 
     * @param id 系统ID
     * @return 删除结果
     */
    @DeleteMapping("/{id}")
    public AtourResponse<Boolean> deleteSystem(@PathVariable Integer id) {
        log.info("删除系统: {}", id);
        try {
            boolean result = systemService.deleteSystem(id);
            return AtourResponse.successResponse(result);
        } catch (Exception e) {
            log.error("删除系统失败: {}", id, e);
            return AtourResponse.errorResponse("删除系统失败: " + e.getMessage());
        }
    }
}
