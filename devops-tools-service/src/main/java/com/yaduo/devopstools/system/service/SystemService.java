package com.yaduo.devopstools.system.service;

import com.yaduo.devopstools.system.entity.SystemEntity;
import com.yaduo.devopstools.system.mapper.SystemMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 系统服务类
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
public class SystemService {
    
    @Resource
    private SystemMapper systemMapper;
    
    /**
     * 获取所有系统列表
     * 
     * @return 系统列表
     */
    public List<SystemEntity> getAllSystems() {
        try {
            List<SystemEntity> systems = systemMapper.selectAll();
            log.info("查询到 {} 个系统", systems.size());
            return systems;
        } catch (Exception e) {
            log.error("查询系统列表失败", e);
            throw new RuntimeException("查询系统列表失败", e);
        }
    }
    
    /**
     * 根据系统code获取系统信息
     * 
     * @param systemCode 系统code
     * @return 系统信息
     */
    public SystemEntity getSystemByCode(String systemCode) {
        try {
            SystemEntity system = systemMapper.selectBySystem(systemCode);
            if (system != null) {
                log.info("查询到系统: {}", system.getSystem());
            } else {
                log.warn("未找到系统: {}", systemCode);
            }
            return system;
        } catch (Exception e) {
            log.error("根据系统code查询失败: {}", systemCode, e);
            throw new RuntimeException("查询系统信息失败", e);
        }
    }
    
    /**
     * 添加系统
     * 
     * @param system 系统信息
     * @return 添加的系统信息
     */
    public SystemEntity addSystem(SystemEntity system) {
        try {
            // 检查系统code是否已存在
            SystemEntity existingSystem = systemMapper.selectBySystem(system.getSystem());
            if (existingSystem != null) {
                throw new RuntimeException("系统code已存在: " + system.getSystem());
            }
            
            int result = systemMapper.insertSelective(system);
            if (result > 0) {
                log.info("成功添加系统: {}", system.getSystem());
                return system;
            } else {
                throw new RuntimeException("添加系统失败");
            }
        } catch (Exception e) {
            log.error("添加系统失败: {}", system.getSystem(), e);
            throw new RuntimeException("添加系统失败", e);
        }
    }
    
    /**
     * 更新系统信息
     * 
     * @param system 系统信息
     * @return 更新的系统信息
     */
    public SystemEntity updateSystem(SystemEntity system) {
        try {
            int result = systemMapper.updateByPrimaryKeySelective(system);
            if (result > 0) {
                log.info("成功更新系统: {}", system.getSystem());
                return systemMapper.selectByPrimaryKey(system.getId());
            } else {
                throw new RuntimeException("更新系统失败，系统不存在");
            }
        } catch (Exception e) {
            log.error("更新系统失败: {}", system.getId(), e);
            throw new RuntimeException("更新系统失败", e);
        }
    }
    
    /**
     * 删除系统
     * 
     * @param id 系统ID
     * @return 是否删除成功
     */
    public boolean deleteSystem(Integer id) {
        try {
            SystemEntity system = systemMapper.selectByPrimaryKey(id);
            if (system == null) {
                throw new RuntimeException("系统不存在: " + id);
            }
            
            int result = systemMapper.deleteByPrimaryKey(id);
            if (result > 0) {
                log.info("成功删除系统: {}", system.getSystem());
                return true;
            } else {
                throw new RuntimeException("删除系统失败");
            }
        } catch (Exception e) {
            log.error("删除系统失败: {}", id, e);
            throw new RuntimeException("删除系统失败", e);
        }
    }
}
