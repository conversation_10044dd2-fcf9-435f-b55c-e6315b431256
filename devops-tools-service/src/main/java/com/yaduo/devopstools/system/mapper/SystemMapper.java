package com.yaduo.devopstools.system.mapper;

import com.yaduo.devopstools.system.entity.SystemEntity;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 系统Mapper接口
 * 
 * <AUTHOR>
 */
@Mapper
public interface SystemMapper {
    
    /**
     * 查询所有系统
     * 
     * @return 系统列表
     */
    List<SystemEntity> selectAll();
    
    /**
     * 根据系统code查询
     * 
     * @param system 系统code
     * @return 系统信息
     */
    SystemEntity selectBySystem(String system);
    
    /**
     * 根据主键查询
     * 
     * @param id 主键
     * @return 系统信息
     */
    SystemEntity selectByPrimaryKey(Integer id);
    
    /**
     * 插入系统信息
     * 
     * @param record 系统信息
     * @return 插入条数
     */
    int insertSelective(SystemEntity record);
    
    /**
     * 更新系统信息
     * 
     * @param record 系统信息
     * @return 更新条数
     */
    int updateByPrimaryKeySelective(SystemEntity record);
    
    /**
     * 删除系统信息
     * 
     * @param id 主键
     * @return 删除条数
     */
    int deleteByPrimaryKey(Integer id);
}
