package com.yaduo.devopstools.role.manager;

import com.yaduo.infras.core.base.define.BatchUpdateStatusRequest;
import com.yaduo.devopstools.role.domain.RoleDO;
import com.yaduo.devopstools.role.mapper.RoleMapper;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR> @date 2021-12-06 11:16:41
 */
//@Component
public class RoleManager {

//    @Resource
    private RoleMapper mapperRole;

    /**
     * 批量更新状态
     **/ 
    public void afterModifyStatus(BatchUpdateStatusRequest request) {
    }

    /**
     * 新增
     **/ 
    public void afterInsert(List<RoleDO> dataList) {
    }

    /**
     * 修改
     **/ 
    public void afterUpdate(RoleDO data) {
    }
}
