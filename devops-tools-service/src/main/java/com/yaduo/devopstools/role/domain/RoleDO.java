package com.yaduo.devopstools.role.domain;

//import io.swagger.annotations.ApiModel;
import lombok.*;

/**
 * <AUTHOR> @date 2021-12-06 09:43:30
 */
@Data
@EqualsAndHashCode(callSuper = false)
@AllArgsConstructor
@NoArgsConstructor
@Builder
//@ApiModel(value = "RoleDO", description = "角色")
public class RoleDO {

    /**
     * 自增主键
     */
    private Long id;

    /**
     * 角色名称
     */
    private String roleName;

    /**
     * 数据状态;0，正常；1，不可用；2，过时；4，已删除
     */
    private Integer dataStatus;

    /**
     * 创建人
     */
    private Long createdBy;

    /**
     * 创建时间
     */
    private String gmtCreate;

    /**
     * 更新人
     */
    private Long modifiedBy;

    /**
     * 更新时间
     */
    private String gmtModified;

    /**
     * TraceID
     */
    private String traceId;

}
