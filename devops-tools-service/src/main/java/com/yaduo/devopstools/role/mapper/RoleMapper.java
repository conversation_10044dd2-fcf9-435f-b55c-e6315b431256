package com.yaduo.devopstools.role.mapper;

import com.yaduo.devopstools.api.role.dto.request.RoleMultiRequest;
import com.yaduo.devopstools.api.role.dto.request.RoleRequest;
import com.yaduo.devopstools.role.domain.RoleDO;
import org.apache.ibatis.annotations.Param;

import java.util.Map;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021-12-06 11:26:48
 */
public interface RoleMapper {

    /**
     * 综合查询-分页
     *
     * @param requestModel | 请求条件
     * @param offset | 偏移量
     * @return List<RoleDO>
     * <AUTHOR>
     * @date 2021-12-06 11:26:48
     */
    List<RoleDO> queryPageData(@Param("request_model") RoleRequest requestModel,@Param("offset") int offset);

    /**
     * 综合查询-总条数
     * @param requestModel | 请求条件
     * @return Integer
     * <AUTHOR>
     * @date 2021-12-06 11:26:48
     */
    Integer queryTotalCount(@Param("request_model") RoleRequest requestModel);

    /**
     * 精准匹配-分页
     *
     * @param requestModel | 请求条件
     * @param offset | 偏移量
     * @return List<RoleDO>
     * <AUTHOR>
     * @date 2021-12-06 11:26:48
     */
    List<RoleDO> getExactData(@Param("request_model") RoleMultiRequest requestModel, @Param("offset") int offset);

    /**
     * 精准匹配-总条数
     * @param requestModel | 请求条件
     * @return Integer
     * <AUTHOR>
     * @date 2021-12-06 11:26:48
     */
    Integer getExactTotalCount(@Param("request_model") RoleMultiRequest requestModel);

    /**
     * GroupBy字段
     * @param requestModel | 请求条件
     * @return List<Map<?,?>>
     * <AUTHOR>
     * @date 2021-12-06 11:26:48
     */
    List<Map<?,?>> getCountGroupByField(@Param("request_model") RoleMultiRequest requestModel);

    /**
     * Distinct字段
     * @param record | 输出字段
     * @param conditionModel | 条件字段
     * @param offset | 偏移量
     * @return List<Map<?,?>>
     * <AUTHOR>
     * @date 2021-12-06 11:26:48
     */
    List<Map<?,?>> selectDistinctByConditionModel(@Param("request_model") RoleDO record,@Param("condition_model") RoleMultiRequest conditionModel,@Param("offset") int offset);

    /**
     * Distinct字段-总条数
     * @param record | 输出字段
     * @param conditionModel | 条件字段
     * @return Integer
     * <AUTHOR>
     * @date 2021-12-06 11:26:48
     */
    Integer selectDistinctCountByConditionModel(@Param("request_model") RoleDO record,@Param("condition_model") RoleMultiRequest conditionModel);

    /**
     * 根据主键搜索
     * @param ids | id列表
     * @param dataStatus | 数据状态,see{@link com.yaduo.infras.core.base.define.DataStatus}
     * @return List<RoleDO>
     * <AUTHOR>
     * @date 2021-12-06 11:26:48
     */
    List<RoleDO> selectByPrimaryKey(@Param("ids") List<Long> ids,@Param("dataStatus") Integer dataStatus);

    /**
     * 根据主键批量更新状态
     * @param ids | 主键list
     * @param dataStatus 状态，see{@link com.yaduo.infras.core.base.define.DataStatus}
     * @param modifiedBy | 修改人的UID
     * @return int
     * <AUTHOR>
     * @date 2021-12-06 11:26:48
     */
    int batchUpdateStatus(@Param("ids") List<Long> ids, @Param("data_status") Integer dataStatus, @Param("modifiedBy") Long modifiedBy);

    /**
     * 根据非NullField更新状态
     * @param requestModel | DTO
     * @param dataStatus | 状态，see{@link com.yaduo.infras.core.base.define.DataStatus}
     * @param modifiedBy | 修改人的UID
     * @return int
     * <AUTHOR>
     * @date 2021-12-06 11:26:48
     */
    int updateStatusByNonNullField(@Param("request_model") RoleDO requestModel, @Param("data_status") Integer dataStatus, @Param("modifiedBy") Long modifiedBy);

    /**
     * 物理删除数据
     * @param ids | 主键list
     * @return int
     * <AUTHOR>
     * @date 2021-12-06 11:26:48
     */
    int deletePhysically(@Param("ids") List<Long> ids);

    /**
     * 批量插入
     * @param list | 数组列表
     * @return int
     * <AUTHOR>
     * @date 2021-12-06 11:26:48
     */
    int batchInsert(List<RoleDO> list);

    /**
     * 批量插入
     * @param list | 数组列表
     * @param modifiedBy | 修改人的UID
     * @return int
     * <AUTHOR>
     * @date 2021-12-06 11:26:48
     */
    int batchInsertOrUpdateStatus(@Param("list") List<RoleDO> list, @Param("modifiedBy") Long modifiedBy);

    /**
     * 插入数据，冲突时，更新所有非主键、非null字段
     * @param record | 更新的对象
     * @param modifiedBy | 修改人的UID
     * @return int
     * <AUTHOR>
     * @date 2021-12-06 11:26:48
     */
    int insertOrUpdateNonNullFields(@Param("request_model") RoleDO record,@Param("modifiedBy") Long modifiedBy);

    /**
     * 根据主键更新
     * @param record | 更新的对象
     * @return int
     * <AUTHOR>
     * @date 2021-12-06 11:26:48
     */
    int updateAllFieldsByPrimaryKey(RoleDO record);

    /**
     * 根据条件model更新非null字段
     * @param record | 更新的对象
     * @param conditionModel | 条件对象
     * @param modifiedBy | 修改人的UID
     * @return int
     * <AUTHOR>
     * @date 2021-12-06 11:26:48
     */
    int updateNonNullFieldsByNonNullFields(@Param("request_model") RoleDO record,@Param("condition_model") RoleMultiRequest conditionModel,@Param("modifiedBy") Long modifiedBy);

    /**
     * 根据主键更新非null值
     * @param record | 更新的对象
     * @return int
     * <AUTHOR>
     * @date 2021-12-06 11:26:48
     */
    int updateNonNullFieldsByPrimaryKey(RoleDO record);
}
