package com.yaduo.devopstools;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.web.servlet.ServletComponentScan;
import org.springframework.cloud.netflix.eureka.EnableEurekaClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ImportResource;

/**
 * 主服务入口
 *
 * <AUTHOR>
 */
@SpringBootApplication(scanBasePackages = {"com.yaduo.devopstools", "com.yaduo.infras.core", "feign"})
@ServletComponentScan(basePackages = {"com.yaduo.infras.core"})
@ImportResource({"classpath:spring-mybatis.xml","classpath:spring-aop.xml"})
@EnableFeignClients
@EnableEurekaClient
public class Application {

    public static void main(String[] args) {
        SpringApplication.run(Application.class, args);
    }

}
