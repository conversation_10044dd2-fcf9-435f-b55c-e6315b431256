package com.yaduo.devopstools.configuration;

import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Info;
import org.springdoc.core.GroupedOpenApi;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @since 2021-12-06 17:16
 */
@Configuration
public class OpenApiConfig {
    @Bean
    public OpenAPI initOpenAPI() {
        return new OpenAPI().info(
                new Info().title("OnesTools Project").description("一键工具系列").version("v1.0")
        );
    }
    @Bean
    public GroupedOpenApi publicApi() {
        return GroupedOpenApi.builder()
                .group("public")
                .packagesToScan("com.yaduo.devopstools")
                .build();
    }

    @Bean
    public GroupedOpenApi onesApi() {
        return GroupedOpenApi.builder()
                .group("ones")
                .packagesToScan("com.yaduo.devopstools.ones")
                .build();
    }
}
