package com.yaduo.devopstools.configuration;

import com.ctrip.framework.apollo.openapi.client.ApolloOpenApiClient;
import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;


@Configuration
@EnableApolloConfig(value = "application")
public class ApolloConfig {

    @Value("${apollo.portalUrl:https://qa-apollo.at-our.com}")
    private String portalUrl;
    @Value("${pms-TECH-share-apollo-open-api-token:8eb3d25e685680009fe16fd911d6ed548ee7b0a9}")
    private String pmsTechShareApolloOpenApiToken;

    @Bean
    public ApolloOpenApiClient pmsTechShareApolloOpenApiClient() {
        return ApolloOpenApiClient.newBuilder()
                .withPortalUrl(portalUrl)
                .withToken(pmsTechShareApolloOpenApiToken)
                .build();
    }
}