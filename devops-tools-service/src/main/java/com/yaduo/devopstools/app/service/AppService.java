package com.yaduo.devopstools.app.service;

import com.yaduo.devopstools.app.dto.AppDeployDTO;
import com.yaduo.devopstools.app.entity.AppDeployEntity;
import com.yaduo.devopstools.app.mapper.AppDeployMapper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class AppService {

    @Resource
    private AppDeployMapper appDeployMapper;

    public AppDeployEntity insertOrUpdateAppDeploy(AppDeployDTO appDeployDTO) {
        AppDeployEntity appDeployEntity = new AppDeployEntity();
        appDeployEntity.setId(null);
        appDeployEntity.setAppId(appDeployDTO.getAppId());
        appDeployEntity.setEnv(appDeployDTO.getEnv());
        appDeployEntity.setBranch(appDeployDTO.getBranch());
        appDeployEntity.setRemark(appDeployDTO.getRemark());
        appDeployEntity.setOperateUser(appDeployDTO.getOperateUser());
        appDeployEntity.setCreateTime(null);
        appDeployEntity.setUpdateTime(null);
        appDeployMapper.insertOrUpdateSelective(appDeployEntity);
        return appDeployEntity;
    }

    public List<AppDeployEntity> queryList(AppDeployDTO appDeployDTO) {
        return appDeployMapper.selectAllOrderByUpdateTimeDesc();
    }
}
