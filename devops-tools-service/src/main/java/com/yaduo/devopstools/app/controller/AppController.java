package com.yaduo.devopstools.app.controller;

import com.yaduo.devopstools.app.dto.AppDeployDTO;
import com.yaduo.devopstools.app.entity.AppDeployEntity;
import com.yaduo.devopstools.app.service.AppService;
import com.yaduo.infras.core.base.bean.AtourRequest;
import com.yaduo.infras.core.base.bean.AtourResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@RestController
@Validated
public class AppController {

    @Resource
    private AppService appService;

    /**
     * 更新应用部署信息
     *
     * @param atourRequest
     * @return
     */
    @PostMapping("app/update_deploy")
    public AtourResponse<Object> updateDeploy(@RequestBody AtourRequest<AppDeployDTO> atourRequest) {
        return AtourResponse.successResponse(appService.insertOrUpdateAppDeploy(atourRequest.getModel()));
    }


    /**
     * 查询实例信息
     *
     * @param atourRequest
     * @return
     */
    @PostMapping("app/list")
    public AtourResponse<List<AppDeployEntity>> list(@RequestBody AtourRequest<AppDeployDTO> atourRequest) {
        return AtourResponse.successResponse(appService.queryList(atourRequest.getModel()));
    }


}
