package com.yaduo.devopstools.app.service;

import com.alibaba.fastjson.JSON;
import com.ctrip.framework.apollo.openapi.client.ApolloOpenApiClient;
import com.ctrip.framework.apollo.openapi.dto.NamespaceGrayDelReleaseDTO;
import com.ctrip.framework.apollo.openapi.dto.OpenItemDTO;
import com.yaduo.infras.core.logging.Loggers;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 *
 */
@Slf4j
@Service
public class Apollo4AssoService {

    private static final String clusterName = "default";

    @Value("${apollo-profile:DEV}")
    private String apolloProfile;
    private static final String NEW_AND_OLD_DB_RELATION = "new.old.db.relation";
    private static final String asso_mock_account = "mock.account";
    private static final String asso_app_id = "ASSO";
    private static final String asso_namespace = "application";

    @Resource
    private ApolloOpenApiClient pmsTechShareApolloOpenApiClient;

    /**
     * 增加新的账号
     *
     * @param account
     */
    public void newMockAccount(String account) {
        //获取现在apollo的配置
        OpenItemDTO item = pmsTechShareApolloOpenApiClient.getItem(asso_app_id, apolloProfile, clusterName, asso_namespace, asso_mock_account);
        String jsonString = item.getValue();
        List<String> accountList = JSON.parseArray(jsonString, String.class);
        if (accountList.contains(account)) {
            return;
        }
        Loggers.BIZ.infoF("{}修改前：{}", asso_mock_account,jsonString);
        accountList.add(account);
        String json = JSON.toJSONString(accountList);
        Loggers.BIZ.infoF("{}修改后：{}", asso_mock_account, json);
        try {
            //update
            OpenItemDTO openItemDTO = new OpenItemDTO();
            openItemDTO.setKey(asso_mock_account);
            openItemDTO.setValue(json);
            openItemDTO.setDataChangeLastModifiedBy("dingshui.zhang");
            openItemDTO.setDataChangeLastModifiedTime(new Date());
            openItemDTO.setComment(String.format("自动添加 %s", account));
            pmsTechShareApolloOpenApiClient.updateItem(asso_app_id, apolloProfile, clusterName, asso_namespace, openItemDTO);

            //publish
            NamespaceGrayDelReleaseDTO namespaceGrayDelReleaseDTO = new NamespaceGrayDelReleaseDTO();
            namespaceGrayDelReleaseDTO.setReleaseTitle("release by apollo open api");
            namespaceGrayDelReleaseDTO.setReleaseComment("release by apollo-open-api");
            namespaceGrayDelReleaseDTO.setReleasedBy("dingshui.zhang");
            pmsTechShareApolloOpenApiClient.publishNamespace(asso_app_id, apolloProfile, clusterName, asso_namespace,
                    namespaceGrayDelReleaseDTO);
        } catch (Exception e) {
            Loggers.BIZ.errorF("自动修改apollo配置异常{}_{}_{}_{},", apolloProfile, asso_app_id, asso_namespace, NEW_AND_OLD_DB_RELATION, e);
            throw e;
        }

    }
}
