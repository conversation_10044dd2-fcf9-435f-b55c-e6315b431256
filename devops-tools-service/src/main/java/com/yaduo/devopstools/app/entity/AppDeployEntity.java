package com.yaduo.devopstools.app.entity;

import java.util.Date;
import lombok.Data;

/**
 * 应用部署情况
 */
@Data
public class AppDeployEntity {
    /**
     * id
     */
    private Long id;

    /**
     * 应用id
     */
    private String appId;

    /**
     * 环境
     */
    private String env;

    /**
     * 部署分支
     */
    private String branch;

    /**
     * 备注
     */
    private String remark;

    /**
     * 操作人
     */
    private String operateUser;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
}