package com.yaduo.devopstools.app.mapper;

import com.yaduo.devopstools.app.entity.AppDeployEntity;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface AppDeployMapper {
    /**
     * delete by primary key
     *
     * @param id primaryKey
     * @return deleteCount
     */
    int deleteByPrimaryKey(Long id);

    /**
     * insert record to table selective
     *
     * @param record the record
     * @return insert count
     */
    int insertSelective(AppDeployEntity record);

    /**
     * select by primary key
     *
     * @param id primary key
     * @return object by primary key
     */
    AppDeployEntity selectByPrimaryKey(Long id);

    /**
     * update record selective
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(AppDeployEntity record);

    AppDeployEntity selectOneByAppIdAndEnv(@Param("appId") String appId, @Param("env") String env);

    List<AppDeployEntity> selectAllOrderByUpdateTimeDesc();

    int insertOrUpdate(AppDeployEntity record);

    int insertOrUpdateSelective(AppDeployEntity record);
}