package com.yaduo.devopstools.app.controller;

import com.yaduo.devopstools.app.service.Apollo4AssoService;
import com.yaduo.infras.core.base.bean.AtourRequest;
import com.yaduo.infras.core.base.bean.AtourResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@Slf4j
@RestController
@Validated
public class MockAccountController {

    @Resource
    private Apollo4AssoService apollo4AssoService;

    /**
     * 添加mock_account
     *
     * @param atourRequest
     * @return
     */
    @PostMapping("mock_account/add")
    public AtourResponse<Object> add(@RequestBody AtourRequest<String> atourRequest) {
        apollo4AssoService.newMockAccount(atourRequest.getModel());
        return AtourResponse.successResponse();
    }

}
