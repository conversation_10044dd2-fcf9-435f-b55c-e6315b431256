package com.yaduo.devopstools.asso;

import com.yaduo.devopstools.client.OldRbacApi;
import com.yaduo.infras.core.base.bean.AtourRequest;
import com.yaduo.infras.core.base.bean.AtourResponse;
import com.yaduo.infras.core.base.bean.RequestList;
import com.yaduo.infras.core.base.bean.ResponseList;
import com.yaduo.infras.core.logging.util.RPCContext;
import com.yaduo.user.biz.api.dto.old.OldRbacUserBO;
import com.yaduo.user.biz.api.query.old.RbacV1UserQuery;
import lombok.Builder.Default;
import lombok.extern.slf4j.Slf4j;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletResponse;
import java.time.Duration;
import java.util.Arrays;
import java.util.List;

@Slf4j
@RestController
@Validated
public class UserSwitchController {


    @Resource
    private OldRbacApi oldRbacApi;
    @Autowired
    private StringRedisTemplate redisAssoStringRedisTemplate;



    @GetMapping("api/user/query")
    public List queryUser(String keyword) {
        AtourRequest<RequestList<RbacV1UserQuery>> query = RPCContext.createRequest(RequestList.create(RbacV1UserQuery.builder()
                .aliasKeyWord(keyword)
                .build()));
        AtourResponse<ResponseList<OldRbacUserBO>> atourResponse = oldRbacApi.queryOldUser(query);
        if (ObjectUtils.isNotEmpty(atourResponse.getData())) {
            if (CollectionUtils.isNotEmpty(atourResponse.getData().getContent())) {
                return atourResponse.getData().getContent();
            }
        }

        AtourRequest<RequestList<RbacV1UserQuery>> queryUserName = RPCContext.createRequest(RequestList.create(RbacV1UserQuery.builder()
                .userName(keyword)
                .build()));
        atourResponse = oldRbacApi.queryOldUser(queryUserName);

        if (ObjectUtils.isNotEmpty(atourResponse.getData())) {
            if (CollectionUtils.isNotEmpty(atourResponse.getData().getContent())) {
                return atourResponse.getData().getContent();
            }
        }


        AtourRequest<RequestList<RbacV1UserQuery>> employeeId = RPCContext.createRequest(RequestList.create(RbacV1UserQuery.builder()
                .employeeId(Arrays.asList(keyword))
                .build()));
        atourResponse = oldRbacApi.queryOldUser(employeeId);

        if (ObjectUtils.isNotEmpty(atourResponse.getData())) {
            if (CollectionUtils.isNotEmpty(atourResponse.getData().getContent())) {
                return atourResponse.getData().getContent();
            }
        }
        return null;
    }

    public static final int REDIS_TIME_OUT = 3 * 30 * 24 * 60 * 60;
    public static final String COOKIE_ASSO_TOKEN = "asso_token";

    private void createCookie(HttpServletResponse httpServletResponse, String sessionKey) {
        javax.servlet.http.Cookie assoToken = new Cookie(COOKIE_ASSO_TOKEN, sessionKey);
        assoToken.setDomain("at-our.com");
        //24小时有效
        assoToken.setMaxAge(REDIS_TIME_OUT);
        assoToken.setPath("/");
        assoToken.setHttpOnly(true);
        assoToken.setSecure(true);
        httpServletResponse.addCookie(assoToken);
    }


    @GetMapping("user_switch/{system}/{user}")
    public AtourResponse<String> switchToUser(@PathVariable("user") String loginUser,
                                              @RequestParam(value = "url", required = false) String url,
                                              @PathVariable("system") String system, HttpServletResponse response) {
        try {
            // Create OkHttpClient
            OkHttpClient client = new OkHttpClient().newBuilder()
                    .connectTimeout(Duration.ofSeconds(10))
                    .readTimeout(Duration.ofSeconds(30))
                    .writeTimeout(Duration.ofSeconds(30))
                    .build();

            // Step 1: Get verification code
            Request verificationRequest = new Request.Builder()
                    .url("https://qa-api-asso.corp.at-our.com/verificationCode/verificationCodeForLogin?identification=" + loginUser + "&system=" + system)
                    .addHeader("accept", "application/json, text/plain, */*")
                    .addHeader("origin", "https://qa-asso.corp.at-our.com")
                    .addHeader("referer", "https://qa-asso.corp.at-our.com/")
                    .addHeader("user-agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36")
                    .build();

            Response verificationResponse = client.newCall(verificationRequest).execute();
            log.info("Verification code response: {}", verificationResponse.body().string());

            // In real scenario, we would parse the response and get the verification code
            // For demo purposes, using "1" as verification code
            String verificationCode = redisAssoStringRedisTemplate.opsForValue().get("verification_code_" + loginUser + "_LOGIN");

            // Step 2: Login with verification code
            MediaType mediaType = MediaType.parse("application/json;charset=UTF-8");
            String loginJson = String.format("{\"identification\":\"%s\",\"verificationCode\":\"%s\",\"system\":\"" + system + "\"}",
                    loginUser, verificationCode);
            RequestBody loginBody = RequestBody.create(loginJson, mediaType);

            Request loginRequest = new Request.Builder()
                    .url("https://qa-api-asso.corp.at-our.com/login/loginByVerificationCode/v2?system=HLM")
                    .addHeader("accept", "application/json, text/plain, */*")
                    .addHeader("content-type", "application/json;charset=UTF-8")
                    .addHeader("origin", "https://qa-asso.corp.at-our.com")
                    .addHeader("referer", "https://qa-asso.corp.at-our.com/")
                    .addHeader("user-agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36")
                    .post(loginBody)
                    .build();

            Response loginResponse = client.newCall(loginRequest).execute();
            String loginResponseBody = loginResponse.body().string();
            log.info("Login response: {}", loginResponseBody);

            // Extract cookies from response
            List<String> cookies = loginResponse.headers("Set-Cookie");
            if (cookies != null && !cookies.isEmpty()) {
                // Store cookies in Redis for later use
                System.out.println(cookies);
                parseCookiesAndAddToResponse(response, cookies);
                if(ObjectUtils.isNotEmpty(url)){
                    response.sendRedirect(url);
                }
                return AtourResponse.successResponse("Successfully switched to user: " + loginUser);
            }

            return AtourResponse.successResponse("Failed to switch user");
        } catch (Exception e) {
            log.error("Error switching user", e);
            return AtourResponse.successResponse("Error: " + e.getMessage());
        }
    }

    /**
     * Parse cookies from Set-Cookie headers and add them to the HTTP response
     * @param response HttpServletResponse to add cookies to
     * @param cookieHeaders List of Set-Cookie header values
     */
    private void parseCookiesAndAddToResponse(HttpServletResponse response, List<String> cookieHeaders) {
        if (cookieHeaders == null || cookieHeaders.isEmpty()) {
            return;
        }
        
        for (String cookieHeader : cookieHeaders) {
            // Split the cookie string by semicolons to get individual attributes
            String[] cookieParts = cookieHeader.split(";");
            if (cookieParts.length == 0) {
                continue;
            }
            
            // First part contains the name and value
            String[] nameValue = cookieParts[0].trim().split("=", 2);
            if (nameValue.length != 2) {
                continue;
            }
            
            String cookieName = nameValue[0].trim();
            String cookieValue = nameValue[1].trim();
            
            Cookie cookie = new Cookie(cookieName, cookieValue);
            
            // Process other cookie attributes
            for (int i = 1; i < cookieParts.length; i++) {
                String part = cookieParts[i].trim();
                
                if (part.startsWith("Domain=")) {
                    cookie.setDomain(part.substring("Domain=".length()));
                } else if (part.startsWith("Path=")) {
                    cookie.setPath(part.substring("Path=".length()));
                } else if (part.startsWith("Max-Age=")) {
                    try {
                        int maxAge = Integer.parseInt(part.substring("Max-Age=".length()));
                        cookie.setMaxAge(maxAge);
                    } catch (NumberFormatException e) {
                        log.warn("Invalid Max-Age in cookie: {}", part);
                    }
                } else if (part.equalsIgnoreCase("Secure")) {
                    cookie.setSecure(false);
                } else if (part.equalsIgnoreCase("HttpOnly")) {
                    cookie.setHttpOnly(false);
                }
                // Expires is handled by Max-Age in Java Servlet API
            }
            cookie.setSecure(false);
            cookie.setHttpOnly(false);
            response.addCookie(cookie);
            log.debug("Added cookie: {}", cookieName);
        }
    }
}
