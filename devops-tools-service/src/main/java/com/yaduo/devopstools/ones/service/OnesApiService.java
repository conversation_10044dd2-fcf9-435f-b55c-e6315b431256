package com.yaduo.devopstools.ones.service;

import cn.hutool.core.date.DateTime;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import com.google.common.collect.ImmutableMap;
import com.yaduo.devopstools.client.ApiIMessageChannelWeComService;
import com.yaduo.devopstools.ones.client.OnesApiClient;
import com.yaduo.devopstools.ones.model.AddHourDTO;
import com.yaduo.devopstools.ones.model.AddTaskResponse;
import com.yaduo.devopstools.ones.model.EventMsg;
import com.yaduo.devopstools.ones.model.EventMsg.Message;
import com.yaduo.devopstools.ones.model.ImportParam;
import com.yaduo.devopstools.ones.model.LibraryResponse;
import com.yaduo.devopstools.ones.model.LibraryResponse.Libraries.Modules;
import com.yaduo.devopstools.ones.model.Project;
import com.yaduo.devopstools.ones.model.Task;
import com.yaduo.devopstools.ones.model.Task.FieldValues;
import com.yaduo.devopstools.ones.model.TaskAdd2TaskRequest;
import com.yaduo.devopstools.ones.model.TaskAdd2Tasks;
import com.yaduo.devopstools.ones.model.TasksAdd2AddManhours;
import com.yaduo.devopstools.ones.model.TasksAdd2FieldValues;
import com.yaduo.devopstools.ones.model.TestCaseInfo;
import com.yaduo.devopstools.ones.model.TokenInfo;
import com.yaduo.devopstools.ones.model.TokenInfo.User;
import com.yaduo.devopstools.ones.model.test.Cases;
import com.yaduo.devopstools.ones.model.test.Plans;
import com.yaduo.devopstools.ones.model.test.TestCaseResponse;
import com.yaduo.devopstools.ones.model.test.TestPlanResponse;
import com.yaduo.devopstools.utils.DateUtil;
import com.yaduo.infras.core.base.bean.AtourResponse;
import com.yaduo.infras.core.logging.Loggers;
import com.yaduo.infras.core.logging.util.RPCContext;
import com.yaduo.infras.message.api.dto.MessageUserDTO;
import com.yaduo.infras.message.api.wecom.dto.MessageBodyWeCom;
import com.yaduo.infras.message.api.wecom.dto.MessageContentWeCom;
import com.yaduo.infras.message.api.wecom.dto.WeComMessageType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022-04-17 17:38
 */
@Service
@Slf4j
public class OnesApiService {

    private OnesApiClient onesApiClient;

    /**
     * 接收人白名单
     */
    @ApolloJsonValue("${ones_webhook.white_emails:[]}")
    private Set<String> hookWhiteEmails;

    /**
     * 接收人黑名单
     */
    @ApolloJsonValue("${ones_webhook.black_emails:[]}")
    private Set<String> hookBlackEmails;

    @ApolloJsonValue("${ones.field-name-map:{}}")
    private Map<String,String> fieldNameMap;

    @Autowired
    private ApiIMessageChannelWeComService apiIMessageChannelWeComService;

    @Value("${ones.cookie}")
    public void setOnesCookie(String cookie) {
        onesApiClient = new OnesApiClient(cookie);
        try {
            refresh();
        } catch (Exception e) {
            Loggers.BIZ.errorF("error {}", cookie, e);
        }
    }

    public void refresh() {
        refreshUserCache();
        List<Project> projects = onesApiClient.projects("KMgnuFDd");
        projectMap = projects.stream().collect(Collectors.toMap(Project::getUuid, Project::getName));
        LibraryResponse libraryResponse = onesApiClient.libraries("KMgnuFDd");
        libraryResponse.getLibraries().forEach(libraries -> {
            if (libraries.getModules() != null) {
                libraries.getModules().forEach(modules -> {
                    modules.setLibraryName(libraries.getName());
                    modulesMap.put(modules.getUuid(), modules);
                });
            }
        });
    }

    private synchronized void refreshUserCache() {
        List<User> users = onesApiClient.users();
        uuidUserMap = users.stream().collect(Collectors.toMap(User::getUuid, Function.identity()));
        nameUserMap = users.stream().collect(Collectors.toMap(User::getName, Function.identity()));
    }

//    private LoadingCache<String, User> nameUserMap = CacheBuilder.newBuilder()
//            .expireAfterWrite(1, TimeUnit.DAYS)
//            .build(
//                    CacheLoader.from((key) -> Optional.ofNullable(onesApiClient.users(key)).map(list -> list.get(0)).orElse(null))
//            );

    private volatile Map<String, User> uuidUserMap;
    private Map<String, User> nameUserMap;
    private Map<String, String> projectMap;

    private Map<String, LibraryResponse.Libraries.Modules> modulesMap = new HashMap();


    //常量定义
    private Map<String, String> testCasePriorityMap = ImmutableMap.of("UuvbDFra", "P1", "RTpyBDq3", "P2", "BvJJH3hh", "P3", "Et4MN3oL", "P4");

    private Map<String, String> testCaseTypeMap;
    {
        testCaseTypeMap = new HashMap<>();
        JSON.parseArray("[\n" + "  {\n" + "    \"bgColor\": \"\",\n" + "    \"color\": \"#303030\",\n" + "    \"uuid\": \"Sq8JYhRA\",\n" + "    \"value\": \"功能测试\"\n" + "  },\n" + "  {\n" + "    \"bgColor\": \"\",\n" + "    \"color\": \"#303030\",\n" + "    \"uuid\": \"F8GoKj2Y\",\n" + "    \"value\": \"性能测试\"\n" + "  },\n" + "  {\n" + "    \"bgColor\": \"\",\n" + "    \"color\": \"#303030\",\n" + "    \"uuid\": \"TC4vke1p\",\n" + "    \"value\": \"接口测试\"\n" + "  },\n" + "  {\n" + "    \"bgColor\": \"\",\n" + "    \"color\": \"#303030\",\n" + "    \"uuid\": \"7fHiwtPi\",\n" + "    \"value\": \"配置相关\"\n" + "  },\n" + "  {\n" + "    \"bgColor\": \"\",\n" + "    \"color\": \"#303030\",\n" + "    \"uuid\": \"5e3jR1wu\",\n" + "    \"value\": \"安全测试\"\n" + "  },\n" + "  {\n" + "    \"bgColor\": \"\",\n" + "    \"color\": \"#303030\",\n" + "    \"uuid\": \"3YnAQcwW\",\n" + "    \"value\": \"其他\"\n" + "  }\n" + "]").forEach(item -> {
            JSONObject jsonObject = (JSONObject) item;
            testCaseTypeMap.put(jsonObject.getString("uuid"), jsonObject.getString("value"));
        });
    }

    public boolean importTask(List<ImportParam> taskList) {
        TaskAdd2TaskRequest taskAdd2TaskRequest = new TaskAdd2TaskRequest();
        List<TaskAdd2Tasks> tasks = new ArrayList<>();
        taskAdd2TaskRequest.setTasks(tasks);
        TokenInfo tokenInfo = onesApiClient.tokenInfo();
        taskList.forEach(param -> {

            String parentId = param.getParentId().replace("#", "");
            Task parentTask = onesApiClient.taskDetail(Integer.valueOf(parentId));

            TaskAdd2Tasks taskAdd2Tasks = new TaskAdd2Tasks();
            taskAdd2Tasks.setUuid(tokenInfo.getUser().getUuid() + UUID.randomUUID().toString().replace("-", "").substring(0, 8));
            taskAdd2Tasks.setOwner(tokenInfo.getUser().getUuid());
            User 负责人 = nameUserMap.get(param.getAssign());
            taskAdd2Tasks.setAssign(负责人.getUuid());
            taskAdd2Tasks.setSummary(parentTask.getSummary() + " " + param.getSummary());
            taskAdd2Tasks.setDescRich(param.getDesc());
            taskAdd2Tasks.setParentUuid(parentTask.getUuid());
            taskAdd2Tasks.setProjectUuid(parentTask.getProjectUuid());
            taskAdd2Tasks.setIssueTypeUuid("Jr1wVTR6");
            taskAdd2Tasks.setSubIssueTypeUuid("Jr1wVTR6");
            taskAdd2Tasks.setPriority(parentTask.getPriority());

            TasksAdd2AddManhours hours = new TasksAdd2AddManhours();
            hours.setOwner(负责人.getUuid());
            hours.setMode("detailed");
            hours.setHoursFormat("avg");
            hours.setType("estimated");
            hours.setHours(param.getManHours() * 100000);
            hours.setFrom(param.getPlanStartTime().getTime() / 1000);
            hours.setTo((param.getPlanEndTime().getTime() / 1000));
            taskAdd2Tasks.addAddManhoursItem(hours);
            //优先级 继承父需求
            TasksAdd2FieldValues values1 = new TasksAdd2FieldValues();
            values1.setValue(parentTask.getPriority());
            values1.setType(1);
            values1.setFieldUuid("field012");
            taskAdd2Tasks.addFieldValuesItem(values1);
            //计划开始日期 输入
            TasksAdd2FieldValues values2 = new TasksAdd2FieldValues();
            values2.setValue(String.valueOf(param.getPlanStartTime().getTime() / 1000));
            values2.setType(5);
            values2.setFieldUuid("field027");
            taskAdd2Tasks.addFieldValuesItem(values2);
            //计划结束日期 输入
            TasksAdd2FieldValues values3 = new TasksAdd2FieldValues();
            values3.setValue(String.valueOf(param.getPlanEndTime().getTime() / 1000));
            values3.setType(5);
            values3.setFieldUuid("field028");
            taskAdd2Tasks.addFieldValuesItem(values3);
            //所属产品（多选项） 从父需求获取
            TasksAdd2FieldValues values4 = new TasksAdd2FieldValues();
            Optional<FieldValues> field029 = parentTask.getFieldValues().stream().filter(item -> item.getField_uuid().equals("field029")).findFirst();
            if (field029.isPresent()) {
                FieldValues fieldValues = field029.get();
                values4.setValue(fieldValues.getValue());
                values4.setType(44);
                values4.setFieldUuid("field029");
                taskAdd2Tasks.addFieldValuesItem(values4);
            }

            //需求部门，从父需求 field_values 继承
            TasksAdd2FieldValues values5 = new TasksAdd2FieldValues();
            Optional<FieldValues> qYd4rvwQ = parentTask.getFieldValues().stream().filter(item -> item.getField_uuid().equals("QYd4rvwQ")).findFirst();
            if (qYd4rvwQ.isPresent()) {
                values5.setValue(qYd4rvwQ.get().getValue());
                values5.setType(1);
                values5.setFieldUuid("QYd4rvwQ");
                taskAdd2Tasks.addFieldValuesItem(values5);
            }
            //工时进度
            TasksAdd2FieldValues values6 = new TasksAdd2FieldValues();
            values6.setValue("0");
            values6.setType(4);
            values6.setFieldUuid("field026");
            taskAdd2Tasks.addFieldValuesItem(values6);

            //进度
            TasksAdd2FieldValues values7 = new TasksAdd2FieldValues();
            values7.setValue("0");
            values7.setType(4);
            values7.setFieldUuid("field033");
            taskAdd2Tasks.addFieldValuesItem(values7);

            //查询关注人
            if (StringUtils.isNotEmpty(param.getWatchers())) {
                List<String> watcherUuids;
                if (param.getWatchers().contains(",")) {
                    watcherUuids = Arrays.stream(param.getWatchers().split(",")).map(item -> nameUserMap.get(item).getUuid()).collect(Collectors.toList());
                } else {
                    watcherUuids = Lists.newArrayList(nameUserMap.get(param.getWatchers()).getUuid());
                }
                //关注着，输入 测试
                TasksAdd2FieldValues values8 = new TasksAdd2FieldValues();
                values8.setValue(watcherUuids);
                values8.setType(13);
                values8.setFieldUuid("field008");
                taskAdd2Tasks.addFieldValuesItem(values8);
            }

            tasks.add(taskAdd2Tasks);
        });

        AddTaskResponse addTaskResponse = onesApiClient.addTask(taskAdd2TaskRequest);
        log.info("导入任务响应：{}", addTaskResponse);
        return true;
    }


    public List<Cases> listTestCases(String teamUuid, String planUuid) {
        TestCaseResponse testCaseResponse = onesApiClient.testCases(teamUuid, planUuid);
        return testCaseResponse.getCases();
    }

    public List<Cases> listTestCasesByLibrary(String teamUuid, String libraryUUID) {
        TestCaseResponse testCaseResponse = onesApiClient.testLibraryCases(teamUuid, libraryUUID);
        return testCaseResponse.getCases();
    }

    public List<Plans> listPlans(String teamUuid) {
        TestPlanResponse planResponse = onesApiClient.plans(teamUuid);
        return planResponse.getPlans();
    }

    public List<TestCaseInfo> exportTestCases(String teamUuid) {
        return listPlans(teamUuid).stream().map(plan -> buildTestCaseInfo(plan, listTestCases(teamUuid, plan.getUuid()))).flatMap(Collection::stream).collect(Collectors.toList());
    }

    public List<TestCaseInfo> exportAllLibraryTestCases(String teamUuid) {
        return onesApiClient.libraries(teamUuid).getLibraries().stream().map(libraries -> buildTestCaseInfo(listTestCasesByLibrary(teamUuid, libraries.getUuid()))).flatMap(Collection::stream).collect(Collectors.toList());
    }


    private List<TestCaseInfo> buildTestCaseInfo(Plans plan, List<Cases> listTestCases) {
        return listTestCases.stream().map(item -> {
            TestCaseInfo testCaseInfo = new TestCaseInfo();
            testCaseInfo.setName(item.getName());
            testCaseInfo.setPlanName(plan.getName());
            testCaseInfo.setPlanOwner(getUserName(plan.getOwner()));
            testCaseInfo.setProjectName(projectMap.get(plan.getRelatedProjectUuid()));
            testCaseInfo.setNote(item.getNote());
            testCaseInfo.setPlan_uuid(plan.getUuid());
            testCaseInfo.setCreate_time(DateUtil.printDateTime(item.getCreateTime()));
            testCaseInfo.setLibrary_uuid(getLibraryName(item.getModuleUuid()));
            testCaseInfo.setLibraryName(item.getLibraryUuid());
            testCaseInfo.setModule_uuid(item.getModuleUuid());
            testCaseInfo.setModuleName(getModuleName(item.getModuleUuid()));
            testCaseInfo.setPriority(testCasePriorityMap.get(item.getPriority()));
            testCaseInfo.setType(testCaseTypeMap.get(item.getType()));
            testCaseInfo.setUuid(item.getUuid());
            testCaseInfo.setResult(item.getResult());
            testCaseInfo.setNumber(item.getNumber());
            testCaseInfo.setExecutor(getUserName(item.getExecutor()));
            testCaseInfo.setAssign(getUserName(item.getAssign()));
            testCaseInfo.setDesc(item.getDesc());
            return testCaseInfo;
        }).collect(Collectors.toList());
    }

    private List<TestCaseInfo> buildTestCaseInfo(List<Cases> listTestCases) {
        return listTestCases.stream().map(item -> {
            TestCaseInfo testCaseInfo = new TestCaseInfo();
            testCaseInfo.setName(item.getName());
            testCaseInfo.setPlanName("");
            testCaseInfo.setPlanOwner("");
            testCaseInfo.setProjectName("");
            testCaseInfo.setNote(item.getNote());
            testCaseInfo.setPlan_uuid("");
            testCaseInfo.setCreate_time(DateFormatUtils.format(item.getCreateTime(), "yyyy-MM-dd"));
            testCaseInfo.setLibrary_uuid(getLibraryName(item.getModuleUuid()));
            testCaseInfo.setLibraryName(item.getLibraryUuid());
            testCaseInfo.setModule_uuid(item.getModuleUuid());
            testCaseInfo.setModuleName(getModuleName(item.getModuleUuid()));
            testCaseInfo.setPriority(testCasePriorityMap.get(item.getPriority()));
            testCaseInfo.setType(testCaseTypeMap.get(item.getType()));
            testCaseInfo.setUuid(item.getUuid());
            testCaseInfo.setResult(item.getResult());
            testCaseInfo.setNumber(item.getNumber());
            testCaseInfo.setExecutor(getUserName(item.getExecutor()));
            testCaseInfo.setAssign(getUserName(item.getAssign()));
            testCaseInfo.setDesc(item.getDesc());
            return testCaseInfo;
        }).collect(Collectors.toList());
    }

    private String getLibraryName(String moduleUuid) {
        Modules modules = modulesMap.get(moduleUuid);
        return modules == null ? "" : modules.getLibraryName();
    }

    private String getModuleName(String moduleUuid) {
        Modules modules = modulesMap.get(moduleUuid);
        return modules == null ? "" : modules.getName();
    }

    private String getUserName(String owner) {
        User user = uuidUserMap.get(owner);
        return user == null ? "" : user.getName();
    }

    @Value("${webhook.corpAppId:1000115}")
    private String corpAppId;

    public void webhook(EventMsg eventMsg) {
        List<? extends Message> messages = eventMsg.getMessages();
        if (CollectionUtils.isNotEmpty(messages)) {
            messages.forEach(message -> {
                if (CollectionUtils.isNotEmpty(message.getTo_users())) {
                    MessageBodyWeCom notifyParam = new MessageBodyWeCom();
                    if (CollectionUtils.isNotEmpty(message.getTo_users())) {
                        List<String> mails = message.getTo_users().stream()
                                .map(item -> {
                                    User user = uuidUserMap.get(item.getUuid());
                                    if (user == null) {
                                        refreshUserCache();
                                        user = uuidUserMap.get(item.getUuid());
                                    }
                                    if (user != null) {
                                        return user.getEmail();
                                    }
                                    log.error("没有查询到用户={}:{}", item.getUuid(), item.getName());
                                    return null;
                                })
                                .filter(Objects::nonNull)
                                .collect(Collectors.toList());

                        //过滤黑名单
                        mails = mails.stream().filter(item -> hookBlackEmails.isEmpty() || !hookBlackEmails.contains(item)).collect(Collectors.toList());

                        //消息接收人 开启白名单后只给白名单的人发送。
                        List<MessageUserDTO> messageUserDTOS;
                        if (hookWhiteEmails.isEmpty()) {
                            messageUserDTOS = mails.stream().map(item -> MessageUserDTO.builder().email(item).build()).collect(Collectors.toList());
                        }else {
                            messageUserDTOS = hookWhiteEmails.stream().map(item -> MessageUserDTO.builder().email(item).build()).collect(Collectors.toList());
                        }
                        notifyParam.setTargetUser(messageUserDTOS);

                    }
                    if (CollectionUtils.isNotEmpty(notifyParam.getTargetUser())) {
                        notifyParam.setAgentId(corpAppId);
                        notifyParam.setMessage(MessageContentWeCom.builder()
                                        .title(message.getTitle())
                                        .url(message.getUrl())
                                        .description(message.getDesc())
                                .build());
                        notifyParam.setMessageType(WeComMessageType.TEXT_CARD);
                        log.info("发送企微消息：{}", notifyParam);
                        AtourResponse<String> atourResponse = apiIMessageChannelWeComService.sendWeComMessage(RPCContext.createRequest(notifyParam));
                        log.info("发送企微消息结果：{}", atourResponse);
                    }
                }
            });
        }
    }

    /**
     * 登记工时
     * @param addHourDTO
     */
    public void addHour(AddHourDTO addHourDTO) {
//
//        /project/api/project/team/KMgnuFDd/task/35065/info
//        通过序号查询工作项详情
//        nameUserMap 查询用户UUID
//        开始时间 时间戳 秒
//        工时 * 100000
//        描述 自定义描述 + 【本次由apifox快速登记工时完成】
    }

    public JSONObject detailByOnesID(String onesId) {
        JSONObject jsonObject = onesApiClient.detailByOnesId(onesId);
        JSONObject newJsonObject = JSON.parseObject(jsonObject.toJSONString());
        if (jsonObject != null) {
            for (Entry<String, Object> entry : jsonObject.entrySet()) {
                if (entry.getKey().startsWith("_")) {
                    String name = fieldNameMap.get(entry.getKey().replaceFirst("_", ""));
                    if (StringUtils.isEmpty(name)) {
                        log.info("name is null {}", entry.getKey());
                    }else {
                        newJsonObject.put(name, entry.getValue());
                        newJsonObject.remove(entry.getKey());
                    }
                }
            }
        }
        String planStartDate = newJsonObject.getString("planStartDate");
        if (StringUtils.isNotEmpty(planStartDate)) {
            DateTime dateTime = cn.hutool.core.date.DateUtil.parseDate(planStartDate);
            newJsonObject.put("开始时间", dateTime.getTime());
        }
        String planEndDate = newJsonObject.getString("planEndDate");
        if (StringUtils.isNotEmpty(planEndDate)) {
            DateTime dateTime = cn.hutool.core.date.DateUtil.parseDate(planEndDate);
            newJsonObject.put("结束时间", dateTime.getTime());
        }
        newJsonObject.put("url", "https://ones.corp.yaduo.com/project/#/team/KMgnuFDd/task/" + newJsonObject.getString("path"));
        return newJsonObject;
    }
}
