package com.yaduo.devopstools.ones.service;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.read.listener.ReadListener;
import com.alibaba.excel.util.ListUtils;
import com.yaduo.devopstools.ones.model.ImportParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022-04-18 00:54
 */
@Slf4j
public class ImportTaskExcelReadListener implements ReadListener<ImportParam> {
    private OnesApiService onesApiService;

    private List<ImportParam> batchList = new ArrayList<>(20);

    public ImportTaskExcelReadListener(OnesApiService onesApiService) {
        this.onesApiService = onesApiService;
    }

    @Override
    public void invoke(ImportParam data, AnalysisContext context) {
        batchList.add(data);
        if (batchList.size() > 20) {
            save();
            batchList = ListUtils.newArrayListWithExpectedSize(20);
        }
    }

    private void save() {
        if (CollectionUtils.isNotEmpty(batchList)) {
            boolean success = onesApiService.importTask(batchList);
            log.info("导入结果：" + success);
        }
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        save();
    }
}
