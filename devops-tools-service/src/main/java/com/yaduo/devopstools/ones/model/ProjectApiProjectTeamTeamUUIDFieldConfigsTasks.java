//package com.yaduo.devopstools.ones.model;
//
//import com.fasterxml.jackson.annotation.JsonProperty;
//
//import javax.validation.Valid;
//import javax.validation.constraints.NotNull;
//import java.util.ArrayList;
//import java.util.List;
//import java.util.Objects;
//
///**
// * ProjectApiProjectTeamTeamUUIDFieldConfigsTasks
// */
//@javax.annotation.Generated(value = "org.openapitools.codegen.languages.SpringCodegen", date = "2022-04-17T00:46:55.176358+08:00[Asia/Shanghai]")
//
//public class ProjectApiProjectTeamTeamUUIDFieldConfigsTasks   {
//  @JsonProperty("uuid")
//  private String uuid;
//
//  @JsonProperty("owner")
//  private String owner;
//
//  @JsonProperty("assign")
//  private String assign;
//
//  @JsonProperty("summary")
//  private String summary;
//
//  @JsonProperty("parent_uuid")
//  private String parentUuid;
//
//  @JsonProperty("project_uuid")
//  private String projectUuid;
//
//  @JsonProperty("issue_type_uuid")
//  private String issueTypeUuid;
//
//  @JsonProperty("desc")
//  private String desc;
//
//  @JsonProperty("priority")
//  private String priority;
//
//  @JsonProperty("field_values")
//  @Valid
//  private List<String> fieldValues = null;
//
//  public ProjectApiProjectTeamTeamUUIDFieldConfigsTasks uuid(String uuid) {
//    this.uuid = uuid;
//    return this;
//  }
//
//  /**
//   * 由创建者uuid+随机8位字符组成
//   * @return uuid
//  */
////  @ApiModelProperty(required = true, value = "由创建者uuid+随机8位字符组成")
//  @NotNull
//
//
//  public String getUuid() {
//    return uuid;
//  }
//
//  public void setUuid(String uuid) {
//    this.uuid = uuid;
//  }
//
//  public ProjectApiProjectTeamTeamUUIDFieldConfigsTasks owner(String owner) {
//    this.owner = owner;
//    return this;
//  }
//
//  /**
//   * Get owner
//   * @return owner
//  */
////  @ApiModelProperty(required = true, value = "")
//  @NotNull
//
//
//  public String getOwner() {
//    return owner;
//  }
//
//  public void setOwner(String owner) {
//    this.owner = owner;
//  }
//
//  public ProjectApiProjectTeamTeamUUIDFieldConfigsTasks assign(String assign) {
//    this.assign = assign;
//    return this;
//  }
//
//  /**
//   * 工作项负责人的用户id
//   * @return assign
//  */
////  @ApiModelProperty(required = true, value = "工作项负责人的用户id")
//  @NotNull
//
//
//  public String getAssign() {
//    return assign;
//  }
//
//  public void setAssign(String assign) {
//    this.assign = assign;
//  }
//
//  public ProjectApiProjectTeamTeamUUIDFieldConfigsTasks summary(String summary) {
//    this.summary = summary;
//    return this;
//  }
//
//  /**
//   * 工作项名
//   * @return summary
//  */
////  @ApiModelProperty(required = true, value = "工作项名")
//  @NotNull
//
//
//  public String getSummary() {
//    return summary;
//  }
//
//  public void setSummary(String summary) {
//    this.summary = summary;
//  }
//
//  public ProjectApiProjectTeamTeamUUIDFieldConfigsTasks parentUuid(String parentUuid) {
//    this.parentUuid = parentUuid;
//    return this;
//  }
//
//  /**
//   * 父工作项id
//   * @return parentUuid
//  */
////  @ApiModelProperty(value = "父工作项id")
//
//
//  public String getParentUuid() {
//    return parentUuid;
//  }
//
//  public void setParentUuid(String parentUuid) {
//    this.parentUuid = parentUuid;
//  }
//
//  public ProjectApiProjectTeamTeamUUIDFieldConfigsTasks projectUuid(String projectUuid) {
//    this.projectUuid = projectUuid;
//    return this;
//  }
//
//  /**
//   * 项目id
//   * @return projectUuid
//  */
////  @ApiModelProperty(required = true, value = "项目id")
//  @NotNull
//
//
//  public String getProjectUuid() {
//    return projectUuid;
//  }
//
//  public void setProjectUuid(String projectUuid) {
//    this.projectUuid = projectUuid;
//  }
//
//  public ProjectApiProjectTeamTeamUUIDFieldConfigsTasks issueTypeUuid(String issueTypeUuid) {
//    this.issueTypeUuid = issueTypeUuid;
//    return this;
//  }
//
//  /**
//   * 工作项类型id
//   * @return issueTypeUuid
//  */
////  @ApiModelProperty(required = true, value = "工作项类型id")
//  @NotNull
//
//
//  public String getIssueTypeUuid() {
//    return issueTypeUuid;
//  }
//
//  public void setIssueTypeUuid(String issueTypeUuid) {
//    this.issueTypeUuid = issueTypeUuid;
//  }
//
//  public ProjectApiProjectTeamTeamUUIDFieldConfigsTasks desc(String desc) {
//    this.desc = desc;
//    return this;
//  }
//
//  /**
//   * 工作项描述
//   * @return desc
//  */
////  @ApiModelProperty(value = "工作项描述")
//
//
//  public String getDesc() {
//    return desc;
//  }
//
//  public void setDesc(String desc) {
//    this.desc = desc;
//  }
//
//  public ProjectApiProjectTeamTeamUUIDFieldConfigsTasks priority(String priority) {
//    this.priority = priority;
//    return this;
//  }
//
//  /**
//   * 工作项优先级 [uuid]
//   * @return priority
//  */
////  @ApiModelProperty(required = true, value = "工作项优先级 [uuid]")
//  @NotNull
//
//
//  public String getPriority() {
//    return priority;
//  }
//
//  public void setPriority(String priority) {
//    this.priority = priority;
//  }
//
//  public ProjectApiProjectTeamTeamUUIDFieldConfigsTasks fieldValues(List<String> fieldValues) {
//    this.fieldValues = fieldValues;
//    return this;
//  }
//
//  public ProjectApiProjectTeamTeamUUIDFieldConfigsTasks addFieldValuesItem(String fieldValuesItem) {
//    if (this.fieldValues == null) {
//      this.fieldValues = new ArrayList<>();
//    }
//    this.fieldValues.add(fieldValuesItem);
//    return this;
//  }
//
//  /**
//   * Get fieldValues
//   * @return fieldValues
//  */
////  @ApiModelProperty(value = "")
//
//
//  public List<String> getFieldValues() {
//    return fieldValues;
//  }
//
//  public void setFieldValues(List<String> fieldValues) {
//    this.fieldValues = fieldValues;
//  }
//
//
//  @Override
//  public boolean equals(Object o) {
//    if (this == o) {
//      return true;
//    }
//    if (o == null || getClass() != o.getClass()) {
//      return false;
//    }
//    ProjectApiProjectTeamTeamUUIDFieldConfigsTasks projectApiProjectTeamTeamUUIDFieldConfigsTasks = (ProjectApiProjectTeamTeamUUIDFieldConfigsTasks) o;
//    return Objects.equals(this.uuid, projectApiProjectTeamTeamUUIDFieldConfigsTasks.uuid) &&
//        Objects.equals(this.owner, projectApiProjectTeamTeamUUIDFieldConfigsTasks.owner) &&
//        Objects.equals(this.assign, projectApiProjectTeamTeamUUIDFieldConfigsTasks.assign) &&
//        Objects.equals(this.summary, projectApiProjectTeamTeamUUIDFieldConfigsTasks.summary) &&
//        Objects.equals(this.parentUuid, projectApiProjectTeamTeamUUIDFieldConfigsTasks.parentUuid) &&
//        Objects.equals(this.projectUuid, projectApiProjectTeamTeamUUIDFieldConfigsTasks.projectUuid) &&
//        Objects.equals(this.issueTypeUuid, projectApiProjectTeamTeamUUIDFieldConfigsTasks.issueTypeUuid) &&
//        Objects.equals(this.desc, projectApiProjectTeamTeamUUIDFieldConfigsTasks.desc) &&
//        Objects.equals(this.priority, projectApiProjectTeamTeamUUIDFieldConfigsTasks.priority) &&
//        Objects.equals(this.fieldValues, projectApiProjectTeamTeamUUIDFieldConfigsTasks.fieldValues);
//  }
//
//  @Override
//  public int hashCode() {
//    return Objects.hash(uuid, owner, assign, summary, parentUuid, projectUuid, issueTypeUuid, desc, priority, fieldValues);
//  }
//
//  @Override
//  public String toString() {
//    StringBuilder sb = new StringBuilder();
//    sb.append("class ProjectApiProjectTeamTeamUUIDFieldConfigsTasks {\n");
//    
//    sb.append("    uuid: ").append(toIndentedString(uuid)).append("\n");
//    sb.append("    owner: ").append(toIndentedString(owner)).append("\n");
//    sb.append("    assign: ").append(toIndentedString(assign)).append("\n");
//    sb.append("    summary: ").append(toIndentedString(summary)).append("\n");
//    sb.append("    parentUuid: ").append(toIndentedString(parentUuid)).append("\n");
//    sb.append("    projectUuid: ").append(toIndentedString(projectUuid)).append("\n");
//    sb.append("    issueTypeUuid: ").append(toIndentedString(issueTypeUuid)).append("\n");
//    sb.append("    desc: ").append(toIndentedString(desc)).append("\n");
//    sb.append("    priority: ").append(toIndentedString(priority)).append("\n");
//    sb.append("    fieldValues: ").append(toIndentedString(fieldValues)).append("\n");
//    sb.append("}");
//    return sb.toString();
//  }
//
//  /**
//   * Convert the given object to string with each line indented by 4 spaces
//   * (except the first line).
//   */
//  private String toIndentedString(Object o) {
//    if (o == null) {
//      return "null";
//    }
//    return o.toString().replace("\n", "\n    ");
//  }
//}
//
