package com.yaduo.devopstools.ones.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 *
 */
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.SpringCodegen", date = "2022-04-17T00:46:55.176358+08:00[Asia/Shanghai]")

public class TokenInfo {
    @JsonProperty("user")
    private User user;

    @JsonProperty("teams")
    @Valid
    private List<Teams> teams = new ArrayList<>();

    @JsonProperty("org")
    private Org org;

    public TokenInfo user(User user) {
        this.user = user;
        return this;
    }

    /**
     * Get user
     *
     * @return user
     */
  //  @ApiModelProperty(required = true, value = "")
    @NotNull

    @Valid

    public User getUser() {
        return user;
    }

    public void setUser(User user) {
        this.user = user;
    }

    public TokenInfo teams(List<Teams> teams) {
        this.teams = teams;
        return this;
    }

    public TokenInfo addTeamsItem(Teams teamsItem) {
        this.teams.add(teamsItem);
        return this;
    }

    /**
     * Get teams
     *
     * @return teams
     */
  //  @ApiModelProperty(required = true, value = "")
    @NotNull

    @Valid

    public List<Teams> getTeams() {
        return teams;
    }

    public void setTeams(List<Teams> teams) {
        this.teams = teams;
    }

    public TokenInfo org(Org org) {
        this.org = org;
        return this;
    }

    /**
     * Get org
     *
     * @return org
     */
  //  @ApiModelProperty(required = true, value = "")
    @NotNull

    @Valid

    public Org getOrg() {
        return org;
    }

    public void setOrg(Org org) {
        this.org = org;
    }


    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class  {\n");

        sb.append("    user: ").append(toIndentedString(user)).append("\n");
        sb.append("    teams: ").append(toIndentedString(teams)).append("\n");
        sb.append("    org: ").append(toIndentedString(org)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }

    /**
     * User
     */
    @javax.annotation.Generated(value = "org.openapitools.codegen.languages.SpringCodegen", date = "2022-04-17T00:46:55.176358+08:00[Asia/Shanghai]")

    public static class User {
        @JsonProperty("uuid")
        private String uuid;

        @JsonProperty("email")
        private String email;

        @JsonProperty("name")
        private String name;

        @JsonProperty("name_pinyin")
        private String namePinyin;

        @JsonProperty("title")
        private String title;

        @JsonProperty("avatar")
        private String avatar;

        @JsonProperty("phone")
        private String phone;

        @JsonProperty("create_time")
        @JSONField(format="unixtime")

        private Date createTime;

        @JsonProperty("status")
        private Integer status;

        @JsonProperty("channel")
        private String channel;

        @JsonProperty("token")
        private String token;

        @JsonProperty("license_types")
        @Valid
        private List<Integer> licenseTypes = new ArrayList<>();

        @JsonProperty("imported_jira_user")
        private Boolean importedJiraUser;

        @JsonProperty("is_init_password")
        private Boolean isInitPassword;

        public User uuid(String uuid) {
            this.uuid = uuid;
            return this;
        }

        /**
         * Get uuid
         *
         * @return uuid
         */
      //  @ApiModelProperty(required = true, value = "")
        @NotNull


        public String getUuid() {
            return uuid;
        }

        public void setUuid(String uuid) {
            this.uuid = uuid;
        }

        public User email(String email) {
            this.email = email;
            return this;
        }

        /**
         * Get email
         *
         * @return email
         */
      //  @ApiModelProperty(required = true, value = "")
        @NotNull


        public String getEmail() {
            return email;
        }

        public void setEmail(String email) {
            this.email = email;
        }

        public User name(String name) {
            this.name = name;
            return this;
        }

        /**
         * Get name
         *
         * @return name
         */
      //  @ApiModelProperty(required = true, value = "")
        @NotNull


        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public User namePinyin(String namePinyin) {
            this.namePinyin = namePinyin;
            return this;
        }

        /**
         * Get namePinyin
         *
         * @return namePinyin
         */
      //  @ApiModelProperty(required = true, value = "")
        @NotNull


        public String getNamePinyin() {
            return namePinyin;
        }

        public void setNamePinyin(String namePinyin) {
            this.namePinyin = namePinyin;
        }

        public User title(String title) {
            this.title = title;
            return this;
        }

        /**
         * Get title
         *
         * @return title
         */
      //  @ApiModelProperty(required = true, value = "")
        @NotNull


        public String getTitle() {
            return title;
        }

        public void setTitle(String title) {
            this.title = title;
        }

        public User avatar(String avatar) {
            this.avatar = avatar;
            return this;
        }

        /**
         * Get avatar
         *
         * @return avatar
         */
      //  @ApiModelProperty(required = true, value = "")
        @NotNull


        public String getAvatar() {
            return avatar;
        }

        public void setAvatar(String avatar) {
            this.avatar = avatar;
        }

        public User phone(String phone) {
            this.phone = phone;
            return this;
        }

        /**
         * Get phone
         *
         * @return phone
         */
      //  @ApiModelProperty(required = true, value = "")
        @NotNull


        public String getPhone() {
            return phone;
        }

        public void setPhone(String phone) {
            this.phone = phone;
        }

        public User createTime(Date createTime) {
            this.createTime = createTime;
            return this;
        }

        /**
         * Get createTime
         *
         * @return createTime
         */
      //  @ApiModelProperty(required = true, value = "")
        @NotNull


        public Date getCreateTime() {
            return createTime;
        }

        public void setCreateTime(Date createTime) {
            this.createTime = createTime;
        }

        public User status(Integer status) {
            this.status = status;
            return this;
        }

        /**
         * Get status
         *
         * @return status
         */
      //  @ApiModelProperty(required = true, value = "")
        @NotNull


        public Integer getStatus() {
            return status;
        }

        public void setStatus(Integer status) {
            this.status = status;
        }

        public User channel(String channel) {
            this.channel = channel;
            return this;
        }

        /**
         * Get channel
         *
         * @return channel
         */
      //  @ApiModelProperty(required = true, value = "")
        @NotNull


        public String getChannel() {
            return channel;
        }

        public void setChannel(String channel) {
            this.channel = channel;
        }

        public User token(String token) {
            this.token = token;
            return this;
        }

        /**
         * Get token
         *
         * @return token
         */
      //  @ApiModelProperty(required = true, value = "")
        @NotNull


        public String getToken() {
            return token;
        }

        public void setToken(String token) {
            this.token = token;
        }

        public User licenseTypes(List<Integer> licenseTypes) {
            this.licenseTypes = licenseTypes;
            return this;
        }

        public User addLicenseTypesItem(Integer licenseTypesItem) {
            this.licenseTypes.add(licenseTypesItem);
            return this;
        }

        /**
         * Get licenseTypes
         *
         * @return licenseTypes
         */
      //  @ApiModelProperty(required = true, value = "")
        @NotNull


        public List<Integer> getLicenseTypes() {
            return licenseTypes;
        }

        public void setLicenseTypes(List<Integer> licenseTypes) {
            this.licenseTypes = licenseTypes;
        }

        public User importedJiraUser(Boolean importedJiraUser) {
            this.importedJiraUser = importedJiraUser;
            return this;
        }

        /**
         * Get importedJiraUser
         *
         * @return importedJiraUser
         */
      //  @ApiModelProperty(required = true, value = "")
        @NotNull


        public Boolean getImportedJiraUser() {
            return importedJiraUser;
        }

        public void setImportedJiraUser(Boolean importedJiraUser) {
            this.importedJiraUser = importedJiraUser;
        }

        public User isInitPassword(Boolean isInitPassword) {
            this.isInitPassword = isInitPassword;
            return this;
        }

        /**
         * Get isInitPassword
         *
         * @return isInitPassword
         */
      //  @ApiModelProperty(required = true, value = "")
        @NotNull


        public Boolean getIsInitPassword() {
            return isInitPassword;
        }

        public void setIsInitPassword(Boolean isInitPassword) {
            this.isInitPassword = isInitPassword;
        }


        @Override
        public boolean equals(Object o) {
            if (this == o) {
                return true;
            }
            if (o == null || getClass() != o.getClass()) {
                return false;
            }
            User user = (User) o;
            return Objects.equals(this.uuid, user.uuid) && Objects.equals(this.email, user.email) && Objects.equals(this.name, user.name) && Objects.equals(this.namePinyin, user.namePinyin) && Objects.equals(this.title, user.title) && Objects.equals(this.avatar, user.avatar) && Objects.equals(this.phone, user.phone) && Objects.equals(this.createTime, user.createTime) && Objects.equals(this.status, user.status) && Objects.equals(this.channel, user.channel) && Objects.equals(this.token, user.token) && Objects.equals(this.licenseTypes, user.licenseTypes) && Objects.equals(this.importedJiraUser, user.importedJiraUser) && Objects.equals(this.isInitPassword, user.isInitPassword);
        }

        @Override
        public int hashCode() {
            return Objects.hash(uuid, email, name, namePinyin, title, avatar, phone, createTime, status, channel, token, licenseTypes, importedJiraUser, isInitPassword);
        }

        @Override
        public String toString() {
            StringBuilder sb = new StringBuilder();
            sb.append("class User {\n");

            sb.append("    uuid: ").append(toIndentedString(uuid)).append("\n");
            sb.append("    email: ").append(toIndentedString(email)).append("\n");
            sb.append("    name: ").append(toIndentedString(name)).append("\n");
            sb.append("    namePinyin: ").append(toIndentedString(namePinyin)).append("\n");
            sb.append("    title: ").append(toIndentedString(title)).append("\n");
            sb.append("    avatar: ").append(toIndentedString(avatar)).append("\n");
            sb.append("    phone: ").append(toIndentedString(phone)).append("\n");
            sb.append("    createTime: ").append(toIndentedString(createTime)).append("\n");
            sb.append("    status: ").append(toIndentedString(status)).append("\n");
            sb.append("    channel: ").append(toIndentedString(channel)).append("\n");
            sb.append("    token: ").append(toIndentedString(token)).append("\n");
            sb.append("    licenseTypes: ").append(toIndentedString(licenseTypes)).append("\n");
            sb.append("    importedJiraUser: ").append(toIndentedString(importedJiraUser)).append("\n");
            sb.append("    isInitPassword: ").append(toIndentedString(isInitPassword)).append("\n");
            sb.append("}");
            return sb.toString();
        }

        /**
         * Convert the given object to string with each line indented by 4 spaces
         * (except the first line).
         */
        private String toIndentedString(Object o) {
            if (o == null) {
                return "null";
            }
            return o.toString().replace("\n", "\n    ");
        }
    }

    /**
     * Teams
     */
    @javax.annotation.Generated(value = "org.openapitools.codegen.languages.SpringCodegen", date = "2022-04-17T00:46:55.176358+08:00[Asia/Shanghai]")

    public static class Teams {
        @JsonProperty("uuid")
        private String uuid;

        @JsonProperty("status")
        private Integer status;

        @JsonProperty("name")
        private String name;

        @JsonProperty("owner")
        private String owner;

        @JsonProperty("logo")
        private String logo;

        @JsonProperty("cover_url")
        private String coverUrl;

        @JsonProperty("domain")
        private String domain;

        @JsonProperty("create_time")
        @JSONField(format="unixtime")

        private Date createTime;

        @JsonProperty("expire_time")
        private Date expireTime;

        @JsonProperty("type")
        private String type;

        @JsonProperty("config")
        private Config config;

        @JsonProperty("member_count")
        private Integer memberCount;

        @JsonProperty("pending_member_count")
        private Integer pendingMemberCount;

        @JsonProperty("disable_member_count")
        private Integer disableMemberCount;

        @JsonProperty("org_uuid")
        private String orgUuid;

        @JsonProperty("workdays")
        @Valid
        private List<String> workdays = null;

        @JsonProperty("workhours")
        private Integer workhours;

        @JsonProperty("workhours_unit")
        private String workhoursUnit;

        public Teams uuid(String uuid) {
            this.uuid = uuid;
            return this;
        }

        /**
         * Get uuid
         *
         * @return uuid
         */
      //  @ApiModelProperty(value = "")


        public String getUuid() {
            return uuid;
        }

        public void setUuid(String uuid) {
            this.uuid = uuid;
        }

        public Teams status(Integer status) {
            this.status = status;
            return this;
        }

        /**
         * Get status
         *
         * @return status
         */
      //  @ApiModelProperty(value = "")


        public Integer getStatus() {
            return status;
        }

        public void setStatus(Integer status) {
            this.status = status;
        }

        public Teams name(String name) {
            this.name = name;
            return this;
        }

        /**
         * Get name
         *
         * @return name
         */
      //  @ApiModelProperty(value = "")


        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public Teams owner(String owner) {
            this.owner = owner;
            return this;
        }

        /**
         * Get owner
         *
         * @return owner
         */
      //  @ApiModelProperty(value = "")


        public String getOwner() {
            return owner;
        }

        public void setOwner(String owner) {
            this.owner = owner;
        }

        public Teams logo(String logo) {
            this.logo = logo;
            return this;
        }

        /**
         * Get logo
         *
         * @return logo
         */
      //  @ApiModelProperty(value = "")


        public String getLogo() {
            return logo;
        }

        public void setLogo(String logo) {
            this.logo = logo;
        }

        public Teams coverUrl(String coverUrl) {
            this.coverUrl = coverUrl;
            return this;
        }

        /**
         * Get coverUrl
         *
         * @return coverUrl
         */
      //  @ApiModelProperty(value = "")


        public String getCoverUrl() {
            return coverUrl;
        }

        public void setCoverUrl(String coverUrl) {
            this.coverUrl = coverUrl;
        }

        public Teams domain(String domain) {
            this.domain = domain;
            return this;
        }

        /**
         * Get domain
         *
         * @return domain
         */
      //  @ApiModelProperty(value = "")


        public String getDomain() {
            return domain;
        }

        public void setDomain(String domain) {
            this.domain = domain;
        }

        public Teams createTime(Date createTime) {
            this.createTime = createTime;
            return this;
        }

        /**
         * Get createTime
         *
         * @return createTime
         */
      //  @ApiModelProperty(value = "")


        public Date getCreateTime() {
            return createTime;
        }

        public void setCreateTime(Date createTime) {
            this.createTime = createTime;
        }

        public Teams expireTime(Date expireTime) {
            this.expireTime = expireTime;
            return this;
        }

        /**
         * Get expireTime
         *
         * @return expireTime
         */
      //  @ApiModelProperty(value = "")


        public Date getExpireTime() {
            return expireTime;
        }

        public void setExpireTime(Date expireTime) {
            this.expireTime = expireTime;
        }

        public Teams type(String type) {
            this.type = type;
            return this;
        }

        /**
         * Get type
         *
         * @return type
         */
      //  @ApiModelProperty(value = "")


        public String getType() {
            return type;
        }

        public void setType(String type) {
            this.type = type;
        }

        public Teams config(Config config) {
            this.config = config;
            return this;
        }

        /**
         * Get config
         *
         * @return config
         */
      //  @ApiModelProperty(value = "")

        @Valid

        public Config getConfig() {
            return config;
        }

        public void setConfig(Config config) {
            this.config = config;
        }

        public Teams memberCount(Integer memberCount) {
            this.memberCount = memberCount;
            return this;
        }

        /**
         * Get memberCount
         *
         * @return memberCount
         */
      //  @ApiModelProperty(value = "")


        public Integer getMemberCount() {
            return memberCount;
        }

        public void setMemberCount(Integer memberCount) {
            this.memberCount = memberCount;
        }

        public Teams pendingMemberCount(Integer pendingMemberCount) {
            this.pendingMemberCount = pendingMemberCount;
            return this;
        }

        /**
         * Get pendingMemberCount
         *
         * @return pendingMemberCount
         */
      //  @ApiModelProperty(value = "")


        public Integer getPendingMemberCount() {
            return pendingMemberCount;
        }

        public void setPendingMemberCount(Integer pendingMemberCount) {
            this.pendingMemberCount = pendingMemberCount;
        }

        public Teams disableMemberCount(Integer disableMemberCount) {
            this.disableMemberCount = disableMemberCount;
            return this;
        }

        /**
         * Get disableMemberCount
         *
         * @return disableMemberCount
         */
      //  @ApiModelProperty(value = "")


        public Integer getDisableMemberCount() {
            return disableMemberCount;
        }

        public void setDisableMemberCount(Integer disableMemberCount) {
            this.disableMemberCount = disableMemberCount;
        }

        public Teams orgUuid(String orgUuid) {
            this.orgUuid = orgUuid;
            return this;
        }

        /**
         * Get orgUuid
         *
         * @return orgUuid
         */
      //  @ApiModelProperty(value = "")


        public String getOrgUuid() {
            return orgUuid;
        }

        public void setOrgUuid(String orgUuid) {
            this.orgUuid = orgUuid;
        }

        public Teams workdays(List<String> workdays) {
            this.workdays = workdays;
            return this;
        }

        public Teams addWorkdaysItem(String workdaysItem) {
            if (this.workdays == null) {
                this.workdays = new ArrayList<>();
            }
            this.workdays.add(workdaysItem);
            return this;
        }

        /**
         * Get workdays
         *
         * @return workdays
         */
      //  @ApiModelProperty(value = "")


        public List<String> getWorkdays() {
            return workdays;
        }

        public void setWorkdays(List<String> workdays) {
            this.workdays = workdays;
        }

        public Teams workhours(Integer workhours) {
            this.workhours = workhours;
            return this;
        }

        /**
         * Get workhours
         *
         * @return workhours
         */
      //  @ApiModelProperty(value = "")


        public Integer getWorkhours() {
            return workhours;
        }

        public void setWorkhours(Integer workhours) {
            this.workhours = workhours;
        }

        public Teams workhoursUnit(String workhoursUnit) {
            this.workhoursUnit = workhoursUnit;
            return this;
        }

        /**
         * Get workhoursUnit
         *
         * @return workhoursUnit
         */
      //  @ApiModelProperty(value = "")


        public String getWorkhoursUnit() {
            return workhoursUnit;
        }

        public void setWorkhoursUnit(String workhoursUnit) {
            this.workhoursUnit = workhoursUnit;
        }


        @Override
        public boolean equals(Object o) {
            if (this == o) {
                return true;
            }
            if (o == null || getClass() != o.getClass()) {
                return false;
            }
            Teams teams = (Teams) o;
            return Objects.equals(this.uuid, teams.uuid) && Objects.equals(this.status, teams.status) && Objects.equals(this.name, teams.name) && Objects.equals(this.owner, teams.owner) && Objects.equals(this.logo, teams.logo) && Objects.equals(this.coverUrl, teams.coverUrl) && Objects.equals(this.domain, teams.domain) && Objects.equals(this.createTime, teams.createTime) && Objects.equals(this.expireTime, teams.expireTime) && Objects.equals(this.type, teams.type) && Objects.equals(this.config, teams.config) && Objects.equals(this.memberCount, teams.memberCount) && Objects.equals(this.pendingMemberCount, teams.pendingMemberCount) && Objects.equals(this.disableMemberCount, teams.disableMemberCount) && Objects.equals(this.orgUuid, teams.orgUuid) && Objects.equals(this.workdays, teams.workdays) && Objects.equals(this.workhours, teams.workhours) && Objects.equals(this.workhoursUnit, teams.workhoursUnit);
        }

        @Override
        public int hashCode() {
            return Objects.hash(uuid, status, name, owner, logo, coverUrl, domain, createTime, expireTime, type, config, memberCount, pendingMemberCount, disableMemberCount, orgUuid, workdays, workhours, workhoursUnit);
        }

        @Override
        public String toString() {
            StringBuilder sb = new StringBuilder();
            sb.append("class Teams {\n");

            sb.append("    uuid: ").append(toIndentedString(uuid)).append("\n");
            sb.append("    status: ").append(toIndentedString(status)).append("\n");
            sb.append("    name: ").append(toIndentedString(name)).append("\n");
            sb.append("    owner: ").append(toIndentedString(owner)).append("\n");
            sb.append("    logo: ").append(toIndentedString(logo)).append("\n");
            sb.append("    coverUrl: ").append(toIndentedString(coverUrl)).append("\n");
            sb.append("    domain: ").append(toIndentedString(domain)).append("\n");
            sb.append("    createTime: ").append(toIndentedString(createTime)).append("\n");
            sb.append("    expireTime: ").append(toIndentedString(expireTime)).append("\n");
            sb.append("    type: ").append(toIndentedString(type)).append("\n");
            sb.append("    config: ").append(toIndentedString(config)).append("\n");
            sb.append("    memberCount: ").append(toIndentedString(memberCount)).append("\n");
            sb.append("    pendingMemberCount: ").append(toIndentedString(pendingMemberCount)).append("\n");
            sb.append("    disableMemberCount: ").append(toIndentedString(disableMemberCount)).append("\n");
            sb.append("    orgUuid: ").append(toIndentedString(orgUuid)).append("\n");
            sb.append("    workdays: ").append(toIndentedString(workdays)).append("\n");
            sb.append("    workhours: ").append(toIndentedString(workhours)).append("\n");
            sb.append("    workhoursUnit: ").append(toIndentedString(workhoursUnit)).append("\n");
            sb.append("}");
            return sb.toString();
        }

        /**
         * Convert the given object to string with each line indented by 4 spaces
         * (except the first line).
         */
        private String toIndentedString(Object o) {
            if (o == null) {
                return "null";
            }
            return o.toString().replace("\n", "\n    ");
        }
    }

    /**
     * Org
     */
    @javax.annotation.Generated(value = "org.openapitools.codegen.languages.SpringCodegen", date = "2022-04-17T00:46:55.176358+08:00[Asia/Shanghai]")

    public static class Org {
        @JsonProperty("org_type")
        private Integer orgType;

        @JsonProperty("uuid")
        private String uuid;

        @JsonProperty("name")
        private String name;

        @JsonProperty("style_hash")
        private String styleHash;

        @JsonProperty("favicon")
        private String favicon;

        @JsonProperty("notifications")
        @Valid
        private List<String> notifications = new ArrayList<>();

        @JsonProperty("status")
        private Integer status;

        public Org orgType(Integer orgType) {
            this.orgType = orgType;
            return this;
        }

        /**
         * Get orgType
         *
         * @return orgType
         */
      //  @ApiModelProperty(required = true, value = "")
        @NotNull


        public Integer getOrgType() {
            return orgType;
        }

        public void setOrgType(Integer orgType) {
            this.orgType = orgType;
        }

        public Org uuid(String uuid) {
            this.uuid = uuid;
            return this;
        }

        /**
         * Get uuid
         *
         * @return uuid
         */
      //  @ApiModelProperty(required = true, value = "")
        @NotNull


        public String getUuid() {
            return uuid;
        }

        public void setUuid(String uuid) {
            this.uuid = uuid;
        }

        public Org name(String name) {
            this.name = name;
            return this;
        }

        /**
         * Get name
         *
         * @return name
         */
      //  @ApiModelProperty(required = true, value = "")
        @NotNull


        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public Org styleHash(String styleHash) {
            this.styleHash = styleHash;
            return this;
        }

        /**
         * Get styleHash
         *
         * @return styleHash
         */
      //  @ApiModelProperty(required = true, value = "")
        @NotNull


        public String getStyleHash() {
            return styleHash;
        }

        public void setStyleHash(String styleHash) {
            this.styleHash = styleHash;
        }

        public Org favicon(String favicon) {
            this.favicon = favicon;
            return this;
        }

        /**
         * Get favicon
         *
         * @return favicon
         */
      //  @ApiModelProperty(required = true, value = "")
        @NotNull


        public String getFavicon() {
            return favicon;
        }

        public void setFavicon(String favicon) {
            this.favicon = favicon;
        }

        public Org notifications(List<String> notifications) {
            this.notifications = notifications;
            return this;
        }

        public Org addNotificationsItem(String notificationsItem) {
            this.notifications.add(notificationsItem);
            return this;
        }

        /**
         * Get notifications
         *
         * @return notifications
         */
      //  @ApiModelProperty(required = true, value = "")
        @NotNull


        public List<String> getNotifications() {
            return notifications;
        }

        public void setNotifications(List<String> notifications) {
            this.notifications = notifications;
        }

        public Org status(Integer status) {
            this.status = status;
            return this;
        }

        /**
         * Get status
         *
         * @return status
         */
      //  @ApiModelProperty(required = true, value = "")
        @NotNull


        public Integer getStatus() {
            return status;
        }

        public void setStatus(Integer status) {
            this.status = status;
        }


        @Override
        public boolean equals(Object o) {
            if (this == o) {
                return true;
            }
            if (o == null || getClass() != o.getClass()) {
                return false;
            }
            Org org = (Org) o;
            return Objects.equals(this.orgType, org.orgType) && Objects.equals(this.uuid, org.uuid) && Objects.equals(this.name, org.name) && Objects.equals(this.styleHash, org.styleHash) && Objects.equals(this.favicon, org.favicon) && Objects.equals(this.notifications, org.notifications) && Objects.equals(this.status, org.status);
        }

        @Override
        public int hashCode() {
            return Objects.hash(orgType, uuid, name, styleHash, favicon, notifications, status);
        }

        @Override
        public String toString() {
            StringBuilder sb = new StringBuilder();
            sb.append("class Org {\n");

            sb.append("    orgType: ").append(toIndentedString(orgType)).append("\n");
            sb.append("    uuid: ").append(toIndentedString(uuid)).append("\n");
            sb.append("    name: ").append(toIndentedString(name)).append("\n");
            sb.append("    styleHash: ").append(toIndentedString(styleHash)).append("\n");
            sb.append("    favicon: ").append(toIndentedString(favicon)).append("\n");
            sb.append("    notifications: ").append(toIndentedString(notifications)).append("\n");
            sb.append("    status: ").append(toIndentedString(status)).append("\n");
            sb.append("}");
            return sb.toString();
        }

        /**
         * Convert the given object to string with each line indented by 4 spaces
         * (except the first line).
         */
        private String toIndentedString(Object o) {
            if (o == null) {
                return "null";
            }
            return o.toString().replace("\n", "\n    ");
        }
    }
}

