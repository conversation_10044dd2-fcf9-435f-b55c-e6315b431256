package com.yaduo.devopstools.ones.client;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.yaduo.devopstools.ones.model.AddTaskResponse;
import com.yaduo.devopstools.ones.model.Issue;
import com.yaduo.devopstools.ones.model.LibraryResponse;
import com.yaduo.devopstools.ones.model.Project;
import com.yaduo.devopstools.ones.model.Task;
import com.yaduo.devopstools.ones.model.TaskAdd2TaskRequest;
import com.yaduo.devopstools.ones.model.TokenInfo;
import com.yaduo.devopstools.ones.model.TokenInfo.User;
import com.yaduo.devopstools.ones.model.test.TestCaseResponse;
import com.yaduo.devopstools.ones.model.test.TestPlanResponse;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Request.Builder;
import okhttp3.RequestBody;
import okhttp3.Response;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.Charset;
import java.time.Duration;
import java.util.List;
import java.util.UUID;


/**
 * ones的api接口
 *
 * <AUTHOR>
 * @date 2022-04-16 23:33
 */
public class OnesApiClient {

    private String cookie;

    private OkHttpClient client = new OkHttpClient().newBuilder()
            .connectTimeout(Duration.ofSeconds(10))
            .readTimeout(Duration.ofSeconds(120))
            .writeTimeout(Duration.ofSeconds(120))
            .build();

    public OnesApiClient(String cookie) {
        this.cookie = cookie;
    }

    Logger log = LoggerFactory.getLogger(OnesApiClient.class);

    public AddTaskResponse addTask(TaskAdd2TaskRequest taskAdd2TaskRequest) {
        ObjectMapper objectMapper = new ObjectMapper();

        MediaType mediaType = MediaType.parse("application/json");
        RequestBody body = null;
        try {
            body = RequestBody.create(objectMapper.writeValueAsString(taskAdd2TaskRequest), mediaType);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
        Request request = buildRequest()
                .url("https://ones.corp.yaduo.com/project/api/project/team/KMgnuFDd/tasks/add2")
                .method("POST", body)
                .build();
        try {
            Response response = client.newCall(request).execute();
            log.info(objectMapper.writeValueAsString(taskAdd2TaskRequest) + "\n" + response.toString());
            if (response.isSuccessful()) {
                log.info("success");
                return JSON.parseObject(response.body().string(), AddTaskResponse.class);
            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        return null;
    }


    public Task taskDetail(Integer onesId) {
        Request request = buildRequest()
                .url("https://ones.corp.yaduo.com/project/api/project/team/KMgnuFDd/task/" + onesId + "/info")
                .method("GET", null)
                .build();
        try {
            Response response = client.newCall(request).execute();
            return JSON.parseObject(response.body().string(), Task.class);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    public List<User> users() {
        Request request = buildRequest()
                .url("https://ones.corp.yaduo.com/project/api/project/team/KMgnuFDd/members")
                .get()
                .build();
        try {
            Response response = client.newCall(request).execute();
            String json = response.body().string();
            if (response.isSuccessful()) {
                return JSON.parseObject(json).getJSONArray("members").toJavaList(User.class);
            }
            throw new RuntimeException("error to request users,respone : " + json);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    @NotNull
    Builder buildRequest() {
        return new Builder()
//        request = {"uuid": ''.join(str(uuid.uuid4()).split('-'))[0:8], "text": comment}
//        headers = {'Ones-Auth-Token': '', 'Ones-User-Id': 'A7vn8Hvb', 'Referer': 'https://ones.corp.yaduo.com'}
//                .addHeader("Cookie", cookie)
                .addHeader("Ones-User-Id", "TAGQfxcK")
                .addHeader("uuid", UUID.randomUUID().toString().replace("-", "").substring(0, 8))
//                .addHeader("Referer", "https://ones.corp.yaduo.com")
                .addHeader("Ones-Auth-Token", "eyJhbGciOiJSUzI1NiIsImtpZCI6ImQ3OWU0OTA2LWJjMzQtNDRlZS00YWY2LTc0ODk4ZjRlNTI2ZiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Z97rwG_yANQGiozlfPQLjY7K87ypwjg8ssx-QZq6EBLnZt2_5xlluhbcrXQnjc5OIAH_ZRE9tIx5rKzdgFiaojy45gzI0Ii8TqFnFWq-Hbv0_U60Y5ThXOOqilFTAUq-R5IOI4F26EXHH-ZZ7QHDpFT0LLYzI708TznRse8YOz8E5yLg1aoKWSPSNLs7URGz2Nq1v-2iTtXKf3ZiWtM_u5oyeV035SIvwZR2T_dBo3QKtvSRLMu1uh_zQNBAVA1KbGvITLRqSGjcgSP3kz6tc-QRdm5YAm_mrSmtXDXrEff-yQFMAwIHsVhcdafFfRRooTKihPiPuoGCPlEAAsAEiw")
                .addHeader("User-Agent", "apifox/1.0.0 (https://www.apifox.cn)")
                .addHeader("Content-Type", "application/json");
    }

    public TokenInfo tokenInfo() {
        Request request = buildRequest()
                .url("https://ones.corp.yaduo.com/project/api/project/auth/token_info")
                .method("GET", null)
                .build();
        try {
            Response response = client.newCall(request).execute();
            String body = response.body().string();
            if (response.isSuccessful()) {
                return JSON.parseObject(body, TokenInfo.class);
            } else {
                throw new RuntimeException("error," + body);
            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    public TestPlanResponse plans(String teamUuid) {
        Request request = buildRequest()
                .url("https://ones.corp.yaduo.com/project/api/project/team/" + teamUuid + "/testcase/plans")
                .method("GET", null)
                .build();
        try {
            Response response = client.newCall(request).execute();
            TestPlanResponse testPlanResponse = JSON.parseObject(response.body().string(), TestPlanResponse.class);
            return testPlanResponse;
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    public LibraryResponse libraries(String teamUuid) {
        Request request = buildRequest()
                .url("https://ones.corp.yaduo.com/project/api/project/team/" + teamUuid + "/testcase/libraries")
                .method("GET", null)
                .build();
        String json = null;
        try {
            Response response = client.newCall(request).execute();
            json = response.body().string();
            return JSON.parseObject(json, LibraryResponse.class);
        } catch (IOException e) {
            log.error("error {} to parse response:{}", request.url(), json);
            throw new RuntimeException(e);
        }
    }

    public TestCaseResponse testCases(String teamUuid, String planUuid) {
        Request request = buildRequest()
                .url("https://ones.corp.yaduo.com/project/api/project/team/" + teamUuid + "/testcase/plan/" + planUuid + "/cases")
                .method("GET", null)
                .build();
        try {
            Response response = client.newCall(request).execute();
            return JSON.parseObject(response.body().string(), TestCaseResponse.class);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    public TestCaseResponse testLibraryCases(String teamUuid, String libraryUUID) {
        Request request = buildRequest()
                .url("https://ones.corp.yaduo.com/project/api/project/team/" + teamUuid + "/testcase/library/" + libraryUUID + "/cases")
                .method("GET", null)
                .build();
        try {
            Response response = client.newCall(request).execute();
            log.info("testLibraryCases requestUrl", request.url());
            return JSON.parseObject(response.body().string(), TestCaseResponse.class);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    public List<Project> projects(String teamUuid) {
        Request request = buildRequest()
                .url("https://ones.corp.yaduo.com/project/api/project/team/" + teamUuid + "/projects/my_project")
                .method("GET", null)
                .build();
        try {
            Response response = client.newCall(request).execute();
            return JSON.parseObject(response.body().string()).getJSONArray("projects").toJavaList(Project.class);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }


    public JSONObject detailByOnesId(String onesId) {
//        ?q=%2374870&start=0&limit=10&types=task
        Integer onesNumber = Integer.parseInt(onesId.replace("#", ""));
        List<Issue> issues = searchIssueList(onesId);
        Issue issue = issues.stream().filter(it -> it.getFields().getNumber().equals(onesNumber)).findFirst().orElse(null);
        if (issue != null) {
            JSONObject jsonObject = taskDetail(issue.getFields().getUuid());
            jsonObject.getString("name");
            jsonObject.getString("number");
            jsonObject.getString("planStartDate");
            jsonObject.getString("planEndDate");
            jsonObject.getJSONObject("project").getString("name");
            return jsonObject;
        }
        return null;
    }

    private JSONObject taskDetail(String uuid) {
//
        String json = "{\"query\":\"query Task($key: Key) {\\n  task(key: $key) {\\n    key\\n    ...TaskHeader_task1\\n    ...Permission_Task1\\n    ...TaskAction_task1\\n    ...Permission_Task2\\n    ...TaskPrimaryFields_task1\\n    ...Permission_Task3\\n    ...TaskTabs_task1\\n    ...TaskDesc1\\n    ...TaskFieldList_task1\\n    ...TaskFieldList_task2\\n    ...WideTaskSide_task1\\n    ...Permission_Task4\\n  }\\n}\\n\\nfragment TaskHeader_task1 on Task {\\n  uuid\\n  number\\n  name\\n  issueType {\\n    key\\n    uuid\\n    name\\n    builtIn\\n  }\\n  subIssueType {\\n    key\\n    uuid\\n    name\\n    builtIn\\n  }\\n  canView(attachPermission: {permissions: [\\\"view_tasks\\\"]})\\n  canEdit(attachPermission: {permissions: [\\\"update_tasks\\\"]})\\n  canDelete(attachPermission: {permissions: [\\\"delete_tasks\\\"]})\\n  canTransitTask(attachPermission: {permissions: [\\\"transit_tasks\\\"]})\\n  canUpdateWatchers(attachPermission: {permissions: [\\\"update_task_watchers\\\"]})\\n  parent {\\n    uuid\\n    number\\n    issueType {\\n      uuid\\n    }\\n    subIssueType {\\n      uuid\\n    }\\n    canView(attachPermission: {permissions: [\\\"view_tasks\\\"]})\\n    parent {\\n      uuid\\n      number\\n      issueType {\\n        uuid\\n      }\\n      subIssueType {\\n        uuid\\n      }\\n      canView(attachPermission: {permissions: [\\\"view_tasks\\\"]})\\n      parent {\\n        uuid\\n        number\\n        issueType {\\n          uuid\\n        }\\n        subIssueType {\\n          uuid\\n        }\\n        canView(attachPermission: {permissions: [\\\"view_tasks\\\"]})\\n        parent {\\n          uuid\\n          number\\n          issueType {\\n            uuid\\n          }\\n          subIssueType {\\n            uuid\\n          }\\n          canView(attachPermission: {permissions: [\\\"view_tasks\\\"]})\\n          parent {\\n            uuid\\n            number\\n            issueType {\\n              uuid\\n            }\\n            subIssueType {\\n              uuid\\n            }\\n            canView(attachPermission: {permissions: [\\\"view_tasks\\\"]})\\n            parent {\\n              uuid\\n              number\\n              issueType {\\n                uuid\\n              }\\n              subIssueType {\\n                uuid\\n              }\\n              canView(attachPermission: {permissions: [\\\"view_tasks\\\"]})\\n              parent {\\n                uuid\\n                number\\n                issueType {\\n                  uuid\\n                }\\n                subIssueType {\\n                  uuid\\n                }\\n                canView(attachPermission: {permissions: [\\\"view_tasks\\\"]})\\n                parent {\\n                  uuid\\n                  number\\n                  issueType {\\n                    uuid\\n                  }\\n                  subIssueType {\\n                    uuid\\n                  }\\n                  canView(attachPermission: {permissions: [\\\"view_tasks\\\"]})\\n                  parent {\\n                    uuid\\n                    number\\n                    issueType {\\n                      uuid\\n                    }\\n                    subIssueType {\\n                      uuid\\n                    }\\n                    canView(attachPermission: {permissions: [\\\"view_tasks\\\"]})\\n                    parent {\\n                      uuid\\n                      number\\n                      issueType {\\n                        uuid\\n                      }\\n                      subIssueType {\\n                        uuid\\n                      }\\n                      canView(attachPermission: {permissions: [\\\"view_tasks\\\"]})\\n                    }\\n                  }\\n                }\\n              }\\n            }\\n          }\\n        }\\n      }\\n    }\\n  }\\n}\\n\\nfragment Permission_Task1 on Task {\\n  project {\\n    uuid\\n  }\\n  issueTypeScope {\\n    uuid\\n  }\\n  issueType {\\n    uuid\\n    name\\n  }\\n  _XvqbuXrx {\\n    uuid\\n    name\\n    namePinyin\\n    avatar\\n  }\\n  _2FxCoXXj {\\n    uuid\\n    name\\n    namePinyin\\n    avatar\\n  }\\n  _NvukmCxT {\\n    uuid\\n    name\\n    namePinyin\\n    avatar\\n  }\\n  _HHqvUzx1 {\\n    uuid\\n    name\\n    namePinyin\\n    avatar\\n  }\\n  _9uNZTEXh {\\n    uuid\\n    name\\n    namePinyin\\n    avatar\\n  }\\n  _NDfgosLm {\\n    uuid\\n    name\\n    namePinyin\\n    avatar\\n  }\\n  _4j65cdsK {\\n    uuid\\n    name\\n    namePinyin\\n    avatar\\n  }\\n  _HiK8vp2Y {\\n    uuid\\n    name\\n    namePinyin\\n    avatar\\n  }\\n  owner {\\n    uuid\\n    name\\n    namePinyin\\n    avatar\\n  }\\n  _JEmYi6MX {\\n    uuid\\n    name\\n    namePinyin\\n    avatar\\n  }\\n  _QtZbMNFu {\\n    uuid\\n    name\\n    namePinyin\\n    avatar\\n  }\\n  assign {\\n    uuid\\n    name\\n    namePinyin\\n    avatar\\n  }\\n  watchers {\\n    uuid\\n    name\\n    namePinyin\\n    avatar\\n  }\\n  _YYcUXvpW {\\n    uuid\\n    name\\n    namePinyin\\n    avatar\\n  }\\n  _JxQ5Jg4Y {\\n    uuid\\n    name\\n    namePinyin\\n    avatar\\n  }\\n  _2sjCMMQd {\\n    uuid\\n    name\\n    namePinyin\\n    avatar\\n  }\\n  _SN9uXi8s {\\n    uuid\\n    name\\n    namePinyin\\n    avatar\\n  }\\n  _V1F5kp9a {\\n    uuid\\n    name\\n    namePinyin\\n    avatar\\n  }\\n  _8Kd3q6ft {\\n    uuid\\n    name\\n    namePinyin\\n    avatar\\n  }\\n  _6qhkPtdA {\\n    uuid\\n    name\\n    namePinyin\\n    avatar\\n  }\\n  _7xj4T7Vi {\\n    uuid\\n    name\\n    namePinyin\\n    avatar\\n  }\\n  _85NnJKos {\\n    uuid\\n    name\\n    namePinyin\\n    avatar\\n  }\\n  hasEditPermission(attachPermission: {permissions: [\\\"update_tasks\\\"]})\\n}\\n\\nfragment TaskAction_task1 on Task {\\n  key\\n  path\\n  project {\\n    uuid\\n    name\\n    namePinyin\\n    isSample\\n    isArchive\\n    activityChart {\\n      uuid\\n    }\\n  }\\n  sprint {\\n    description\\n    name\\n    namePinyin\\n    uuid\\n    project {\\n      name\\n      sprintComponent {\\n        uuid\\n      }\\n      uuid\\n    }\\n  }\\n  priority {\\n    bgColor\\n    color\\n    defaultSelected\\n    position\\n    uuid\\n    value\\n  }\\n  products {\\n    name\\n    uuid\\n  }\\n  productModules {\\n    key\\n    name\\n    namePinyin\\n    uuid\\n  }\\n  uuid\\n  project {\\n    uuid\\n    isSample\\n    isArchive\\n  }\\n  issueType {\\n    uuid\\n    detailType\\n  }\\n  canEdit(attachPermission: {permissions: [\\\"update_tasks\\\"]})\\n  project {\\n    uuid\\n  }\\n  path\\n  project {\\n    uuid\\n    isArchive\\n  }\\n  issueType {\\n    uuid\\n  }\\n  subTaskCount\\n  subTaskDoneCount\\n  sprint {\\n    name\\n    uuid\\n  }\\n  relatedWikiPages {\\n    uuid\\n    title\\n    referenceType\\n    ref_type: referenceType\\n    subReferenceType\\n    sub_ref_type: subReferenceType\\n    errorMessage\\n  }\\n  relatedWikiPagesCount\\n  relatedCases {\\n    uuid\\n  }\\n  allRelatedCases(stubRelatedCases: {}) {\\n    uuid\\n    name\\n    id\\n    path\\n    testcaseLibrary {\\n      uuid\\n      name\\n    }\\n  }\\n}\\n\\nfragment Permission_Task2 on Task {\\n  project {\\n    uuid\\n  }\\n  issueTypeScope {\\n    uuid\\n  }\\n  issueType {\\n    uuid\\n    name\\n  }\\n  _XvqbuXrx {\\n    uuid\\n    name\\n    namePinyin\\n    avatar\\n  }\\n  _2FxCoXXj {\\n    uuid\\n    name\\n    namePinyin\\n    avatar\\n  }\\n  _NvukmCxT {\\n    uuid\\n    name\\n    namePinyin\\n    avatar\\n  }\\n  _HHqvUzx1 {\\n    uuid\\n    name\\n    namePinyin\\n    avatar\\n  }\\n  _9uNZTEXh {\\n    uuid\\n    name\\n    namePinyin\\n    avatar\\n  }\\n  _NDfgosLm {\\n    uuid\\n    name\\n    namePinyin\\n    avatar\\n  }\\n  _4j65cdsK {\\n    uuid\\n    name\\n    namePinyin\\n    avatar\\n  }\\n  _HiK8vp2Y {\\n    uuid\\n    name\\n    namePinyin\\n    avatar\\n  }\\n  owner {\\n    uuid\\n    name\\n    namePinyin\\n    avatar\\n  }\\n  _JEmYi6MX {\\n    uuid\\n    name\\n    namePinyin\\n    avatar\\n  }\\n  _QtZbMNFu {\\n    uuid\\n    name\\n    namePinyin\\n    avatar\\n  }\\n  assign {\\n    uuid\\n    name\\n    namePinyin\\n    avatar\\n  }\\n  watchers {\\n    uuid\\n    name\\n    namePinyin\\n    avatar\\n  }\\n  _YYcUXvpW {\\n    uuid\\n    name\\n    namePinyin\\n    avatar\\n  }\\n  _JxQ5Jg4Y {\\n    uuid\\n    name\\n    namePinyin\\n    avatar\\n  }\\n  _2sjCMMQd {\\n    uuid\\n    name\\n    namePinyin\\n    avatar\\n  }\\n  _SN9uXi8s {\\n    uuid\\n    name\\n    namePinyin\\n    avatar\\n  }\\n  _V1F5kp9a {\\n    uuid\\n    name\\n    namePinyin\\n    avatar\\n  }\\n  _8Kd3q6ft {\\n    uuid\\n    name\\n    namePinyin\\n    avatar\\n  }\\n  _6qhkPtdA {\\n    uuid\\n    name\\n    namePinyin\\n    avatar\\n  }\\n  _7xj4T7Vi {\\n    uuid\\n    name\\n    namePinyin\\n    avatar\\n  }\\n  _85NnJKos {\\n    uuid\\n    name\\n    namePinyin\\n    avatar\\n  }\\n  hasEditPermission(attachPermission: {permissions: [\\\"update_tasks\\\"]})\\n  hasDeletePermission(attachPermission: {permissions: [\\\"delete_tasks\\\"]})\\n  hasUpdateStatusPermission(attachPermission: {permissions: [\\\"transit_tasks\\\"]})\\n  hasUpdateAllManHourPermission(attachPermission: {permissions: [\\\"manage_task_record_manhours\\\"]})\\n  hasUpdateOwnManHourPermission(attachPermission: {permissions: [\\\"manage_task_own_record_manhours\\\"]})\\n  hasChangeIssueTypePermission(attachPermission: {permissions: [\\\"update_tasks_issue_type\\\"]})\\n}\\n\\nfragment TaskPrimaryFields_task1 on Task {\\n  uuid\\n  project {\\n    uuid\\n  }\\n  issueType {\\n    uuid\\n  }\\n  subIssueType {\\n    uuid\\n  }\\n  assign {\\n    uuid\\n    name\\n    namePinyin\\n    avatar\\n  }\\n  status {\\n    uuid\\n    name\\n    namePinyin\\n    category\\n    builtIn\\n    detailType\\n  }\\n  priority {\\n    bgColor\\n    color\\n    defaultSelected\\n    position\\n    uuid\\n    value\\n  }\\n  planEndDate(unit: ONESDATE)\\n  planStartDate(unit: ONESDATE)\\n}\\n\\nfragment Permission_Task3 on Task {\\n  project {\\n    uuid\\n  }\\n  issueTypeScope {\\n    uuid\\n  }\\n  issueType {\\n    uuid\\n    name\\n  }\\n  _XvqbuXrx {\\n    uuid\\n    name\\n    namePinyin\\n    avatar\\n  }\\n  _2FxCoXXj {\\n    uuid\\n    name\\n    namePinyin\\n    avatar\\n  }\\n  _NvukmCxT {\\n    uuid\\n    name\\n    namePinyin\\n    avatar\\n  }\\n  _HHqvUzx1 {\\n    uuid\\n    name\\n    namePinyin\\n    avatar\\n  }\\n  _9uNZTEXh {\\n    uuid\\n    name\\n    namePinyin\\n    avatar\\n  }\\n  _NDfgosLm {\\n    uuid\\n    name\\n    namePinyin\\n    avatar\\n  }\\n  _4j65cdsK {\\n    uuid\\n    name\\n    namePinyin\\n    avatar\\n  }\\n  _HiK8vp2Y {\\n    uuid\\n    name\\n    namePinyin\\n    avatar\\n  }\\n  owner {\\n    uuid\\n    name\\n    namePinyin\\n    avatar\\n  }\\n  _JEmYi6MX {\\n    uuid\\n    name\\n    namePinyin\\n    avatar\\n  }\\n  _QtZbMNFu {\\n    uuid\\n    name\\n    namePinyin\\n    avatar\\n  }\\n  assign {\\n    uuid\\n    name\\n    namePinyin\\n    avatar\\n  }\\n  watchers {\\n    uuid\\n    name\\n    namePinyin\\n    avatar\\n  }\\n  _YYcUXvpW {\\n    uuid\\n    name\\n    namePinyin\\n    avatar\\n  }\\n  _JxQ5Jg4Y {\\n    uuid\\n    name\\n    namePinyin\\n    avatar\\n  }\\n  _2sjCMMQd {\\n    uuid\\n    name\\n    namePinyin\\n    avatar\\n  }\\n  _SN9uXi8s {\\n    uuid\\n    name\\n    namePinyin\\n    avatar\\n  }\\n  _V1F5kp9a {\\n    uuid\\n    name\\n    namePinyin\\n    avatar\\n  }\\n  _8Kd3q6ft {\\n    uuid\\n    name\\n    namePinyin\\n    avatar\\n  }\\n  _6qhkPtdA {\\n    uuid\\n    name\\n    namePinyin\\n    avatar\\n  }\\n  _7xj4T7Vi {\\n    uuid\\n    name\\n    namePinyin\\n    avatar\\n  }\\n  _85NnJKos {\\n    uuid\\n    name\\n    namePinyin\\n    avatar\\n  }\\n  hasEditPermission(attachPermission: {permissions: [\\\"update_tasks\\\"]})\\n  hasUpdateStatusPermission(attachPermission: {permissions: [\\\"transit_tasks\\\"]})\\n}\\n\\nfragment TaskTabs_task1 on Task {\\n  key\\n  subTaskCount\\n  subTaskDoneCount\\n  relatedTasksCount\\n  allRelatedTasksCount: stubRelatedTasksCount\\n  relatedActivitiesCount\\n  attachmentCount\\n  relatedCasesCount\\n  allRelatedCasesCount: stubRelatedCasesCount\\n  relatedTestcasePlanCasesCount\\n  allRelatedTestcasePlanCasesCount: stubRelatedTestcasePlanCasesCount\\n  relatedTestcasePlansCount\\n  allRelatedTestcasePlansCount: stubRelatedTestcasePlansCount\\n  relatedWikiPagesCount\\n  devopsCommitCount\\n  codeCommitsCount\\n  devopsPullRequestsCount\\n  devopsBranchesCount\\n}\\n\\nfragment TaskDesc1 on Task {\\n  uuid\\n  description\\n  descriptionText\\n  desc_rich: description\\n}\\n\\nfragment TaskFieldList_task1 on Task {\\n  uuid\\n  project {\\n    uuid\\n  }\\n  issueType {\\n    uuid\\n  }\\n  subIssueType {\\n    uuid\\n  }\\n  products {\\n    name\\n    uuid\\n    value: uuid\\n    label: name\\n  }\\n  allProducts(stubTaskProducts: {}) {\\n    uuid\\n    name\\n  }\\n  productModules {\\n    name\\n    uuid\\n    value: uuid\\n    label: name\\n  }\\n  allProductModules(stubTaskProductModules: {}) {\\n    uuid\\n    name\\n  }\\n  products {\\n    name\\n    uuid\\n    value: uuid\\n    label: name\\n  }\\n  allProducts(stubTaskProducts: {}) {\\n    uuid\\n    name\\n  }\\n  _QYd4rvwQ {\\n    bgColor\\n    color\\n    defaultSelected\\n    position\\n    uuid\\n    value\\n  }\\n  _7iL3xaJX {\\n    bgColor\\n    color\\n    defaultSelected\\n    position\\n    uuid\\n    value\\n  }\\n  priority {\\n    bgColor\\n    color\\n    defaultSelected\\n    position\\n    uuid\\n    value\\n  }\\n  deadline(unit: ONESDATE)\\n  status {\\n    uuid\\n    name\\n    namePinyin\\n    category\\n    builtIn\\n    detailType\\n  }\\n  _BQjic57s {\\n    bgColor\\n    color\\n    defaultSelected\\n    position\\n    uuid\\n    value\\n  }\\n  _NCUT6mj7 {\\n    bgColor\\n    color\\n    defaultSelected\\n    position\\n    uuid\\n    value\\n  }\\n  _D5ShLfvk {\\n    bgColor\\n    color\\n    defaultSelected\\n    position\\n    uuid\\n    value\\n  }\\n  _CKowPjZB {\\n    bgColor\\n    color\\n    defaultSelected\\n    position\\n    uuid\\n    value\\n  }\\n  assign {\\n    uuid\\n    name\\n    namePinyin\\n    avatar\\n  }\\n  _UBVziE7m {\\n    bgColor\\n    color\\n    defaultSelected\\n    position\\n    uuid\\n    value\\n  }\\n  sprint {\\n    description\\n    name\\n    namePinyin\\n    uuid\\n    project {\\n      name\\n      sprintComponent {\\n        uuid\\n      }\\n      uuid\\n    }\\n    value: uuid\\n    label: name\\n  }\\n  _XitKU7P6 {\\n    bgColor\\n    color\\n    defaultSelected\\n    position\\n    uuid\\n    value\\n  }\\n  _S9adxLM1\\n  _KkKgTJNZ\\n  _6qhkPtdA {\\n    uuid\\n    name\\n    namePinyin\\n    avatar\\n  }\\n  _NcWHX55k\\n  _PxiMrQ6M\\n  _85NnJKos {\\n    uuid\\n    name\\n    namePinyin\\n    avatar\\n  }\\n  _CNwMSsLY\\n  _M7TbjqE6\\n  _PbYGJdKt {\\n    bgColor\\n    color\\n    defaultSelected\\n    position\\n    uuid\\n    value\\n  }\\n  _NVx78zku {\\n    bgColor\\n    color\\n    defaultSelected\\n    position\\n    uuid\\n    value\\n  }\\n  _HtvnJ6zx(unit: ONESDATE)\\n  _YUUncgeB {\\n    bgColor\\n    color\\n    defaultSelected\\n    position\\n    uuid\\n    value\\n  }\\n  _HFzi4TjX\\n  _SnYX7Rbq {\\n    bgColor\\n    color\\n    defaultSelected\\n    position\\n    uuid\\n    value\\n  }\\n  _FTmaJCXF\\n  _Y2mRmv69(unit: ONESDATE)\\n  _CuNGTTPf\\n  _K8zxhQk9\\n  _X46SWeN1(unit: ONESDATE)\\n  _6PuX6Wb5(unit: ONESDATE)\\n  _7Xu8Wwdb(unit: ONESDATE)\\n  _Au49jviJ {\\n    bgColor\\n    color\\n    defaultSelected\\n    position\\n    uuid\\n    value\\n  }\\n  publishDate(unit: ONESDATE)\\n  _AxxmcgeW\\n  _CSEwfFwC(unit: ONESDATE)\\n  _VGmcaUUP {\\n    bgColor\\n    color\\n    defaultSelected\\n    position\\n    uuid\\n    value\\n  }\\n  _7iaKCvPZ(unit: ONESDATE)\\n  _EeFi6tAL\\n  _3gb1Z8Kh\\n  _CtizUPS7\\n  _MZLg25tV\\n  _9GisdRzX\\n  _4LFn6tEs {\\n    bgColor\\n    color\\n    defaultSelected\\n    position\\n    uuid\\n    value\\n  }\\n  _Nh6yMRKC\\n  _PbWe33BR\\n  _PY7RgaDs\\n  _QGDxM78H\\n  _2psyeK4i\\n  _YNpUZ3N4(unit: ONESDATE)\\n  _2jymqZQe\\n  _7CN3bGZg\\n  _RgUJrmrG\\n  _8b9X7fh9\\n  _SdnKZgqT(unit: ONESDATE)\\n  _6P4xDHSV(unit: ONESDATE)\\n  _U3zTGqJe(unit: ONESDATE)\\n  _V1F5kp9a {\\n    uuid\\n    name\\n    namePinyin\\n    avatar\\n  }\\n  _FXbNfMYC(unit: ONESDATE)\\n  _KWw84sv1(unit: ONESDATE)\\n  _JxQ5Jg4Y {\\n    uuid\\n    name\\n    namePinyin\\n    avatar\\n  }\\n  _2VHWmPqR(unit: ONESDATE)\\n  _JCTAZLgr(unit: ONESDATE)\\n  _9uNZTEXh {\\n    uuid\\n    name\\n    namePinyin\\n    avatar\\n  }\\n  _WTF4MSnL(unit: ONESDATE)\\n  _VNkCotNz {\\n    bgColor\\n    color\\n    defaultSelected\\n    position\\n    uuid\\n    value\\n  }\\n  _5GDec5PA {\\n    bgColor\\n    color\\n    defaultSelected\\n    position\\n    uuid\\n    value\\n  }\\n  _5jMbWb2K\\n  _AGuvdPJJ {\\n    bgColor\\n    color\\n    defaultSelected\\n    position\\n    uuid\\n    value\\n  }\\n  _T5Qt3pMw {\\n    bgColor\\n    color\\n    defaultSelected\\n    position\\n    uuid\\n    value\\n  }\\n  _NW987Usc(unit: ONESDATE)\\n  _VarHXD1e\\n  _FoCShTcY\\n  _2tP1YD6G\\n  _AuxYqe3N\\n  _L9A7mn8R\\n  _RrhL4ouU\\n  _2sjCMMQd {\\n    uuid\\n    name\\n    namePinyin\\n    avatar\\n  }\\n  _8Kd3q6ft {\\n    uuid\\n    name\\n    namePinyin\\n    avatar\\n  }\\n  _NvukmCxT {\\n    uuid\\n    name\\n    namePinyin\\n    avatar\\n  }\\n  _SN9uXi8s {\\n    uuid\\n    name\\n    namePinyin\\n    avatar\\n  }\\n  _Vn7S1YQ3\\n  _7xj4T7Vi {\\n    uuid\\n    name\\n    namePinyin\\n    avatar\\n  }\\n  _XJyaJuEc\\n  _2FxCoXXj {\\n    uuid\\n    name\\n    namePinyin\\n    avatar\\n  }\\n  _8Lz2iyGN\\n  _XvqbuXrx {\\n    uuid\\n    name\\n    namePinyin\\n    avatar\\n  }\\n  _VWXWo9hu\\n  _YYcUXvpW {\\n    uuid\\n    name\\n    namePinyin\\n    avatar\\n  }\\n  _CziQoW1T\\n  _2C5rXa9s\\n  _4j65cdsK {\\n    uuid\\n    name\\n    namePinyin\\n    avatar\\n  }\\n  _NDfgosLm {\\n    uuid\\n    name\\n    namePinyin\\n    avatar\\n  }\\n  _RtmJwDiN\\n  _GopMmJxA(unit: ONESDATE)\\n  _UdDK7LeK(unit: ONESDATE)\\n  _2Qz4neSK\\n  _3dYknae9\\n  _HiK8vp2Y {\\n    uuid\\n    name\\n    namePinyin\\n    avatar\\n  }\\n  _JEmYi6MX {\\n    uuid\\n    name\\n    namePinyin\\n    avatar\\n  }\\n  _HHqvUzx1 {\\n    uuid\\n    name\\n    namePinyin\\n    avatar\\n  }\\n  _DAXWu422\\n  _QtZbMNFu {\\n    uuid\\n    name\\n    namePinyin\\n    avatar\\n  }\\n  _H39Gtywn\\n  _6rnf7JKB {\\n    bgColor\\n    color\\n    defaultSelected\\n    position\\n    uuid\\n    value\\n  }\\n  _EF5EHMBs\\n  _E3DHosht {\\n    bgColor\\n    color\\n    defaultSelected\\n    position\\n    uuid\\n    value\\n  }\\n}\\n\\nfragment TaskFieldList_task2 on Task {\\n  uuid\\n  project {\\n    uuid\\n  }\\n  issueType {\\n    uuid\\n  }\\n  subIssueType {\\n    uuid\\n  }\\n  project {\\n    uuid\\n    name\\n    namePinyin\\n    isSample\\n    isArchive\\n    activityChart {\\n      uuid\\n    }\\n    value: uuid\\n    label: name\\n  }\\n  issueType {\\n    key\\n    uuid\\n    value: uuid\\n    name\\n    label: name\\n    builtIn\\n    namePinyin\\n    icon\\n    manhourStatisticMode\\n    detailType\\n    subIssueType\\n  }\\n  subIssueType {\\n    key\\n    uuid\\n    value: uuid\\n    name\\n    label: name\\n    builtIn\\n    namePinyin\\n    icon\\n    manhourStatisticMode\\n    detailType\\n    subIssueType\\n  }\\n  issueType {\\n    key\\n    uuid\\n    value: uuid\\n    name\\n    label: name\\n    builtIn\\n    namePinyin\\n    icon\\n    manhourStatisticMode\\n    detailType\\n    subIssueType\\n  }\\n  subIssueType {\\n    key\\n    uuid\\n    value: uuid\\n    name\\n    label: name\\n    builtIn\\n    namePinyin\\n    icon\\n    manhourStatisticMode\\n    detailType\\n    subIssueType\\n  }\\n  owner {\\n    uuid\\n    name\\n    namePinyin\\n    avatar\\n  }\\n  createTime\\n  serverUpdateStamp\\n}\\n\\nfragment WideTaskSide_task1 on Task {\\n  owner {\\n    uuid\\n    name\\n    namePinyin\\n    avatar\\n  }\\n  assign {\\n    uuid\\n    name\\n    namePinyin\\n    avatar\\n  }\\n  watchers {\\n    uuid\\n    name\\n    namePinyin\\n    avatar\\n  }\\n  issueType {\\n    key\\n    uuid\\n    value: uuid\\n    name\\n    label: name\\n    builtIn\\n    namePinyin\\n    icon\\n    manhourStatisticMode\\n    detailType\\n    subIssueType\\n  }\\n  subIssueType {\\n    key\\n    uuid\\n    value: uuid\\n    name\\n    label: name\\n    builtIn\\n    namePinyin\\n    icon\\n    manhourStatisticMode\\n    detailType\\n    subIssueType\\n  }\\n}\\n\\nfragment Permission_Task4 on Task {\\n  project {\\n    uuid\\n  }\\n  issueTypeScope {\\n    uuid\\n  }\\n  issueType {\\n    uuid\\n    name\\n  }\\n  _XvqbuXrx {\\n    uuid\\n    name\\n    namePinyin\\n    avatar\\n  }\\n  _2FxCoXXj {\\n    uuid\\n    name\\n    namePinyin\\n    avatar\\n  }\\n  _NvukmCxT {\\n    uuid\\n    name\\n    namePinyin\\n    avatar\\n  }\\n  _HHqvUzx1 {\\n    uuid\\n    name\\n    namePinyin\\n    avatar\\n  }\\n  _9uNZTEXh {\\n    uuid\\n    name\\n    namePinyin\\n    avatar\\n  }\\n  _NDfgosLm {\\n    uuid\\n    name\\n    namePinyin\\n    avatar\\n  }\\n  _4j65cdsK {\\n    uuid\\n    name\\n    namePinyin\\n    avatar\\n  }\\n  _HiK8vp2Y {\\n    uuid\\n    name\\n    namePinyin\\n    avatar\\n  }\\n  owner {\\n    uuid\\n    name\\n    namePinyin\\n    avatar\\n  }\\n  _JEmYi6MX {\\n    uuid\\n    name\\n    namePinyin\\n    avatar\\n  }\\n  _QtZbMNFu {\\n    uuid\\n    name\\n    namePinyin\\n    avatar\\n  }\\n  assign {\\n    uuid\\n    name\\n    namePinyin\\n    avatar\\n  }\\n  watchers {\\n    uuid\\n    name\\n    namePinyin\\n    avatar\\n  }\\n  _YYcUXvpW {\\n    uuid\\n    name\\n    namePinyin\\n    avatar\\n  }\\n  _JxQ5Jg4Y {\\n    uuid\\n    name\\n    namePinyin\\n    avatar\\n  }\\n  _2sjCMMQd {\\n    uuid\\n    name\\n    namePinyin\\n    avatar\\n  }\\n  _SN9uXi8s {\\n    uuid\\n    name\\n    namePinyin\\n    avatar\\n  }\\n  _V1F5kp9a {\\n    uuid\\n    name\\n    namePinyin\\n    avatar\\n  }\\n  _8Kd3q6ft {\\n    uuid\\n    name\\n    namePinyin\\n    avatar\\n  }\\n  _6qhkPtdA {\\n    uuid\\n    name\\n    namePinyin\\n    avatar\\n  }\\n  _7xj4T7Vi {\\n    uuid\\n    name\\n    namePinyin\\n    avatar\\n  }\\n  _85NnJKos {\\n    uuid\\n    name\\n    namePinyin\\n    avatar\\n  }\\n  hasEditPermission(attachPermission: {permissions: [\\\"update_tasks\\\"]})\\n  hasUpdateWatchersPermission(attachPermission: {permissions: [\\\"update_task_watchers\\\"]})\\n}\\n\",\"variables\":{\"key\":\"task-" + uuid + "\"}}";
        Request post = buildRequest().url("https://ones.corp.yaduo.com/project/api/project/team/KMgnuFDd/items/graphql?t=Task")
                .method("POST", RequestBody.create(json, MediaType.parse("application/json; charset=UTF-8"))).build();
        try {
            Response response = client.newCall(post).execute();
            return JSON.parseObject(response.body().string()).getJSONObject("data").getJSONObject("task");
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    private List<Issue> searchIssueList(String onesId) {
        Request post = buildRequest().url("https://ones.corp.yaduo.com/project/api/project/team/KMgnuFDd/search?q=" + URLEncoder.encode(onesId, Charset.defaultCharset()) + "&start=0&limit=10&types=task").method("GET", null).build();
        try {
            Response response = client.newCall(post).execute();
            return JSON.parseObject(response.body().string()).getJSONObject("datas").getJSONArray("task").toJavaList(Issue.class);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }
}
