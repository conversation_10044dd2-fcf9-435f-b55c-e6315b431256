package com.yaduo.devopstools.ones.model.test;

import com.fasterxml.jackson.annotation.JsonProperty;

import javax.validation.constraints.NotNull;
import java.util.Objects;

/**
 * InlineResponse200Members
 */
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.SpringCodegen", date = "2022-04-21T21:27:40.321239+08:00[Asia/Shanghai]")

public class Members {
  @JsonProperty("user_domain_type")
  private String userDomainType;

  @JsonProperty("user_domain_param")
  private String userDomainParam;

  public Members userDomainType(String userDomainType) {
    this.userDomainType = userDomainType;
    return this;
  }

  /**
   * Get userDomainType
   * @return userDomainType
  */
//  @ApiModelProperty(required = true, value = "")
  @NotNull


  public String getUserDomainType() {
    return userDomainType;
  }

  public void setUserDomainType(String userDomainType) {
    this.userDomainType = userDomainType;
  }

  public Members userDomainParam(String userDomainParam) {
    this.userDomainParam = userDomainParam;
    return this;
  }

  /**
   * Get userDomainParam
   * @return userDomainParam
  */
//  @ApiModelProperty(required = true, value = "")
  @NotNull


  public String getUserDomainParam() {
    return userDomainParam;
  }

  public void setUserDomainParam(String userDomainParam) {
    this.userDomainParam = userDomainParam;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    Members inlineResponse200Members = (Members) o;
    return Objects.equals(this.userDomainType, inlineResponse200Members.userDomainType) &&
        Objects.equals(this.userDomainParam, inlineResponse200Members.userDomainParam);
  }

  @Override
  public int hashCode() {
    return Objects.hash(userDomainType, userDomainParam);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class InlineResponse200Members {\n");
    
    sb.append("    userDomainType: ").append(toIndentedString(userDomainType)).append("\n");
    sb.append("    userDomainParam: ").append(toIndentedString(userDomainParam)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

