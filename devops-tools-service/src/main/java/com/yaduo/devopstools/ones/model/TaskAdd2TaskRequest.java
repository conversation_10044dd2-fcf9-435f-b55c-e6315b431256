package com.yaduo.devopstools.ones.model;

import com.alibaba.fastjson.annotation.JSONField;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * InlineObject
 */
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.SpringCodegen", date = "2022-04-17T14:07:26.169847+08:00[Asia/Shanghai]")

public class TaskAdd2TaskRequest {
  @JSONField(name = "tasks")
  @Valid
  private List<TaskAdd2Tasks> tasks = new ArrayList<>();

  public TaskAdd2TaskRequest tasks(List<TaskAdd2Tasks> tasks) {
    this.tasks = tasks;
    return this;
  }

  public TaskAdd2TaskRequest addTasksItem(TaskAdd2Tasks tasksItem) {
    this.tasks.add(tasksItem);
    return this;
  }

  /**
   * Get tasks
   * @return tasks
  */
//  @ApiModelProperty(required = true, value = "")
  @NotNull

  @Valid

  public List<TaskAdd2Tasks> getTasks() {
    return tasks;
  }

  public void setTasks(List<TaskAdd2Tasks> tasks) {
    this.tasks = tasks;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    TaskAdd2TaskRequest inlineObject = (TaskAdd2TaskRequest) o;
    return Objects.equals(this.tasks, inlineObject.tasks);
  }

  @Override
  public int hashCode() {
    return Objects.hash(tasks);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class InlineObject {\n");
    sb.append("    tasks: ").append(toIndentedString(tasks)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

