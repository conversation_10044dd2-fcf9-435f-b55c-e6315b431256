package com.yaduo.devopstools.ones.model;

import java.io.Serializable;
import java.util.List;

public class LibraryResponse implements Serializable {

    private List<? extends Libraries> libraries;

    public List<? extends Libraries> getLibraries() {
        return this.libraries;
    }

    public void setLibraries(List<? extends Libraries> libraries) {
        this.libraries = libraries;
    }

    public static class Libraries implements Serializable {
        private String create_time;

        private String name;

        private String field_config_uuid;

        private String uuid;

        private List<? extends Modules> modules;

        private Boolean is_pin;

        public String getCreate_time() {
            return this.create_time;
        }

        public void setCreate_time(String create_time) {
            this.create_time = create_time;
        }

        public String getName() {
            return this.name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getField_config_uuid() {
            return this.field_config_uuid;
        }

        public void setField_config_uuid(String field_config_uuid) {
            this.field_config_uuid = field_config_uuid;
        }

        public String getUuid() {
            return this.uuid;
        }

        public void setUuid(String uuid) {
            this.uuid = uuid;
        }

        public List<? extends Modules> getModules() {
            return this.modules;
        }

        public void setModules(List<? extends Modules> modules) {
            this.modules = modules;
        }

        public Boolean getIs_pin() {
            return this.is_pin;
        }

        public void setIs_pin(Boolean is_pin) {
            this.is_pin = is_pin;
        }

        public static class Modules implements Serializable {

            private String libraryName;

            public String getLibraryName() {
                return libraryName;
            }

            public void setLibraryName(String libraryName) {
                this.libraryName = libraryName;
            }

            private String path;

            private String name_pinyin;

            private String parent_uuid;

            private Long create_time;

            private String library_uuid;

            private String name;

            private String position;

            private Boolean is_default;

            private String uuid;

            public String getPath() {
                return this.path;
            }

            public void setPath(String path) {
                this.path = path;
            }

            public String getName_pinyin() {
                return this.name_pinyin;
            }

            public void setName_pinyin(String name_pinyin) {
                this.name_pinyin = name_pinyin;
            }

            public String getParent_uuid() {
                return this.parent_uuid;
            }

            public void setParent_uuid(String parent_uuid) {
                this.parent_uuid = parent_uuid;
            }

            public Long getCreate_time() {
                return this.create_time;
            }

            public void setCreate_time(Long create_time) {
                this.create_time = create_time;
            }

            public String getLibrary_uuid() {
                return this.library_uuid;
            }

            public void setLibrary_uuid(String library_uuid) {
                this.library_uuid = library_uuid;
            }

            public String getName() {
                return this.name;
            }

            public void setName(String name) {
                this.name = name;
            }

            public String getPosition() {
                return this.position;
            }

            public void setPosition(String position) {
                this.position = position;
            }

            public Boolean getIs_default() {
                return this.is_default;
            }

            public void setIs_default(Boolean is_default) {
                this.is_default = is_default;
            }

            public String getUuid() {
                return this.uuid;
            }

            public void setUuid(String uuid) {
                this.uuid = uuid;
            }
        }
    }
}
