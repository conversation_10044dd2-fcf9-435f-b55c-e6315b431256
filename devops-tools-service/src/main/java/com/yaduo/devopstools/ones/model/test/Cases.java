package com.yaduo.devopstools.ones.model.test;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * InlineResponse200Cases
 */
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.SpringCodegen", date = "2022-04-21T21:32:27.138095+08:00[Asia/Shanghai]")

public class Cases {
  @JsonProperty("uuid")
  private String uuid;

  @JsonProperty("library_uuid")
  private String libraryUuid;

  @JsonProperty("module_uuid")
  private String moduleUuid;

  @JsonProperty("path")
  private String path;

  @JsonProperty("name")
  private String name;

  @JsonProperty("priority")
  private String priority;

  @JsonProperty("type")
  private String type;

  @JsonProperty("assign")
  private String assign;

  @JsonProperty("desc")
  private String desc;

  @JsonProperty("create_time")
  @JSONField(format="unixtime")
  private Date createTime;

  @JsonProperty("number")
  private Integer number;

  @JsonProperty("condition")
  private String condition;

  @JsonProperty("steps")
  @Valid
  private List<String> steps = new ArrayList<>();

  @JsonProperty("name_pinyin")
  private String namePinyin;

  @JsonProperty("plan_uuid")
  private String planUuid;

  @JsonProperty("executor")
  private String executor;

  @JsonProperty("result")
  private String result;

  @JsonProperty("note")
  private String note;

  @JsonProperty("related_tasks")
  @Valid
  private List<RelatedTasks> relatedTasks = new ArrayList<>();

  public Cases uuid(String uuid) {
    this.uuid = uuid;
    return this;
  }

  /**
   * Get uuid
   * @return uuid
  */
//  @ApiModelProperty(required = true, value = "")
  @NotNull


  public String getUuid() {
    return uuid;
  }

  public void setUuid(String uuid) {
    this.uuid = uuid;
  }

  public Cases libraryUuid(String libraryUuid) {
    this.libraryUuid = libraryUuid;
    return this;
  }

  /**
   * Get libraryUuid
   * @return libraryUuid
  */
//  @ApiModelProperty(required = true, value = "")
  @NotNull


  public String getLibraryUuid() {
    return libraryUuid;
  }

  public void setLibraryUuid(String libraryUuid) {
    this.libraryUuid = libraryUuid;
  }

  public Cases moduleUuid(String moduleUuid) {
    this.moduleUuid = moduleUuid;
    return this;
  }

  /**
   * Get moduleUuid
   * @return moduleUuid
  */
//  @ApiModelProperty(required = true, value = "")
  @NotNull


  public String getModuleUuid() {
    return moduleUuid;
  }

  public void setModuleUuid(String moduleUuid) {
    this.moduleUuid = moduleUuid;
  }

  public Cases path(String path) {
    this.path = path;
    return this;
  }

  /**
   * Get path
   * @return path
  */
//  @ApiModelProperty(required = true, value = "")
  @NotNull


  public String getPath() {
    return path;
  }

  public void setPath(String path) {
    this.path = path;
  }

  public Cases name(String name) {
    this.name = name;
    return this;
  }

  /**
   * Get name
   * @return name
  */
//  @ApiModelProperty(required = true, value = "")
  @NotNull


  public String getName() {
    return name;
  }

  public void setName(String name) {
    this.name = name;
  }

  public Cases priority(String priority) {
    this.priority = priority;
    return this;
  }

  /**
   * Get priority
   * @return priority
  */
//  @ApiModelProperty(required = true, value = "")
  @NotNull


  public String getPriority() {
    return priority;
  }

  public void setPriority(String priority) {
    this.priority = priority;
  }

  public Cases type(String type) {
    this.type = type;
    return this;
  }

  /**
   * Get type
   * @return type
  */
//  @ApiModelProperty(required = true, value = "")
  @NotNull


  public String getType() {
    return type;
  }

  public void setType(String type) {
    this.type = type;
  }

  public Cases assign(String assign) {
    this.assign = assign;
    return this;
  }

  /**
   * Get assign
   * @return assign
  */
//  @ApiModelProperty(required = true, value = "")
  @NotNull


  public String getAssign() {
    return assign;
  }

  public void setAssign(String assign) {
    this.assign = assign;
  }

  public Cases desc(String desc) {
    this.desc = desc;
    return this;
  }

  /**
   * Get desc
   * @return desc
  */
//  @ApiModelProperty(required = true, value = "")
  @NotNull


  public String getDesc() {
    return desc;
  }

  public void setDesc(String desc) {
    this.desc = desc;
  }

  public Cases createTime(Date createTime) {
    this.createTime = createTime;
    return this;
  }

  /**
   * Get createTime
   * @return createTime
  */
//  @ApiModelProperty(required = true, value = "")
  @NotNull


  public Date getCreateTime() {
    return createTime;
  }

  public void setCreateTime(Date createTime) {
    this.createTime = createTime;
  }

  public Cases number(Integer number) {
    this.number = number;
    return this;
  }

  /**
   * Get number
   * @return number
  */
//  @ApiModelProperty(required = true, value = "")
  @NotNull


  public Integer getNumber() {
    return number;
  }

  public void setNumber(Integer number) {
    this.number = number;
  }

  public Cases condition(String condition) {
    this.condition = condition;
    return this;
  }

  /**
   * Get condition
   * @return condition
  */
//  @ApiModelProperty(required = true, value = "")
  @NotNull


  public String getCondition() {
    return condition;
  }

  public void setCondition(String condition) {
    this.condition = condition;
  }

  public Cases steps(List<String> steps) {
    this.steps = steps;
    return this;
  }

  public Cases addStepsItem(String stepsItem) {
    this.steps.add(stepsItem);
    return this;
  }

  /**
   * Get steps
   * @return steps
  */
//  @ApiModelProperty(required = true, value = "")
  @NotNull


  public List<String> getSteps() {
    return steps;
  }

  public void setSteps(List<String> steps) {
    this.steps = steps;
  }

  public Cases namePinyin(String namePinyin) {
    this.namePinyin = namePinyin;
    return this;
  }

  /**
   * Get namePinyin
   * @return namePinyin
  */
//  @ApiModelProperty(required = true, value = "")
  @NotNull


  public String getNamePinyin() {
    return namePinyin;
  }

  public void setNamePinyin(String namePinyin) {
    this.namePinyin = namePinyin;
  }

  public Cases planUuid(String planUuid) {
    this.planUuid = planUuid;
    return this;
  }

  /**
   * Get planUuid
   * @return planUuid
  */
//  @ApiModelProperty(required = true, value = "")
  @NotNull


  public String getPlanUuid() {
    return planUuid;
  }

  public void setPlanUuid(String planUuid) {
    this.planUuid = planUuid;
  }

  public Cases executor(String executor) {
    this.executor = executor;
    return this;
  }

  /**
   * Get executor
   * @return executor
  */
//  @ApiModelProperty(required = true, value = "")
  @NotNull


  public String getExecutor() {
    return executor;
  }

  public void setExecutor(String executor) {
    this.executor = executor;
  }

  public Cases result(String result) {
    this.result = result;
    return this;
  }

  /**
   * Get result
   * @return result
  */
//  @ApiModelProperty(required = true, value = "")
  @NotNull


  public String getResult() {
    return result;
  }

  public void setResult(String result) {
    this.result = result;
  }

  public Cases note(String note) {
    this.note = note;
    return this;
  }

  /**
   * Get note
   * @return note
  */
//  @ApiModelProperty(required = true, value = "")
  @NotNull


  public String getNote() {
    return note;
  }

  public void setNote(String note) {
    this.note = note;
  }

  public Cases relatedTasks(List<RelatedTasks> relatedTasks) {
    this.relatedTasks = relatedTasks;
    return this;
  }

  public Cases addRelatedTasksItem(RelatedTasks relatedTasksItem) {
    this.relatedTasks.add(relatedTasksItem);
    return this;
  }

  /**
   * Get relatedTasks
   * @return relatedTasks
  */
//  @ApiModelProperty(required = true, value = "")
  @NotNull

  @Valid

  public List<RelatedTasks> getRelatedTasks() {
    return relatedTasks;
  }

  public void setRelatedTasks(List<RelatedTasks> relatedTasks) {
    this.relatedTasks = relatedTasks;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    Cases inlineResponse200Cases = (Cases) o;
    return Objects.equals(this.uuid, inlineResponse200Cases.uuid) &&
        Objects.equals(this.libraryUuid, inlineResponse200Cases.libraryUuid) &&
        Objects.equals(this.moduleUuid, inlineResponse200Cases.moduleUuid) &&
        Objects.equals(this.path, inlineResponse200Cases.path) &&
        Objects.equals(this.name, inlineResponse200Cases.name) &&
        Objects.equals(this.priority, inlineResponse200Cases.priority) &&
        Objects.equals(this.type, inlineResponse200Cases.type) &&
        Objects.equals(this.assign, inlineResponse200Cases.assign) &&
        Objects.equals(this.desc, inlineResponse200Cases.desc) &&
        Objects.equals(this.createTime, inlineResponse200Cases.createTime) &&
        Objects.equals(this.number, inlineResponse200Cases.number) &&
        Objects.equals(this.condition, inlineResponse200Cases.condition) &&
        Objects.equals(this.steps, inlineResponse200Cases.steps) &&
        Objects.equals(this.namePinyin, inlineResponse200Cases.namePinyin) &&
        Objects.equals(this.planUuid, inlineResponse200Cases.planUuid) &&
        Objects.equals(this.executor, inlineResponse200Cases.executor) &&
        Objects.equals(this.result, inlineResponse200Cases.result) &&
        Objects.equals(this.note, inlineResponse200Cases.note) &&
        Objects.equals(this.relatedTasks, inlineResponse200Cases.relatedTasks);
  }

  @Override
  public int hashCode() {
    return Objects.hash(uuid, libraryUuid, moduleUuid, path, name, priority, type, assign, desc, createTime, number, condition, steps, namePinyin, planUuid, executor, result, note, relatedTasks);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class InlineResponse200Cases {\n");
    
    sb.append("    uuid: ").append(toIndentedString(uuid)).append("\n");
    sb.append("    libraryUuid: ").append(toIndentedString(libraryUuid)).append("\n");
    sb.append("    moduleUuid: ").append(toIndentedString(moduleUuid)).append("\n");
    sb.append("    path: ").append(toIndentedString(path)).append("\n");
    sb.append("    name: ").append(toIndentedString(name)).append("\n");
    sb.append("    priority: ").append(toIndentedString(priority)).append("\n");
    sb.append("    type: ").append(toIndentedString(type)).append("\n");
    sb.append("    assign: ").append(toIndentedString(assign)).append("\n");
    sb.append("    desc: ").append(toIndentedString(desc)).append("\n");
    sb.append("    createTime: ").append(toIndentedString(createTime)).append("\n");
    sb.append("    number: ").append(toIndentedString(number)).append("\n");
    sb.append("    condition: ").append(toIndentedString(condition)).append("\n");
    sb.append("    steps: ").append(toIndentedString(steps)).append("\n");
    sb.append("    namePinyin: ").append(toIndentedString(namePinyin)).append("\n");
    sb.append("    planUuid: ").append(toIndentedString(planUuid)).append("\n");
    sb.append("    executor: ").append(toIndentedString(executor)).append("\n");
    sb.append("    result: ").append(toIndentedString(result)).append("\n");
    sb.append("    note: ").append(toIndentedString(note)).append("\n");
    sb.append("    relatedTasks: ").append(toIndentedString(relatedTasks)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

