package com.yaduo.devopstools.ones.model.test;

import com.fasterxml.jackson.annotation.JsonProperty;

import javax.validation.constraints.NotNull;
import java.util.Objects;

public class RelatedTasks {
  @JsonProperty("uuid")
  private String uuid;

  @JsonProperty("summary")
  private String summary;

  @JsonProperty("number")
  private Integer number;

  @JsonProperty("status_uuid")
  private String statusUuid;

  @JsonProperty("readable")
  private Boolean readable;

  @JsonProperty("project_uuid")
  private String projectUuid;

  public RelatedTasks uuid(String uuid) {
    this.uuid = uuid;
    return this;
  }

  /**
   * Get uuid
   * @return uuid
  */
//  @ApiModelProperty(required = true, value = "")
  @NotNull


  public String getUuid() {
    return uuid;
  }

  public void setUuid(String uuid) {
    this.uuid = uuid;
  }

  public RelatedTasks summary(String summary) {
    this.summary = summary;
    return this;
  }

  /**
   * Get summary
   * @return summary
  */
//  @ApiModelProperty(required = true, value = "")
  @NotNull


  public String getSummary() {
    return summary;
  }

  public void setSummary(String summary) {
    this.summary = summary;
  }

  public RelatedTasks number(Integer number) {
    this.number = number;
    return this;
  }

  /**
   * Get number
   * @return number
  */
//  @ApiModelProperty(required = true, value = "")
  @NotNull


  public Integer getNumber() {
    return number;
  }

  public void setNumber(Integer number) {
    this.number = number;
  }

  public RelatedTasks statusUuid(String statusUuid) {
    this.statusUuid = statusUuid;
    return this;
  }

  /**
   * Get statusUuid
   * @return statusUuid
  */
//  @ApiModelProperty(required = true, value = "")
  @NotNull


  public String getStatusUuid() {
    return statusUuid;
  }

  public void setStatusUuid(String statusUuid) {
    this.statusUuid = statusUuid;
  }

  public RelatedTasks readable(Boolean readable) {
    this.readable = readable;
    return this;
  }

  /**
   * Get readable
   * @return readable
  */
//  @ApiModelProperty(required = true, value = "")
  @NotNull


  public Boolean getReadable() {
    return readable;
  }

  public void setReadable(Boolean readable) {
    this.readable = readable;
  }

  public RelatedTasks projectUuid(String projectUuid) {
    this.projectUuid = projectUuid;
    return this;
  }

  /**
   * Get projectUuid
   * @return projectUuid
  */
//  @ApiModelProperty(required = true, value = "")
  @NotNull


  public String getProjectUuid() {
    return projectUuid;
  }

  public void setProjectUuid(String projectUuid) {
    this.projectUuid = projectUuid;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    RelatedTasks inlineResponse200RelatedTasks = (RelatedTasks) o;
    return Objects.equals(this.uuid, inlineResponse200RelatedTasks.uuid) &&
        Objects.equals(this.summary, inlineResponse200RelatedTasks.summary) &&
        Objects.equals(this.number, inlineResponse200RelatedTasks.number) &&
        Objects.equals(this.statusUuid, inlineResponse200RelatedTasks.statusUuid) &&
        Objects.equals(this.readable, inlineResponse200RelatedTasks.readable) &&
        Objects.equals(this.projectUuid, inlineResponse200RelatedTasks.projectUuid);
  }

  @Override
  public int hashCode() {
    return Objects.hash(uuid, summary, number, statusUuid, readable, projectUuid);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class InlineResponse200RelatedTasks {\n");
    
    sb.append("    uuid: ").append(toIndentedString(uuid)).append("\n");
    sb.append("    summary: ").append(toIndentedString(summary)).append("\n");
    sb.append("    number: ").append(toIndentedString(number)).append("\n");
    sb.append("    statusUuid: ").append(toIndentedString(statusUuid)).append("\n");
    sb.append("    readable: ").append(toIndentedString(readable)).append("\n");
    sb.append("    projectUuid: ").append(toIndentedString(projectUuid)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

