package com.yaduo.devopstools.ones.model.test;

import com.fasterxml.jackson.annotation.JsonProperty;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.List;

/**
 * InlineResponse200
 */

public class TestCaseResponse {
  @JsonProperty("cases")
  @Valid
  private List<Cases> cases = new ArrayList<>();

  public TestCaseResponse cases(List<Cases> cases) {
    this.cases = cases;
    return this;
  }

  public TestCaseResponse addCasesItem(Cases casesItem) {
    this.cases.add(casesItem);
    return this;
  }

  /**
   * Get cases
   * @return cases
  */
//  @ApiModelProperty(required = true, value = "")
  @NotNull

  @Valid

  public List<Cases> getCases() {
    return cases;
  }

  public void setCases(List<Cases> cases) {
    this.cases = cases;
  }




}

