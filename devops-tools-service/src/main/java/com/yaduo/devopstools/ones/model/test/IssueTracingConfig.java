package com.yaduo.devopstools.ones.model.test;

import com.fasterxml.jackson.annotation.JsonProperty;

import javax.validation.constraints.NotNull;
import java.util.Objects;

/**
 * InlineResponse200IssueTracingConfig
 */
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.SpringCodegen", date = "2022-04-21T21:27:40.321239+08:00[Asia/Shanghai]")

public class IssueTracingConfig {
  @JsonProperty("uuid")
  private String uuid;

  @JsonProperty("column_config")
  private String columnConfig;

  @JsonProperty("show_issue_track")
  private Boolean showIssueTrack;

  public IssueTracingConfig uuid(String uuid) {
    this.uuid = uuid;
    return this;
  }

  /**
   * Get uuid
   * @return uuid
  */
//  @ApiModelProperty(required = true, value = "")
  @NotNull


  public String getUuid() {
    return uuid;
  }

  public void setUuid(String uuid) {
    this.uuid = uuid;
  }

  public IssueTracingConfig columnConfig(String columnConfig) {
    this.columnConfig = columnConfig;
    return this;
  }

  /**
   * Get columnConfig
   * @return columnConfig
  */
//  @ApiModelProperty(required = true, value = "")
  @NotNull


  public String getColumnConfig() {
    return columnConfig;
  }

  public void setColumnConfig(String columnConfig) {
    this.columnConfig = columnConfig;
  }

  public IssueTracingConfig showIssueTrack(Boolean showIssueTrack) {
    this.showIssueTrack = showIssueTrack;
    return this;
  }

  /**
   * Get showIssueTrack
   * @return showIssueTrack
  */
//  @ApiModelProperty(required = true, value = "")
  @NotNull


  public Boolean getShowIssueTrack() {
    return showIssueTrack;
  }

  public void setShowIssueTrack(Boolean showIssueTrack) {
    this.showIssueTrack = showIssueTrack;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    IssueTracingConfig inlineResponse200IssueTracingConfig = (IssueTracingConfig) o;
    return Objects.equals(this.uuid, inlineResponse200IssueTracingConfig.uuid) &&
        Objects.equals(this.columnConfig, inlineResponse200IssueTracingConfig.columnConfig) &&
        Objects.equals(this.showIssueTrack, inlineResponse200IssueTracingConfig.showIssueTrack);
  }

  @Override
  public int hashCode() {
    return Objects.hash(uuid, columnConfig, showIssueTrack);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class InlineResponse200IssueTracingConfig {\n");
    
    sb.append("    uuid: ").append(toIndentedString(uuid)).append("\n");
    sb.append("    columnConfig: ").append(toIndentedString(columnConfig)).append("\n");
    sb.append("    showIssueTrack: ").append(toIndentedString(showIssueTrack)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

