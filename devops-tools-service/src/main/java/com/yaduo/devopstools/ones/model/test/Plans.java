package com.yaduo.devopstools.ones.model.test;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * InlineResponse200Plans
 */
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.SpringCodegen", date = "2022-04-21T21:27:40.321239+08:00[Asia/Shanghai]")

public class Plans {
  @JsonProperty("uuid")
  private String uuid;

  @JsonProperty("owner")
  private String owner;

  @JsonProperty("name")
  private String name;

  @JsonProperty("plan_stage")
  private String planStage;

  @JsonProperty("status")
  private String status;

  @JsonProperty("create_time")
  @JSONField(format="unixtime")
  private Date createTime;

  @JsonProperty("members")
  @Valid
  private List<Members> members = new ArrayList<>();

  @JsonProperty("related_project_uuid")
  private String relatedProjectUuid;

  @JsonProperty("related_sprint_uuid")
  private String relatedSprintUuid;

  @JsonProperty("related_issue_type_uuid")
  private String relatedIssueTypeUuid;

  @JsonProperty("assigns")
  @Valid
  private List<String> assigns = new ArrayList<>();

  @JsonProperty("todo_case_count")
  private Integer todoCaseCount;

  @JsonProperty("passed_case_count")
  private Integer passedCaseCount;

  @JsonProperty("failed_case_count")
  private Integer failedCaseCount;

  @JsonProperty("blocked_case_count")
  private Integer blockedCaseCount;

  @JsonProperty("skipped_case_count")
  private Integer skippedCaseCount;

  @JsonProperty("report_uuids")
  @Valid
  private List<String> reportUuids = new ArrayList<>();

  @JsonProperty("field_values")
  @Valid
  private List<String> fieldValues = new ArrayList<>();

  @JsonProperty("issue_tracing_config")
  private IssueTracingConfig issueTracingConfig;

  @JsonProperty("check_points")
  @Valid
  private List<Object> checkPoints = new ArrayList<>();

  public Plans uuid(String uuid) {
    this.uuid = uuid;
    return this;
  }

  /**
   * Get uuid
   * @return uuid
  */
//  @ApiModelProperty(required = true, value = "")
  @NotNull


  public String getUuid() {
    return uuid;
  }

  public void setUuid(String uuid) {
    this.uuid = uuid;
  }

  public Plans owner(String owner) {
    this.owner = owner;
    return this;
  }

  /**
   * Get owner
   * @return owner
  */
//  @ApiModelProperty(required = true, value = "")
  @NotNull


  public String getOwner() {
    return owner;
  }

  public void setOwner(String owner) {
    this.owner = owner;
  }

  public Plans name(String name) {
    this.name = name;
    return this;
  }

  /**
   * Get name
   * @return name
  */
//  @ApiModelProperty(required = true, value = "")
  @NotNull


  public String getName() {
    return name;
  }

  public void setName(String name) {
    this.name = name;
  }

  public Plans planStage(String planStage) {
    this.planStage = planStage;
    return this;
  }

  /**
   * Get planStage
   * @return planStage
  */
//  @ApiModelProperty(required = true, value = "")
  @NotNull


  public String getPlanStage() {
    return planStage;
  }

  public void setPlanStage(String planStage) {
    this.planStage = planStage;
  }

  public Plans status(String status) {
    this.status = status;
    return this;
  }

  /**
   * Get status
   * @return status
  */
//  @ApiModelProperty(required = true, value = "")
  @NotNull


  public String getStatus() {
    return status;
  }

  public void setStatus(String status) {
    this.status = status;
  }

  public Plans createTime(Date createTime) {
    this.createTime = createTime;
    return this;
  }

  /**
   * Get createTime
   * @return createTime
  */
//  @ApiModelProperty(required = true, value = "")
  @NotNull
  public Date getCreateTime() {
    return createTime;
  }

  public void setCreateTime(Date createTime) {
    this.createTime = createTime;
  }

  public Plans members(List<Members> members) {
    this.members = members;
    return this;
  }

  public Plans addMembersItem(Members membersItem) {
    this.members.add(membersItem);
    return this;
  }

  /**
   * Get members
   * @return members
  */
//  @ApiModelProperty(required = true, value = "")
  @NotNull

  @Valid

  public List<Members> getMembers() {
    return members;
  }

  public void setMembers(List<Members> members) {
    this.members = members;
  }

  public Plans relatedProjectUuid(String relatedProjectUuid) {
    this.relatedProjectUuid = relatedProjectUuid;
    return this;
  }

  /**
   * Get relatedProjectUuid
   * @return relatedProjectUuid
  */
//  @ApiModelProperty(required = true, value = "")
  @NotNull


  public String getRelatedProjectUuid() {
    return relatedProjectUuid;
  }

  public void setRelatedProjectUuid(String relatedProjectUuid) {
    this.relatedProjectUuid = relatedProjectUuid;
  }

  public Plans relatedSprintUuid(String relatedSprintUuid) {
    this.relatedSprintUuid = relatedSprintUuid;
    return this;
  }

  /**
   * Get relatedSprintUuid
   * @return relatedSprintUuid
  */
//  @ApiModelProperty(required = true, value = "")
  @NotNull


  public String getRelatedSprintUuid() {
    return relatedSprintUuid;
  }

  public void setRelatedSprintUuid(String relatedSprintUuid) {
    this.relatedSprintUuid = relatedSprintUuid;
  }

  public Plans relatedIssueTypeUuid(String relatedIssueTypeUuid) {
    this.relatedIssueTypeUuid = relatedIssueTypeUuid;
    return this;
  }

  /**
   * Get relatedIssueTypeUuid
   * @return relatedIssueTypeUuid
  */
//  @ApiModelProperty(required = true, value = "")
  @NotNull


  public String getRelatedIssueTypeUuid() {
    return relatedIssueTypeUuid;
  }

  public void setRelatedIssueTypeUuid(String relatedIssueTypeUuid) {
    this.relatedIssueTypeUuid = relatedIssueTypeUuid;
  }

  public Plans assigns(List<String> assigns) {
    this.assigns = assigns;
    return this;
  }

  public Plans addAssignsItem(String assignsItem) {
    this.assigns.add(assignsItem);
    return this;
  }

  /**
   * Get assigns
   * @return assigns
  */
//  @ApiModelProperty(required = true, value = "")
  @NotNull


  public List<String> getAssigns() {
    return assigns;
  }

  public void setAssigns(List<String> assigns) {
    this.assigns = assigns;
  }

  public Plans todoCaseCount(Integer todoCaseCount) {
    this.todoCaseCount = todoCaseCount;
    return this;
  }

  /**
   * Get todoCaseCount
   * @return todoCaseCount
  */
//  @ApiModelProperty(required = true, value = "")
  @NotNull


  public Integer getTodoCaseCount() {
    return todoCaseCount;
  }

  public void setTodoCaseCount(Integer todoCaseCount) {
    this.todoCaseCount = todoCaseCount;
  }

  public Plans passedCaseCount(Integer passedCaseCount) {
    this.passedCaseCount = passedCaseCount;
    return this;
  }

  /**
   * Get passedCaseCount
   * @return passedCaseCount
  */
//  @ApiModelProperty(required = true, value = "")
  @NotNull


  public Integer getPassedCaseCount() {
    return passedCaseCount;
  }

  public void setPassedCaseCount(Integer passedCaseCount) {
    this.passedCaseCount = passedCaseCount;
  }

  public Plans failedCaseCount(Integer failedCaseCount) {
    this.failedCaseCount = failedCaseCount;
    return this;
  }

  /**
   * Get failedCaseCount
   * @return failedCaseCount
  */
//  @ApiModelProperty(required = true, value = "")
  @NotNull


  public Integer getFailedCaseCount() {
    return failedCaseCount;
  }

  public void setFailedCaseCount(Integer failedCaseCount) {
    this.failedCaseCount = failedCaseCount;
  }

  public Plans blockedCaseCount(Integer blockedCaseCount) {
    this.blockedCaseCount = blockedCaseCount;
    return this;
  }

  /**
   * Get blockedCaseCount
   * @return blockedCaseCount
  */
//  @ApiModelProperty(required = true, value = "")
  @NotNull


  public Integer getBlockedCaseCount() {
    return blockedCaseCount;
  }

  public void setBlockedCaseCount(Integer blockedCaseCount) {
    this.blockedCaseCount = blockedCaseCount;
  }

  public Plans skippedCaseCount(Integer skippedCaseCount) {
    this.skippedCaseCount = skippedCaseCount;
    return this;
  }

  /**
   * Get skippedCaseCount
   * @return skippedCaseCount
  */
//  @ApiModelProperty(required = true, value = "")
  @NotNull


  public Integer getSkippedCaseCount() {
    return skippedCaseCount;
  }

  public void setSkippedCaseCount(Integer skippedCaseCount) {
    this.skippedCaseCount = skippedCaseCount;
  }

  public Plans reportUuids(List<String> reportUuids) {
    this.reportUuids = reportUuids;
    return this;
  }

  public Plans addReportUuidsItem(String reportUuidsItem) {
    this.reportUuids.add(reportUuidsItem);
    return this;
  }

  /**
   * Get reportUuids
   * @return reportUuids
  */
//  @ApiModelProperty(required = true, value = "")
  @NotNull


  public List<String> getReportUuids() {
    return reportUuids;
  }

  public void setReportUuids(List<String> reportUuids) {
    this.reportUuids = reportUuids;
  }

  public Plans fieldValues(List<String> fieldValues) {
    this.fieldValues = fieldValues;
    return this;
  }

  public Plans addFieldValuesItem(String fieldValuesItem) {
    this.fieldValues.add(fieldValuesItem);
    return this;
  }

  /**
   * Get fieldValues
   * @return fieldValues
  */
//  @ApiModelProperty(required = true, value = "")
  @NotNull


  public List<String> getFieldValues() {
    return fieldValues;
  }

  public void setFieldValues(List<String> fieldValues) {
    this.fieldValues = fieldValues;
  }

  public Plans issueTracingConfig(IssueTracingConfig issueTracingConfig) {
    this.issueTracingConfig = issueTracingConfig;
    return this;
  }

  /**
   * Get issueTracingConfig
   * @return issueTracingConfig
  */
//  @ApiModelProperty(required = true, value = "")
  @NotNull

  @Valid

  public IssueTracingConfig getIssueTracingConfig() {
    return issueTracingConfig;
  }

  public void setIssueTracingConfig(IssueTracingConfig issueTracingConfig) {
    this.issueTracingConfig = issueTracingConfig;
  }

  public Plans checkPoints(List<Object> checkPoints) {
    this.checkPoints = checkPoints;
    return this;
  }

  public Plans addCheckPointsItem(Object checkPointsItem) {
    this.checkPoints.add(checkPointsItem);
    return this;
  }

  /**
   * Get checkPoints
   * @return checkPoints
  */
//  @ApiModelProperty(required = true, value = "")
  @NotNull


  public List<Object> getCheckPoints() {
    return checkPoints;
  }

  public void setCheckPoints(List<Object> checkPoints) {
    this.checkPoints = checkPoints;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    Plans inlineResponse200Plans = (Plans) o;
    return Objects.equals(this.uuid, inlineResponse200Plans.uuid) &&
        Objects.equals(this.owner, inlineResponse200Plans.owner) &&
        Objects.equals(this.name, inlineResponse200Plans.name) &&
        Objects.equals(this.planStage, inlineResponse200Plans.planStage) &&
        Objects.equals(this.status, inlineResponse200Plans.status) &&
        Objects.equals(this.createTime, inlineResponse200Plans.createTime) &&
        Objects.equals(this.members, inlineResponse200Plans.members) &&
        Objects.equals(this.relatedProjectUuid, inlineResponse200Plans.relatedProjectUuid) &&
        Objects.equals(this.relatedSprintUuid, inlineResponse200Plans.relatedSprintUuid) &&
        Objects.equals(this.relatedIssueTypeUuid, inlineResponse200Plans.relatedIssueTypeUuid) &&
        Objects.equals(this.assigns, inlineResponse200Plans.assigns) &&
        Objects.equals(this.todoCaseCount, inlineResponse200Plans.todoCaseCount) &&
        Objects.equals(this.passedCaseCount, inlineResponse200Plans.passedCaseCount) &&
        Objects.equals(this.failedCaseCount, inlineResponse200Plans.failedCaseCount) &&
        Objects.equals(this.blockedCaseCount, inlineResponse200Plans.blockedCaseCount) &&
        Objects.equals(this.skippedCaseCount, inlineResponse200Plans.skippedCaseCount) &&
        Objects.equals(this.reportUuids, inlineResponse200Plans.reportUuids) &&
        Objects.equals(this.fieldValues, inlineResponse200Plans.fieldValues) &&
        Objects.equals(this.issueTracingConfig, inlineResponse200Plans.issueTracingConfig) &&
        Objects.equals(this.checkPoints, inlineResponse200Plans.checkPoints);
  }

  @Override
  public int hashCode() {
    return Objects.hash(uuid, owner, name, planStage, status, createTime, members, relatedProjectUuid, relatedSprintUuid, relatedIssueTypeUuid, assigns, todoCaseCount, passedCaseCount, failedCaseCount, blockedCaseCount, skippedCaseCount, reportUuids, fieldValues, issueTracingConfig, checkPoints);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class InlineResponse200Plans {\n");
    
    sb.append("    uuid: ").append(toIndentedString(uuid)).append("\n");
    sb.append("    owner: ").append(toIndentedString(owner)).append("\n");
    sb.append("    name: ").append(toIndentedString(name)).append("\n");
    sb.append("    planStage: ").append(toIndentedString(planStage)).append("\n");
    sb.append("    status: ").append(toIndentedString(status)).append("\n");
    sb.append("    createTime: ").append(toIndentedString(createTime)).append("\n");
    sb.append("    members: ").append(toIndentedString(members)).append("\n");
    sb.append("    relatedProjectUuid: ").append(toIndentedString(relatedProjectUuid)).append("\n");
    sb.append("    relatedSprintUuid: ").append(toIndentedString(relatedSprintUuid)).append("\n");
    sb.append("    relatedIssueTypeUuid: ").append(toIndentedString(relatedIssueTypeUuid)).append("\n");
    sb.append("    assigns: ").append(toIndentedString(assigns)).append("\n");
    sb.append("    todoCaseCount: ").append(toIndentedString(todoCaseCount)).append("\n");
    sb.append("    passedCaseCount: ").append(toIndentedString(passedCaseCount)).append("\n");
    sb.append("    failedCaseCount: ").append(toIndentedString(failedCaseCount)).append("\n");
    sb.append("    blockedCaseCount: ").append(toIndentedString(blockedCaseCount)).append("\n");
    sb.append("    skippedCaseCount: ").append(toIndentedString(skippedCaseCount)).append("\n");
    sb.append("    reportUuids: ").append(toIndentedString(reportUuids)).append("\n");
    sb.append("    fieldValues: ").append(toIndentedString(fieldValues)).append("\n");
    sb.append("    issueTracingConfig: ").append(toIndentedString(issueTracingConfig)).append("\n");
    sb.append("    checkPoints: ").append(toIndentedString(checkPoints)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

