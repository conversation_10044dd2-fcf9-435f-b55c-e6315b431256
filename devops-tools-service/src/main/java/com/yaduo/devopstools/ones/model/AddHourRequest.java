package com.yaduo.devopstools.ones.model;

import java.io.Serializable;

public class AddHourRequest implements Serializable {
    private Variables variables;

    private String query;

    public Variables getVariables() {
        return this.variables;
    }

    public void setVariables(Variables variables) {
        this.variables = variables;
    }

    public String getQuery() {
        return this.query;
    }

    public void setQuery(String query) {
        this.query = query;
    }

    public static class Variables implements Serializable {
        private String mode;

        private String owner;

        private Integer start_time;

        private Integer hours;

        private String task;

        private String description;

        private String type;

        public String getMode() {
            return this.mode;
        }

        public void setMode(String mode) {
            this.mode = mode;
        }

        public String getOwner() {
            return this.owner;
        }

        public void setOwner(String owner) {
            this.owner = owner;
        }

        public Integer getStart_time() {
            return this.start_time;
        }

        public void setStart_time(Integer start_time) {
            this.start_time = start_time;
        }

        public Integer getHours() {
            return this.hours;
        }

        public void setHours(Integer hours) {
            this.hours = hours;
        }

        public String getTask() {
            return this.task;
        }

        public void setTask(String task) {
            this.task = task;
        }

        public String getDescription() {
            return this.description;
        }

        public void setDescription(String description) {
            this.description = description;
        }

        public String getType() {
            return this.type;
        }

        public void setType(String type) {
            this.type = type;
        }
    }
}
