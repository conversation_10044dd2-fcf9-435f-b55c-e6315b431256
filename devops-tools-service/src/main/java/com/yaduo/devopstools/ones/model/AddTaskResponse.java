package com.yaduo.devopstools.ones.model;

import com.fasterxml.jackson.annotation.JsonProperty;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * InlineResponse2002
 */
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.SpringCodegen", date = "2022-04-17T00:46:55.176358+08:00[Asia/Shanghai]")
public class AddTaskResponse {
  @JsonProperty("tasks")
  @Valid
  private List<Task> tasks = new ArrayList<>();
  @JsonProperty("bad_tasks")
  @Valid
  private List<String> badTasks = new ArrayList<>();

  public AddTaskResponse tasks(List<Task> tasks) {
    this.tasks = tasks;
    return this;
  }

  public AddTaskResponse addTasksItem(Task tasksItem) {
    this.tasks.add(tasksItem);
    return this;
  }

  /**
   * Get tasks
   * @return tasks
  */
  @NotNull

  @Valid

  public List<Task> getTasks() {
    return tasks;
  }

  public void setTasks(List<Task> tasks) {
    this.tasks = tasks;
  }

  public AddTaskResponse badTasks(List<String> badTasks) {
    this.badTasks = badTasks;
    return this;
  }

  public AddTaskResponse addBadTasksItem(String badTasksItem) {
    this.badTasks.add(badTasksItem);
    return this;
  }

  /**
   * Get badTasks
   * @return badTasks
  */
//  @ApiModelProperty(required = true, value = "")
  @NotNull


  public List<String> getBadTasks() {
    return badTasks;
  }

  public void setBadTasks(List<String> badTasks) {
    this.badTasks = badTasks;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    AddTaskResponse inlineResponse2002 = (AddTaskResponse) o;
    return Objects.equals(this.tasks, inlineResponse2002.tasks) &&
        Objects.equals(this.badTasks, inlineResponse2002.badTasks);
  }

  @Override
  public int hashCode() {
    return Objects.hash(tasks, badTasks);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class InlineResponse2002 {\n");
    
    sb.append("    tasks: ").append(toIndentedString(tasks)).append("\n");
    sb.append("    badTasks: ").append(toIndentedString(badTasks)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

