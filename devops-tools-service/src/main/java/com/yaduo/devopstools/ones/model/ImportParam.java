package com.yaduo.devopstools.ones.model;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 导入参数
 *
 * <AUTHOR>
 * @date 2022-04-17 17:59
 */

@Data
public class ImportParam {
    @ExcelProperty("父工作项ID")
    private String parentId;

    @ExcelProperty("标题")
    private String summary;


    @ExcelProperty("描述")
    private String desc;


    @ExcelProperty("负责人")
    private String assign;


    @ExcelProperty("关注者")
    private String watchers;


//    @ExcelProperty("预估工时")
    @ExcelIgnore
    private Integer manHours = 8;


    @ExcelProperty("计划开始日期")
    private Date planStartTime;


    @ExcelProperty("计划完成日期")
    private Date planEndTime;

//
//    @ExcelProperty("状态")
//    private String status;
//
//
//    @ExcelProperty("工作项类型")
//    private String taskType;
//
//
//    @ExcelProperty("优先级")
//    private String priority;


}
