package com.yaduo.devopstools.ones.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * InlineResponse2002Tasks
 */
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.SpringCodegen", date = "2022-04-17T00:46:55.176358+08:00[Asia/Shanghai]")

public class Task {
  @JsonProperty("uuid")
  private String uuid;

  @JsonProperty("owner")
  private String owner;

  @JsonProperty("assign")
  private String assign;

  @JsonProperty("tags")
  private String tags;

  @JsonProperty("sprint_uuid")
  private String sprintUuid;

  @JsonProperty("project_uuid")
  private String projectUuid;

  @JsonProperty("issue_type_scope_uuid")
  private String issueTypeScopeUuid;

  @JsonProperty("issue_type_uuid")
  private String issueTypeUuid;

  @JsonProperty("sub_issue_type_uuid")
  private String subIssueTypeUuid;

  @JsonProperty("status_uuid")
  private String statusUuid;

  @JsonProperty("create_time")
  @JSONField(format="unixtime")
  private Date createTime;

  @JsonProperty("status")
  private Integer status;

  @JsonProperty("summary")
  private String summary;

  @JsonProperty("desc")
  private String desc;

  @JsonProperty("desc_rich")
  private String descRich;

  @JsonProperty("parent_uuid")
  private String parentUuid;

  @JsonProperty("position")
  private Integer position;

  @JsonProperty("number")
  private Integer number;

  @JsonProperty("priority")
  private String priority;

  @JsonProperty("assess_manhour")
  private Integer assessManhour;

  @JsonProperty("total_manhour")
  private Integer totalManhour;

  @JsonProperty("remaining_manhour")
  private Integer remainingManhour;

  @JsonProperty("estimate_variance")
  private String estimateVariance = null;

  @JsonProperty("time_progress")
  private Integer timeProgress;

  @JsonProperty("attribute")
  private String attribute = null;

  @JsonProperty("watchers")
  @Valid
  private List<String> watchers = null;

  @JsonProperty("field_values")
  @Valid
  private List<FieldValues> fieldValues;

  @JsonProperty("related_tasks")
  @Valid
  private List<String> relatedTasks = null;

  @JsonProperty("links")
  @Valid
  private List<String> links = null;

  @JsonProperty("subtasks")
  @Valid
  private List<String> subtasks = null;

  @JsonProperty("path")
  private String path;

  @JsonProperty("code_commits")
  @Valid
  private List<String> codeCommits = null;

  @JsonProperty("related_task_count")
  @Valid
  private List<Integer> relatedTaskCount = null;

  @JsonProperty("visible_subtask_count")
  private Integer visibleSubtaskCount;

  @JsonProperty("subtask_count")
  @Valid
  private List<Integer> subtaskCount = null;

  @JsonProperty("discussion_count")
  private Integer discussionCount;

  @JsonProperty("attachment_count")
  private Integer attachmentCount;

  @JsonProperty("related_cases")
  @Valid
  private List<String> relatedCases = null;

  @JsonProperty("related_plan_cases")
  @Valid
  private List<String> relatedPlanCases = null;

  @JsonProperty("related_testcase_plans")
  @Valid
  private List<String> relatedTestcasePlans = null;

  @JsonProperty("related_wiki_pages")
  @Valid
  private List<String> relatedWikiPages = null;

  @JsonProperty("related_activities")
  @Valid
  private List<String> relatedActivities = null;

  @JsonProperty("related_devops_commits")
  @Valid
  private List<String> relatedDevopsCommits = null;

  @JsonProperty("publish_version")
  private String publishVersion = null;

  @JsonProperty("publish_content")
  @Valid
  private List<String> publishContent = null;

  @JsonProperty("publish_content_done_count")
  private Integer publishContentDoneCount;

  @JsonProperty("publish_content_count")
  private Integer publishContentCount;

  @JsonProperty("manhours")
  @Valid
  private List<String> manhours = null;

  @JsonProperty("product_uuids")
  @Valid
  private List<String> productUuids = null;

  @JsonProperty("SkipCheckFieldPermissions")
  private Boolean skipCheckFieldPermissions;

  public Task uuid(String uuid) {
    this.uuid = uuid;
    return this;
  }

  /**
   * Get uuid
   * @return uuid
  */
//  @ApiModelProperty(value = "")


  public String getUuid() {
    return uuid;
  }

  public void setUuid(String uuid) {
    this.uuid = uuid;
  }

  public Task owner(String owner) {
    this.owner = owner;
    return this;
  }

  /**
   * Get owner
   * @return owner
  */
//  @ApiModelProperty(value = "")


  public String getOwner() {
    return owner;
  }

  public void setOwner(String owner) {
    this.owner = owner;
  }

  public Task assign(String assign) {
    this.assign = assign;
    return this;
  }

  /**
   * Get assign
   * @return assign
  */
//  @ApiModelProperty(value = "")


  public String getAssign() {
    return assign;
  }

  public void setAssign(String assign) {
    this.assign = assign;
  }

  public Task tags(String tags) {
    this.tags = tags;
    return this;
  }

  /**
   * Get tags
   * @return tags
  */
//  @ApiModelProperty(value = "")


  public String getTags() {
    return tags;
  }

  public void setTags(String tags) {
    this.tags = tags;
  }

  public Task sprintUuid(String sprintUuid) {
    this.sprintUuid = sprintUuid;
    return this;
  }

  /**
   * Get sprintUuid
   * @return sprintUuid
  */
//  @ApiModelProperty(value = "")

  @Valid

  public String getSprintUuid() {
    return sprintUuid;
  }

  public void setSprintUuid(String sprintUuid) {
    this.sprintUuid = sprintUuid;
  }

  public Task projectUuid(String projectUuid) {
    this.projectUuid = projectUuid;
    return this;
  }

  /**
   * Get projectUuid
   * @return projectUuid
  */
//  @ApiModelProperty(value = "")


  public String getProjectUuid() {
    return projectUuid;
  }

  public void setProjectUuid(String projectUuid) {
    this.projectUuid = projectUuid;
  }

  public Task issueTypeScopeUuid(String issueTypeScopeUuid) {
    this.issueTypeScopeUuid = issueTypeScopeUuid;
    return this;
  }

  /**
   * Get issueTypeScopeUuid
   * @return issueTypeScopeUuid
  */
//  @ApiModelProperty(value = "")


  public String getIssueTypeScopeUuid() {
    return issueTypeScopeUuid;
  }

  public void setIssueTypeScopeUuid(String issueTypeScopeUuid) {
    this.issueTypeScopeUuid = issueTypeScopeUuid;
  }

  public Task issueTypeUuid(String issueTypeUuid) {
    this.issueTypeUuid = issueTypeUuid;
    return this;
  }

  /**
   * Get issueTypeUuid
   * @return issueTypeUuid
  */
//  @ApiModelProperty(value = "")


  public String getIssueTypeUuid() {
    return issueTypeUuid;
  }

  public void setIssueTypeUuid(String issueTypeUuid) {
    this.issueTypeUuid = issueTypeUuid;
  }

  public Task subIssueTypeUuid(String subIssueTypeUuid) {
    this.subIssueTypeUuid = subIssueTypeUuid;
    return this;
  }

  /**
   * Get subIssueTypeUuid
   * @return subIssueTypeUuid
  */
//  @ApiModelProperty(value = "")
  public String getSubIssueTypeUuid() {
    return subIssueTypeUuid;
  }

  public void setSubIssueTypeUuid(String subIssueTypeUuid) {
    this.subIssueTypeUuid = subIssueTypeUuid;
  }

  public Task statusUuid(String statusUuid) {
    this.statusUuid = statusUuid;
    return this;
  }

  /**
   * Get statusUuid
   * @return statusUuid
  */
//  @ApiModelProperty(value = "")
  public String getStatusUuid() {
    return statusUuid;
  }

  public void setStatusUuid(String statusUuid) {
    this.statusUuid = statusUuid;
  }

  public Task createTime(Date createTime) {
    this.createTime = createTime;
    return this;
  }

  /**
   * Get createTime
   * @return createTime
  */
//  @ApiModelProperty(value = "")
  public Date getCreateTime() {
    return createTime;
  }

  public void setCreateTime(Date createTime) {
    this.createTime = createTime;
  }

  public Task status(Integer status) {
    this.status = status;
    return this;
  }

  /**
   * Get status
   * @return status
  */
//  @ApiModelProperty(value = "")
  public Integer getStatus() {
    return status;
  }

  public void setStatus(Integer status) {
    this.status = status;
  }

  public Task summary(String summary) {
    this.summary = summary;
    return this;
  }

  /**
   * Get summary
   * @return summary
  */
//  @ApiModelProperty(value = "")


  public String getSummary() {
    return summary;
  }

  public void setSummary(String summary) {
    this.summary = summary;
  }

  public Task desc(String desc) {
    this.desc = desc;
    return this;
  }

  /**
   * Get desc
   * @return desc
  */
//  @ApiModelProperty(value = "")


  public String getDesc() {
    return desc;
  }

  public void setDesc(String desc) {
    this.desc = desc;
  }

  public Task descRich(String descRich) {
    this.descRich = descRich;
    return this;
  }

  /**
   * Get descRich
   * @return descRich
  */
//  @ApiModelProperty(value = "")


  public String getDescRich() {
    return descRich;
  }

  public void setDescRich(String descRich) {
    this.descRich = descRich;
  }

  public Task parentUuid(String parentUuid) {
    this.parentUuid = parentUuid;
    return this;
  }

  /**
   * Get parentUuid
   * @return parentUuid
  */
//  @ApiModelProperty(value = "")


  public String getParentUuid() {
    return parentUuid;
  }

  public void setParentUuid(String parentUuid) {
    this.parentUuid = parentUuid;
  }

  public Task position(Integer position) {
    this.position = position;
    return this;
  }

  /**
   * Get position
   * @return position
  */
//  @ApiModelProperty(value = "")


  public Integer getPosition() {
    return position;
  }

  public void setPosition(Integer position) {
    this.position = position;
  }

  public Task number(Integer number) {
    this.number = number;
    return this;
  }

  /**
   * Get number
   * @return number
  */
//  @ApiModelProperty(value = "")


  public Integer getNumber() {
    return number;
  }

  public void setNumber(Integer number) {
    this.number = number;
  }

  public Task priority(String priority) {
    this.priority = priority;
    return this;
  }

  /**
   * Get priority
   * @return priority
  */
//  @ApiModelProperty(value = "")


  public String getPriority() {
    return priority;
  }

  public void setPriority(String priority) {
    this.priority = priority;
  }

  public Task assessManhour(Integer assessManhour) {
    this.assessManhour = assessManhour;
    return this;
  }

  /**
   * Get assessManhour
   * @return assessManhour
  */
//  @ApiModelProperty(value = "")


  public Integer getAssessManhour() {
    return assessManhour;
  }

  public void setAssessManhour(Integer assessManhour) {
    this.assessManhour = assessManhour;
  }

  public Task totalManhour(Integer totalManhour) {
    this.totalManhour = totalManhour;
    return this;
  }

  /**
   * Get totalManhour
   * @return totalManhour
  */
//  @ApiModelProperty(value = "")


  public Integer getTotalManhour() {
    return totalManhour;
  }

  public void setTotalManhour(Integer totalManhour) {
    this.totalManhour = totalManhour;
  }

  public Task remainingManhour(Integer remainingManhour) {
    this.remainingManhour = remainingManhour;
    return this;
  }

  /**
   * Get remainingManhour
   * @return remainingManhour
  */
//  @ApiModelProperty(value = "")


  public Integer getRemainingManhour() {
    return remainingManhour;
  }

  public void setRemainingManhour(Integer remainingManhour) {
    this.remainingManhour = remainingManhour;
  }

  public Task estimateVariance(String estimateVariance) {
    this.estimateVariance = estimateVariance;
    return this;
  }

  /**
   * Get estimateVariance
   * @return estimateVariance
  */
//  @ApiModelProperty(value = "")

  @Valid

  public String getEstimateVariance() {
    return estimateVariance;
  }

  public void setEstimateVariance(String estimateVariance) {
    this.estimateVariance = estimateVariance;
  }

  public Task timeProgress(Integer timeProgress) {
    this.timeProgress = timeProgress;
    return this;
  }

  /**
   * Get timeProgress
   * @return timeProgress
  */
//  @ApiModelProperty(value = "")


  public Integer getTimeProgress() {
    return timeProgress;
  }

  public void setTimeProgress(Integer timeProgress) {
    this.timeProgress = timeProgress;
  }

  public Task attribute(String attribute) {
    this.attribute = attribute;
    return this;
  }

  /**
   * Get attribute
   * @return attribute
  */
//  @ApiModelProperty(value = "")

  @Valid

  public String getAttribute() {
    return attribute;
  }

  public void setAttribute(String attribute) {
    this.attribute = attribute;
  }

  public Task watchers(List<String> watchers) {
    this.watchers = watchers;
    return this;
  }

  public Task addWatchersItem(String watchersItem) {
    if (this.watchers == null) {
      this.watchers = new ArrayList<>();
    }
    this.watchers.add(watchersItem);
    return this;
  }

  /**
   * Get watchers
   * @return watchers
  */
//  @ApiModelProperty(value = "")


  public List<String> getWatchers() {
    return watchers;
  }

  public void setWatchers(List<String> watchers) {
    this.watchers = watchers;
  }

  public Task fieldValues(List<FieldValues> fieldValues) {
    this.fieldValues = fieldValues;
    return this;
  }

  public Task addFieldValuesItem(FieldValues fieldValuesItem) {
    if (this.fieldValues == null) {
      this.fieldValues = new ArrayList<>();
    }
    this.fieldValues.add(fieldValuesItem);
    return this;
  }

  /**
   * Get fieldValues
   * @return fieldValues
  */
//  @ApiModelProperty(value = "")

  @Valid

  public List<FieldValues> getFieldValues() {
    return fieldValues;
  }

  public void setFieldValues(List<FieldValues> fieldValues) {
    this.fieldValues = fieldValues;
  }


  public Task relatedTasks(List<String> relatedTasks) {
    this.relatedTasks = relatedTasks;
    return this;
  }

  public Task addRelatedTasksItem(String relatedTasksItem) {
    if (this.relatedTasks == null) {
      this.relatedTasks = new ArrayList<>();
    }
    this.relatedTasks.add(relatedTasksItem);
    return this;
  }

  /**
   * Get relatedTasks
   * @return relatedTasks
  */
//  @ApiModelProperty(value = "")


  public List<String> getRelatedTasks() {
    return relatedTasks;
  }

  public void setRelatedTasks(List<String> relatedTasks) {
    this.relatedTasks = relatedTasks;
  }

  public Task links(List<String> links) {
    this.links = links;
    return this;
  }

  public Task addLinksItem(String linksItem) {
    if (this.links == null) {
      this.links = new ArrayList<>();
    }
    this.links.add(linksItem);
    return this;
  }

  /**
   * Get links
   * @return links
  */
//  @ApiModelProperty(value = "")


  public List<String> getLinks() {
    return links;
  }

  public void setLinks(List<String> links) {
    this.links = links;
  }

  public Task subtasks(List<String> subtasks) {
    this.subtasks = subtasks;
    return this;
  }

  public Task addSubtasksItem(String subtasksItem) {
    if (this.subtasks == null) {
      this.subtasks = new ArrayList<>();
    }
    this.subtasks.add(subtasksItem);
    return this;
  }

  /**
   * Get subtasks
   * @return subtasks
  */
//  @ApiModelProperty(value = "")


  public List<String> getSubtasks() {
    return subtasks;
  }

  public void setSubtasks(List<String> subtasks) {
    this.subtasks = subtasks;
  }

  public Task path(String path) {
    this.path = path;
    return this;
  }

  /**
   * Get path
   * @return path
  */
//  @ApiModelProperty(value = "")


  public String getPath() {
    return path;
  }

  public void setPath(String path) {
    this.path = path;
  }

  public Task codeCommits(List<String> codeCommits) {
    this.codeCommits = codeCommits;
    return this;
  }

  public Task addCodeCommitsItem(String codeCommitsItem) {
    if (this.codeCommits == null) {
      this.codeCommits = new ArrayList<>();
    }
    this.codeCommits.add(codeCommitsItem);
    return this;
  }

  /**
   * Get codeCommits
   * @return codeCommits
  */
//  @ApiModelProperty(value = "")


  public List<String> getCodeCommits() {
    return codeCommits;
  }

  public void setCodeCommits(List<String> codeCommits) {
    this.codeCommits = codeCommits;
  }

  public Task relatedTaskCount(List<Integer> relatedTaskCount) {
    this.relatedTaskCount = relatedTaskCount;
    return this;
  }

  public Task addRelatedTaskCountItem(Integer relatedTaskCountItem) {
    if (this.relatedTaskCount == null) {
      this.relatedTaskCount = new ArrayList<>();
    }
    this.relatedTaskCount.add(relatedTaskCountItem);
    return this;
  }

  /**
   * Get relatedTaskCount
   * @return relatedTaskCount
  */
//  @ApiModelProperty(value = "")


  public List<Integer> getRelatedTaskCount() {
    return relatedTaskCount;
  }

  public void setRelatedTaskCount(List<Integer> relatedTaskCount) {
    this.relatedTaskCount = relatedTaskCount;
  }

  public Task visibleSubtaskCount(Integer visibleSubtaskCount) {
    this.visibleSubtaskCount = visibleSubtaskCount;
    return this;
  }

  /**
   * Get visibleSubtaskCount
   * @return visibleSubtaskCount
  */
//  @ApiModelProperty(value = "")


  public Integer getVisibleSubtaskCount() {
    return visibleSubtaskCount;
  }

  public void setVisibleSubtaskCount(Integer visibleSubtaskCount) {
    this.visibleSubtaskCount = visibleSubtaskCount;
  }

  public Task subtaskCount(List<Integer> subtaskCount) {
    this.subtaskCount = subtaskCount;
    return this;
  }

  public Task addSubtaskCountItem(Integer subtaskCountItem) {
    if (this.subtaskCount == null) {
      this.subtaskCount = new ArrayList<>();
    }
    this.subtaskCount.add(subtaskCountItem);
    return this;
  }

  /**
   * Get subtaskCount
   * @return subtaskCount
  */
//  @ApiModelProperty(value = "")


  public List<Integer> getSubtaskCount() {
    return subtaskCount;
  }

  public void setSubtaskCount(List<Integer> subtaskCount) {
    this.subtaskCount = subtaskCount;
  }

  public Task discussionCount(Integer discussionCount) {
    this.discussionCount = discussionCount;
    return this;
  }

  /**
   * Get discussionCount
   * @return discussionCount
  */
//  @ApiModelProperty(value = "")


  public Integer getDiscussionCount() {
    return discussionCount;
  }

  public void setDiscussionCount(Integer discussionCount) {
    this.discussionCount = discussionCount;
  }

  public Task attachmentCount(Integer attachmentCount) {
    this.attachmentCount = attachmentCount;
    return this;
  }

  /**
   * Get attachmentCount
   * @return attachmentCount
  */
//  @ApiModelProperty(value = "")


  public Integer getAttachmentCount() {
    return attachmentCount;
  }

  public void setAttachmentCount(Integer attachmentCount) {
    this.attachmentCount = attachmentCount;
  }

  public Task relatedCases(List<String> relatedCases) {
    this.relatedCases = relatedCases;
    return this;
  }

  public Task addRelatedCasesItem(String relatedCasesItem) {
    if (this.relatedCases == null) {
      this.relatedCases = new ArrayList<>();
    }
    this.relatedCases.add(relatedCasesItem);
    return this;
  }

  /**
   * Get relatedCases
   * @return relatedCases
  */
//  @ApiModelProperty(value = "")


  public List<String> getRelatedCases() {
    return relatedCases;
  }

  public void setRelatedCases(List<String> relatedCases) {
    this.relatedCases = relatedCases;
  }

  public Task relatedPlanCases(List<String> relatedPlanCases) {
    this.relatedPlanCases = relatedPlanCases;
    return this;
  }

  public Task addRelatedPlanCasesItem(String relatedPlanCasesItem) {
    if (this.relatedPlanCases == null) {
      this.relatedPlanCases = new ArrayList<>();
    }
    this.relatedPlanCases.add(relatedPlanCasesItem);
    return this;
  }

  /**
   * Get relatedPlanCases
   * @return relatedPlanCases
  */
//  @ApiModelProperty(value = "")


  public List<String> getRelatedPlanCases() {
    return relatedPlanCases;
  }

  public void setRelatedPlanCases(List<String> relatedPlanCases) {
    this.relatedPlanCases = relatedPlanCases;
  }

  public Task relatedTestcasePlans(List<String> relatedTestcasePlans) {
    this.relatedTestcasePlans = relatedTestcasePlans;
    return this;
  }

  public Task addRelatedTestcasePlansItem(String relatedTestcasePlansItem) {
    if (this.relatedTestcasePlans == null) {
      this.relatedTestcasePlans = new ArrayList<>();
    }
    this.relatedTestcasePlans.add(relatedTestcasePlansItem);
    return this;
  }

  /**
   * Get relatedTestcasePlans
   * @return relatedTestcasePlans
  */
//  @ApiModelProperty(value = "")


  public List<String> getRelatedTestcasePlans() {
    return relatedTestcasePlans;
  }

  public void setRelatedTestcasePlans(List<String> relatedTestcasePlans) {
    this.relatedTestcasePlans = relatedTestcasePlans;
  }

  public Task relatedWikiPages(List<String> relatedWikiPages) {
    this.relatedWikiPages = relatedWikiPages;
    return this;
  }

  public Task addRelatedWikiPagesItem(String relatedWikiPagesItem) {
    if (this.relatedWikiPages == null) {
      this.relatedWikiPages = new ArrayList<>();
    }
    this.relatedWikiPages.add(relatedWikiPagesItem);
    return this;
  }

  /**
   * Get relatedWikiPages
   * @return relatedWikiPages
  */
//  @ApiModelProperty(value = "")


  public List<String> getRelatedWikiPages() {
    return relatedWikiPages;
  }

  public void setRelatedWikiPages(List<String> relatedWikiPages) {
    this.relatedWikiPages = relatedWikiPages;
  }

  public Task relatedActivities(List<String> relatedActivities) {
    this.relatedActivities = relatedActivities;
    return this;
  }

  public Task addRelatedActivitiesItem(String relatedActivitiesItem) {
    if (this.relatedActivities == null) {
      this.relatedActivities = new ArrayList<>();
    }
    this.relatedActivities.add(relatedActivitiesItem);
    return this;
  }

  /**
   * Get relatedActivities
   * @return relatedActivities
  */
//  @ApiModelProperty(value = "")


  public List<String> getRelatedActivities() {
    return relatedActivities;
  }

  public void setRelatedActivities(List<String> relatedActivities) {
    this.relatedActivities = relatedActivities;
  }

  public Task relatedDevopsCommits(List<String> relatedDevopsCommits) {
    this.relatedDevopsCommits = relatedDevopsCommits;
    return this;
  }

  public Task addRelatedDevopsCommitsItem(String relatedDevopsCommitsItem) {
    if (this.relatedDevopsCommits == null) {
      this.relatedDevopsCommits = new ArrayList<>();
    }
    this.relatedDevopsCommits.add(relatedDevopsCommitsItem);
    return this;
  }

  /**
   * Get relatedDevopsCommits
   * @return relatedDevopsCommits
  */
//  @ApiModelProperty(value = "")


  public List<String> getRelatedDevopsCommits() {
    return relatedDevopsCommits;
  }

  public void setRelatedDevopsCommits(List<String> relatedDevopsCommits) {
    this.relatedDevopsCommits = relatedDevopsCommits;
  }

  public Task publishVersion(String publishVersion) {
    this.publishVersion = publishVersion;
    return this;
  }

  /**
   * Get publishVersion
   * @return publishVersion
  */
//  @ApiModelProperty(value = "")

  @Valid

  public String getPublishVersion() {
    return publishVersion;
  }

  public void setPublishVersion(String publishVersion) {
    this.publishVersion = publishVersion;
  }

  public Task publishContent(List<String> publishContent) {
    this.publishContent = publishContent;
    return this;
  }

  public Task addPublishContentItem(String publishContentItem) {
    if (this.publishContent == null) {
      this.publishContent = new ArrayList<>();
    }
    this.publishContent.add(publishContentItem);
    return this;
  }

  /**
   * Get publishContent
   * @return publishContent
  */
//  @ApiModelProperty(value = "")


  public List<String> getPublishContent() {
    return publishContent;
  }

  public void setPublishContent(List<String> publishContent) {
    this.publishContent = publishContent;
  }

  public Task publishContentDoneCount(Integer publishContentDoneCount) {
    this.publishContentDoneCount = publishContentDoneCount;
    return this;
  }

  /**
   * Get publishContentDoneCount
   * @return publishContentDoneCount
  */
//  @ApiModelProperty(value = "")


  public Integer getPublishContentDoneCount() {
    return publishContentDoneCount;
  }

  public void setPublishContentDoneCount(Integer publishContentDoneCount) {
    this.publishContentDoneCount = publishContentDoneCount;
  }

  public Task publishContentCount(Integer publishContentCount) {
    this.publishContentCount = publishContentCount;
    return this;
  }

  /**
   * Get publishContentCount
   * @return publishContentCount
  */
//  @ApiModelProperty(value = "")


  public Integer getPublishContentCount() {
    return publishContentCount;
  }

  public void setPublishContentCount(Integer publishContentCount) {
    this.publishContentCount = publishContentCount;
  }

  public Task manhours(List<String> manhours) {
    this.manhours = manhours;
    return this;
  }

  public Task addManhoursItem(String manhoursItem) {
    if (this.manhours == null) {
      this.manhours = new ArrayList<>();
    }
    this.manhours.add(manhoursItem);
    return this;
  }

  /**
   * Get manhours
   * @return manhours
  */
//  @ApiModelProperty(value = "")


  public List<String> getManhours() {
    return manhours;
  }

  public void setManhours(List<String> manhours) {
    this.manhours = manhours;
  }

  public Task productUuids(List<String> productUuids) {
    this.productUuids = productUuids;
    return this;
  }

  public Task addProductUuidsItem(String productUuidsItem) {
    if (this.productUuids == null) {
      this.productUuids = new ArrayList<>();
    }
    this.productUuids.add(productUuidsItem);
    return this;
  }

  /**
   * Get productUuids
   * @return productUuids
  */
//  @ApiModelProperty(value = "")


  public List<String> getProductUuids() {
    return productUuids;
  }

  public void setProductUuids(List<String> productUuids) {
    this.productUuids = productUuids;
  }

  public Task skipCheckFieldPermissions(Boolean skipCheckFieldPermissions) {
    this.skipCheckFieldPermissions = skipCheckFieldPermissions;
    return this;
  }

  /**
   * Get skipCheckFieldPermissions
   * @return skipCheckFieldPermissions
  */
//  @ApiModelProperty(value = "")


  public Boolean getSkipCheckFieldPermissions() {
    return skipCheckFieldPermissions;
  }

  public void setSkipCheckFieldPermissions(Boolean skipCheckFieldPermissions) {
    this.skipCheckFieldPermissions = skipCheckFieldPermissions;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    Task inlineResponse2002Tasks = (Task) o;
    return Objects.equals(this.uuid, inlineResponse2002Tasks.uuid) &&
        Objects.equals(this.owner, inlineResponse2002Tasks.owner) &&
        Objects.equals(this.assign, inlineResponse2002Tasks.assign) &&
        Objects.equals(this.tags, inlineResponse2002Tasks.tags) &&
        Objects.equals(this.sprintUuid, inlineResponse2002Tasks.sprintUuid) &&
        Objects.equals(this.projectUuid, inlineResponse2002Tasks.projectUuid) &&
        Objects.equals(this.issueTypeScopeUuid, inlineResponse2002Tasks.issueTypeScopeUuid) &&
        Objects.equals(this.issueTypeUuid, inlineResponse2002Tasks.issueTypeUuid) &&
        Objects.equals(this.subIssueTypeUuid, inlineResponse2002Tasks.subIssueTypeUuid) &&
        Objects.equals(this.statusUuid, inlineResponse2002Tasks.statusUuid) &&
        Objects.equals(this.createTime, inlineResponse2002Tasks.createTime) &&
        Objects.equals(this.status, inlineResponse2002Tasks.status) &&
        Objects.equals(this.summary, inlineResponse2002Tasks.summary) &&
        Objects.equals(this.desc, inlineResponse2002Tasks.desc) &&
        Objects.equals(this.descRich, inlineResponse2002Tasks.descRich) &&
        Objects.equals(this.parentUuid, inlineResponse2002Tasks.parentUuid) &&
        Objects.equals(this.position, inlineResponse2002Tasks.position) &&
        Objects.equals(this.number, inlineResponse2002Tasks.number) &&
        Objects.equals(this.priority, inlineResponse2002Tasks.priority) &&
        Objects.equals(this.assessManhour, inlineResponse2002Tasks.assessManhour) &&
        Objects.equals(this.totalManhour, inlineResponse2002Tasks.totalManhour) &&
        Objects.equals(this.remainingManhour, inlineResponse2002Tasks.remainingManhour) &&
        Objects.equals(this.estimateVariance, inlineResponse2002Tasks.estimateVariance) &&
        Objects.equals(this.timeProgress, inlineResponse2002Tasks.timeProgress) &&
        Objects.equals(this.attribute, inlineResponse2002Tasks.attribute) &&
        Objects.equals(this.watchers, inlineResponse2002Tasks.watchers) &&
        Objects.equals(this.fieldValues, inlineResponse2002Tasks.fieldValues) &&
        Objects.equals(this.relatedTasks, inlineResponse2002Tasks.relatedTasks) &&
        Objects.equals(this.links, inlineResponse2002Tasks.links) &&
        Objects.equals(this.subtasks, inlineResponse2002Tasks.subtasks) &&
        Objects.equals(this.path, inlineResponse2002Tasks.path) &&
        Objects.equals(this.codeCommits, inlineResponse2002Tasks.codeCommits) &&
        Objects.equals(this.relatedTaskCount, inlineResponse2002Tasks.relatedTaskCount) &&
        Objects.equals(this.visibleSubtaskCount, inlineResponse2002Tasks.visibleSubtaskCount) &&
        Objects.equals(this.subtaskCount, inlineResponse2002Tasks.subtaskCount) &&
        Objects.equals(this.discussionCount, inlineResponse2002Tasks.discussionCount) &&
        Objects.equals(this.attachmentCount, inlineResponse2002Tasks.attachmentCount) &&
        Objects.equals(this.relatedCases, inlineResponse2002Tasks.relatedCases) &&
        Objects.equals(this.relatedPlanCases, inlineResponse2002Tasks.relatedPlanCases) &&
        Objects.equals(this.relatedTestcasePlans, inlineResponse2002Tasks.relatedTestcasePlans) &&
        Objects.equals(this.relatedWikiPages, inlineResponse2002Tasks.relatedWikiPages) &&
        Objects.equals(this.relatedActivities, inlineResponse2002Tasks.relatedActivities) &&
        Objects.equals(this.relatedDevopsCommits, inlineResponse2002Tasks.relatedDevopsCommits) &&
        Objects.equals(this.publishVersion, inlineResponse2002Tasks.publishVersion) &&
        Objects.equals(this.publishContent, inlineResponse2002Tasks.publishContent) &&
        Objects.equals(this.publishContentDoneCount, inlineResponse2002Tasks.publishContentDoneCount) &&
        Objects.equals(this.publishContentCount, inlineResponse2002Tasks.publishContentCount) &&
        Objects.equals(this.manhours, inlineResponse2002Tasks.manhours) &&
        Objects.equals(this.productUuids, inlineResponse2002Tasks.productUuids) &&
        Objects.equals(this.skipCheckFieldPermissions, inlineResponse2002Tasks.skipCheckFieldPermissions);
  }

  @Override
  public int hashCode() {
    return Objects.hash(uuid, owner, assign, tags, sprintUuid, projectUuid, issueTypeScopeUuid, issueTypeUuid, subIssueTypeUuid, statusUuid, createTime, status, summary, desc, descRich, parentUuid, position, number, priority, assessManhour, totalManhour, remainingManhour, estimateVariance, timeProgress, attribute, watchers, fieldValues, relatedTasks, links, subtasks, path, codeCommits, relatedTaskCount, visibleSubtaskCount, subtaskCount, discussionCount, attachmentCount, relatedCases, relatedPlanCases, relatedTestcasePlans, relatedWikiPages, relatedActivities, relatedDevopsCommits, publishVersion, publishContent, publishContentDoneCount, publishContentCount, manhours, productUuids, skipCheckFieldPermissions);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class InlineResponse2002Tasks {\n");
    
    sb.append("    uuid: ").append(toIndentedString(uuid)).append("\n");
    sb.append("    owner: ").append(toIndentedString(owner)).append("\n");
    sb.append("    assign: ").append(toIndentedString(assign)).append("\n");
    sb.append("    tags: ").append(toIndentedString(tags)).append("\n");
    sb.append("    sprintUuid: ").append(toIndentedString(sprintUuid)).append("\n");
    sb.append("    projectUuid: ").append(toIndentedString(projectUuid)).append("\n");
    sb.append("    issueTypeScopeUuid: ").append(toIndentedString(issueTypeScopeUuid)).append("\n");
    sb.append("    issueTypeUuid: ").append(toIndentedString(issueTypeUuid)).append("\n");
    sb.append("    subIssueTypeUuid: ").append(toIndentedString(subIssueTypeUuid)).append("\n");
    sb.append("    statusUuid: ").append(toIndentedString(statusUuid)).append("\n");
    sb.append("    createTime: ").append(toIndentedString(createTime)).append("\n");
    sb.append("    status: ").append(toIndentedString(status)).append("\n");
    sb.append("    summary: ").append(toIndentedString(summary)).append("\n");
    sb.append("    desc: ").append(toIndentedString(desc)).append("\n");
    sb.append("    descRich: ").append(toIndentedString(descRich)).append("\n");
    sb.append("    parentUuid: ").append(toIndentedString(parentUuid)).append("\n");
    sb.append("    position: ").append(toIndentedString(position)).append("\n");
    sb.append("    number: ").append(toIndentedString(number)).append("\n");
    sb.append("    priority: ").append(toIndentedString(priority)).append("\n");
    sb.append("    assessManhour: ").append(toIndentedString(assessManhour)).append("\n");
    sb.append("    totalManhour: ").append(toIndentedString(totalManhour)).append("\n");
    sb.append("    remainingManhour: ").append(toIndentedString(remainingManhour)).append("\n");
    sb.append("    estimateVariance: ").append(toIndentedString(estimateVariance)).append("\n");
    sb.append("    timeProgress: ").append(toIndentedString(timeProgress)).append("\n");
    sb.append("    attribute: ").append(toIndentedString(attribute)).append("\n");
    sb.append("    watchers: ").append(toIndentedString(watchers)).append("\n");
    sb.append("    fieldValues: ").append(toIndentedString(fieldValues)).append("\n");
    sb.append("    relatedTasks: ").append(toIndentedString(relatedTasks)).append("\n");
    sb.append("    links: ").append(toIndentedString(links)).append("\n");
    sb.append("    subtasks: ").append(toIndentedString(subtasks)).append("\n");
    sb.append("    path: ").append(toIndentedString(path)).append("\n");
    sb.append("    codeCommits: ").append(toIndentedString(codeCommits)).append("\n");
    sb.append("    relatedTaskCount: ").append(toIndentedString(relatedTaskCount)).append("\n");
    sb.append("    visibleSubtaskCount: ").append(toIndentedString(visibleSubtaskCount)).append("\n");
    sb.append("    subtaskCount: ").append(toIndentedString(subtaskCount)).append("\n");
    sb.append("    discussionCount: ").append(toIndentedString(discussionCount)).append("\n");
    sb.append("    attachmentCount: ").append(toIndentedString(attachmentCount)).append("\n");
    sb.append("    relatedCases: ").append(toIndentedString(relatedCases)).append("\n");
    sb.append("    relatedPlanCases: ").append(toIndentedString(relatedPlanCases)).append("\n");
    sb.append("    relatedTestcasePlans: ").append(toIndentedString(relatedTestcasePlans)).append("\n");
    sb.append("    relatedWikiPages: ").append(toIndentedString(relatedWikiPages)).append("\n");
    sb.append("    relatedActivities: ").append(toIndentedString(relatedActivities)).append("\n");
    sb.append("    relatedDevopsCommits: ").append(toIndentedString(relatedDevopsCommits)).append("\n");
    sb.append("    publishVersion: ").append(toIndentedString(publishVersion)).append("\n");
    sb.append("    publishContent: ").append(toIndentedString(publishContent)).append("\n");
    sb.append("    publishContentDoneCount: ").append(toIndentedString(publishContentDoneCount)).append("\n");
    sb.append("    publishContentCount: ").append(toIndentedString(publishContentCount)).append("\n");
    sb.append("    manhours: ").append(toIndentedString(manhours)).append("\n");
    sb.append("    productUuids: ").append(toIndentedString(productUuids)).append("\n");
    sb.append("    skipCheckFieldPermissions: ").append(toIndentedString(skipCheckFieldPermissions)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

  public static class FieldValues {
//    {
//      "field_uuid": "QYd4rvwQ",
//            "type": 1,
//            "value": "29NmwxUC",
//            "value_type": 0
//    }
    private String field_uuid;
    private Integer type ;
    private Object value;
    private Integer value_type;

    public String getField_uuid() {
      return field_uuid;
    }

    public void setField_uuid(String field_uuid) {
      this.field_uuid = field_uuid;
    }

    public Integer getType() {
      return type;
    }

    public void setType(Integer type) {
      this.type = type;
    }

    public Object getValue() {
      return value;
    }

    public void setValue(Object value) {
      this.value = value;
    }

    public Integer getValue_type() {
      return value_type;
    }

    public void setValue_type(Integer value_type) {
      this.value_type = value_type;
    }
  }
}

