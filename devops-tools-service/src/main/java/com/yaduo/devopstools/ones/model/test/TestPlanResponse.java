package com.yaduo.devopstools.ones.model.test;

import com.fasterxml.jackson.annotation.JsonProperty;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.List;

/**
 * InlineResponse200
 */
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.SpringCodegen", date = "2022-04-21T21:27:40.321239+08:00[Asia/Shanghai]")

public class TestPlanResponse {
  @JsonProperty("plans")
  @Valid
  private List<Plans> plans = new ArrayList<>();


  public TestPlanResponse plans(List<Plans> plans) {
    this.plans = plans;
    return this;
  }

  public TestPlanResponse addPlansItem(Plans plansItem) {
    this.plans.add(plansItem);
    return this;
  }

  /**
   * Get plans
   * @return plans
  */
//  @ApiModelProperty(required = true, value = "")
  @NotNull

  @Valid

  public List<Plans> getPlans() {
    return plans;
  }

  public void setPlans(List<Plans> plans) {
    this.plans = plans;
  }






  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

