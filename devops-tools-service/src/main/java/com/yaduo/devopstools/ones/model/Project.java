package com.yaduo.devopstools.ones.model;

import java.io.Serializable;

public class Project implements Serializable {
  private String archive_user;

  private String status_uuid;

  private Long archive_time;

  private String type;

  private String uuid;

  private Boolean is_pin;

  private Boolean is_sample;

  private String program_uuid;

  private String status_category;

  private Integer task_update_time;

  private String name;

  private Boolean is_archive;

  private Boolean is_open_email_notify;

  private Integer deadline;

  private String assign;

  private String announcement;

  private Integer status;

  public String getArchive_user() {
    return this.archive_user;
  }

  public void setArchive_user(String archive_user) {
    this.archive_user = archive_user;
  }

  public String getStatus_uuid() {
    return this.status_uuid;
  }

  public void setStatus_uuid(String status_uuid) {
    this.status_uuid = status_uuid;
  }

  public Long getArchive_time() {
    return this.archive_time;
  }

  public void setArchive_time(Long archive_time) {
    this.archive_time = archive_time;
  }

  public String getType() {
    return this.type;
  }

  public void setType(String type) {
    this.type = type;
  }

  public String getUuid() {
    return this.uuid;
  }

  public void setUuid(String uuid) {
    this.uuid = uuid;
  }

  public Boolean getIs_pin() {
    return this.is_pin;
  }

  public void setIs_pin(Boolean is_pin) {
    this.is_pin = is_pin;
  }

  public Boolean getIs_sample() {
    return this.is_sample;
  }

  public void setIs_sample(Boolean is_sample) {
    this.is_sample = is_sample;
  }

  public String getProgram_uuid() {
    return this.program_uuid;
  }

  public void setProgram_uuid(String program_uuid) {
    this.program_uuid = program_uuid;
  }

  public String getStatus_category() {
    return this.status_category;
  }

  public void setStatus_category(String status_category) {
    this.status_category = status_category;
  }

  public Integer getTask_update_time() {
    return this.task_update_time;
  }

  public void setTask_update_time(Integer task_update_time) {
    this.task_update_time = task_update_time;
  }

  public String getName() {
    return this.name;
  }

  public void setName(String name) {
    this.name = name;
  }

  public Boolean getIs_archive() {
    return this.is_archive;
  }

  public void setIs_archive(Boolean is_archive) {
    this.is_archive = is_archive;
  }

  public Boolean getIs_open_email_notify() {
    return this.is_open_email_notify;
  }

  public void setIs_open_email_notify(Boolean is_open_email_notify) {
    this.is_open_email_notify = is_open_email_notify;
  }

  public Integer getDeadline() {
    return this.deadline;
  }

  public void setDeadline(Integer deadline) {
    this.deadline = deadline;
  }

  public String getAssign() {
    return this.assign;
  }

  public void setAssign(String assign) {
    this.assign = assign;
  }

  public String getAnnouncement() {
    return this.announcement;
  }

  public void setAnnouncement(String announcement) {
    this.announcement = announcement;
  }

  public Integer getStatus() {
    return this.status;
  }

  public void setStatus(Integer status) {
    this.status = status;
  }
}
