package com.yaduo.devopstools.ones.controller;


import com.alibaba.excel.EasyExcel;
import com.yaduo.devopstools.ones.model.AddHourDTO;
import com.yaduo.devopstools.ones.model.EventMsg;
import com.yaduo.devopstools.ones.model.ImportParam;
import com.yaduo.devopstools.ones.model.TestCaseInfo;
import com.yaduo.devopstools.ones.service.ImportTaskExcelReadListener;
import com.yaduo.devopstools.ones.service.OnesApiService;
import com.yaduo.infras.core.logging.annotations.RestMethod;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * ones相关接口
 *
 * <AUTHOR>
 * @date 2022-04-18 00:50
 */
@RestController
@RequestMapping("/ones")
public class OnesController {
    @Autowired
    private OnesApiService onesApiService;

    @PostMapping("importTask")
    public String importTask(@RequestParam("file") MultipartFile file) {
        try {
            EasyExcel.read(file.getInputStream(), new ImportTaskExcelReadListener(onesApiService)).doReadAll();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        return "提交成功";
    }


    @GetMapping("importTask/download")
    public void downloadTemplate(HttpServletResponse response) {
        try {
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            String fileName = URLEncoder.encode("ones任务导入模板", "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
            // 写法2
//            String fileName = TestFileUtil.getPath() + "simpleWrite" + System.currentTimeMillis() + ".xlsx";
            // 这里 需要指定写用哪个class去写，然后写到第一个sheet，名字为模板 然后文件流会自动关闭
            // 如果这里想使用03 则 传入excelType参数即可
            EasyExcel.write(response.getOutputStream(), ImportParam.class)
                    .sheet().doWrite(Collections.emptyList());
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }


    @GetMapping("exportTestCase")
    public void exportTestCase(HttpServletResponse response) {
        try {
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            String fileName = URLEncoder.encode("测试用例导出", "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");

            List<TestCaseInfo> list = onesApiService.exportTestCases("KMgnuFDd");
            EasyExcel.write(response.getOutputStream(), TestCaseInfo.class)
                    .sheet().doWrite(list);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    @GetMapping("exportAllLibraryTestCases")
    public void exportAllLibraryTestCases(@RequestParam(value = "refresh", required = false, defaultValue = "false") boolean refresh,
                                          HttpServletResponse response) {
        try {
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            String fileName = URLEncoder.encode("测试用例导出", "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
            if (refresh) {
                onesApiService.refresh();
            }
            List<TestCaseInfo> list = onesApiService.exportAllLibraryTestCases("KMgnuFDd");
            EasyExcel.write(response.getOutputStream(), TestCaseInfo.class)
                    .sheet().doWrite(list);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }


    /**
     * ones webhook消息通知
     *
     * @param eventMsg
     * @return
     */
    @PostMapping("webhook")
    @RestMethod
    public String webhook(@RequestBody EventMsg eventMsg) {
        onesApiService.webhook(eventMsg);
        return eventMsg.getId();
    }


    /**
     * ones 登记工时
     *
     * @param addHourDTO
     * @return
     */
    @PostMapping("addHour")
    public String webhook(@RequestBody AddHourDTO addHourDTO) {
        onesApiService.addHour(addHourDTO);
        return "OK";
    }


    /**
     * ones 详情
     *
     * @param onesId
     * @return
     */
    @GetMapping("detail")
    public Object detailByOnesID(@RequestParam String onesId) {
        return onesApiService.detailByOnesID(onesId);
    }

    /**
     * ones 详情
     *
     * @param onesId
     * @return
     */
    @PostMapping("detail")
    public Object detailByOnesIDPost(@RequestBody Map<String,Object> map) {
        String onesId = map.get("onesId").toString();
        if (onesId.startsWith("#")) {
            // 正则表达式
            String regex = "#(\\d+)";

            // 创建 Pattern 对象
            Pattern pattern = Pattern.compile(regex);

            // 创建 Matcher 对象
            Matcher matcher = pattern.matcher(onesId);

            // 提取第一个匹配的数字
            if (matcher.find()) {
                String firstMatch = matcher.group(1); // 提取第一个捕获组
                System.out.println("第一个匹配的数字：" + firstMatch);
                return onesApiService.detailByOnesID(matcher.group(1));
            } else {
                System.out.println("未找到匹配的数字");
            }
        }
        return onesApiService.detailByOnesID(onesId);
    }
}
