package com.yaduo.devopstools.ones.model;

import com.fasterxml.jackson.annotation.JsonProperty;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * ProjectApiProjectTeamTeamUUIDTasksAdd2Tasks
 */
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.SpringCodegen", date = "2022-04-17T14:07:26.169847+08:00[Asia/Shanghai]")
public class TaskAdd2Tasks {
  @JsonProperty("uuid")
  private String uuid;

  @JsonProperty("owner")
  private String owner;

  @JsonProperty("assign")
  private String assign;

  @JsonProperty("summary")
  private String summary;

  @JsonProperty("parent_uuid")
  private String parentUuid;

  @JsonProperty("desc_rich")
  private String descRich;

  @JsonProperty("project_uuid")
  private String projectUuid;

  @JsonProperty("issue_type_uuid")
  private String issueTypeUuid;

  @JsonProperty("sub_issue_type_uuid")
  private String subIssueTypeUuid;

  @JsonProperty("priority")
  private String priority;

  @JsonProperty("add_manhours")
  @Valid
  private List<TasksAdd2AddManhours> addManhours = new ArrayList<>();

  @JsonProperty("field_values")
  @Valid
  private List<TasksAdd2FieldValues> fieldValues = new ArrayList<>();

  public TaskAdd2Tasks uuid(String uuid) {
    this.uuid = uuid;
    return this;
  }

  /**
   * Get uuid
   * @return uuid
  */
//  @ApiModelProperty(required = true, value = "")
  @NotNull
  public String getUuid() {
    return uuid;
  }

  public void setUuid(String uuid) {
    this.uuid = uuid;
  }

  public TaskAdd2Tasks owner(String owner) {
    this.owner = owner;
    return this;
  }

  /**
   * Get owner
   * @return owner
  */
//  @ApiModelProperty(required = true, value = "")
  @NotNull


  public String getOwner() {
    return owner;
  }

  public void setOwner(String owner) {
    this.owner = owner;
  }

  public TaskAdd2Tasks assign(String assign) {
    this.assign = assign;
    return this;
  }

  /**
   * Get assign
   * @return assign
  */
//  @ApiModelProperty(required = true, value = "")
  @NotNull


  public String getAssign() {
    return assign;
  }

  public void setAssign(String assign) {
    this.assign = assign;
  }

  public TaskAdd2Tasks summary(String summary) {
    this.summary = summary;
    return this;
  }

  /**
   * Get summary
   * @return summary
  */
//  @ApiModelProperty(required = true, value = "")
  @NotNull


  public String getSummary() {
    return summary;
  }

  public void setSummary(String summary) {
    this.summary = summary;
  }

  public TaskAdd2Tasks parentUuid(String parentUuid) {
    this.parentUuid = parentUuid;
    return this;
  }

  /**
   * Get parentUuid
   * @return parentUuid
  */
//  @ApiModelProperty(required = true, value = "")
  @NotNull


  public String getParentUuid() {
    return parentUuid;
  }

  public void setParentUuid(String parentUuid) {
    this.parentUuid = parentUuid;
  }

  public TaskAdd2Tasks descRich(String descRich) {
    this.descRich = descRich;
    return this;
  }

  /**
   * Get descRich
   * @return descRich
  */
//  @ApiModelProperty(value = "")


  public String getDescRich() {
    return descRich;
  }

  public void setDescRich(String descRich) {
    this.descRich = descRich;
  }

  public TaskAdd2Tasks projectUuid(String projectUuid) {
    this.projectUuid = projectUuid;
    return this;
  }

  /**
   * Get projectUuid
   * @return projectUuid
  */
//  @ApiModelProperty(required = true, value = "")
  @NotNull


  public String getProjectUuid() {
    return projectUuid;
  }

  public void setProjectUuid(String projectUuid) {
    this.projectUuid = projectUuid;
  }

  public TaskAdd2Tasks issueTypeUuid(String issueTypeUuid) {
    this.issueTypeUuid = issueTypeUuid;
    return this;
  }

  /**
   * Get issueTypeUuid
   * @return issueTypeUuid
  */
//  @ApiModelProperty(required = true, value = "")
  @NotNull


  public String getIssueTypeUuid() {
    return issueTypeUuid;
  }

  public void setIssueTypeUuid(String issueTypeUuid) {
    this.issueTypeUuid = issueTypeUuid;
  }

  public TaskAdd2Tasks subIssueTypeUuid(String subIssueTypeUuid) {
    this.subIssueTypeUuid = subIssueTypeUuid;
    return this;
  }

  /**
   * Get subIssueTypeUuid
   * @return subIssueTypeUuid
  */
//  @ApiModelProperty(required = true, value = "")
  @NotNull


  public String getSubIssueTypeUuid() {
    return subIssueTypeUuid;
  }

  public void setSubIssueTypeUuid(String subIssueTypeUuid) {
    this.subIssueTypeUuid = subIssueTypeUuid;
  }

  public TaskAdd2Tasks priority(String priority) {
    this.priority = priority;
    return this;
  }

  /**
   * Get priority
   * @return priority
  */
//  @ApiModelProperty(required = true, value = "")
  @NotNull


  public String getPriority() {
    return priority;
  }

  public void setPriority(String priority) {
    this.priority = priority;
  }

  public TaskAdd2Tasks addManhours(List<TasksAdd2AddManhours> addManhours) {
    this.addManhours = addManhours;
    return this;
  }

  public TaskAdd2Tasks addAddManhoursItem(TasksAdd2AddManhours addManhoursItem) {
    this.addManhours.add(addManhoursItem);
    return this;
  }

  /**
   * Get addManhours
   * @return addManhours
  */
//  @ApiModelProperty(required = true, value = "")
  @NotNull

  @Valid

  public List<TasksAdd2AddManhours> getAddManhours() {
    return addManhours;
  }

  public void setAddManhours(List<TasksAdd2AddManhours> addManhours) {
    this.addManhours = addManhours;
  }

  public TaskAdd2Tasks fieldValues(List<TasksAdd2FieldValues> fieldValues) {
    this.fieldValues = fieldValues;
    return this;
  }

  public TaskAdd2Tasks addFieldValuesItem(TasksAdd2FieldValues fieldValuesItem) {
    if (this.fieldValues == null) {
      this.fieldValues = new ArrayList<>();
    }
    this.fieldValues.add(fieldValuesItem);
    return this;
  }

  /**
   * Get fieldValues
   * @return fieldValues
  */
//  @ApiModelProperty(value = "")

  @Valid

  public List<TasksAdd2FieldValues> getFieldValues() {
    return fieldValues;
  }

  public void setFieldValues(List<TasksAdd2FieldValues> fieldValues) {
    this.fieldValues = fieldValues;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    TaskAdd2Tasks projectApiProjectTeamTeamUUIDTasksAdd2Tasks = (TaskAdd2Tasks) o;
    return Objects.equals(this.uuid, projectApiProjectTeamTeamUUIDTasksAdd2Tasks.uuid) &&
        Objects.equals(this.owner, projectApiProjectTeamTeamUUIDTasksAdd2Tasks.owner) &&
        Objects.equals(this.assign, projectApiProjectTeamTeamUUIDTasksAdd2Tasks.assign) &&
        Objects.equals(this.summary, projectApiProjectTeamTeamUUIDTasksAdd2Tasks.summary) &&
        Objects.equals(this.parentUuid, projectApiProjectTeamTeamUUIDTasksAdd2Tasks.parentUuid) &&
        Objects.equals(this.descRich, projectApiProjectTeamTeamUUIDTasksAdd2Tasks.descRich) &&
        Objects.equals(this.projectUuid, projectApiProjectTeamTeamUUIDTasksAdd2Tasks.projectUuid) &&
        Objects.equals(this.issueTypeUuid, projectApiProjectTeamTeamUUIDTasksAdd2Tasks.issueTypeUuid) &&
        Objects.equals(this.subIssueTypeUuid, projectApiProjectTeamTeamUUIDTasksAdd2Tasks.subIssueTypeUuid) &&
        Objects.equals(this.priority, projectApiProjectTeamTeamUUIDTasksAdd2Tasks.priority) &&
        Objects.equals(this.addManhours, projectApiProjectTeamTeamUUIDTasksAdd2Tasks.addManhours) &&
        Objects.equals(this.fieldValues, projectApiProjectTeamTeamUUIDTasksAdd2Tasks.fieldValues);
  }

  @Override
  public int hashCode() {
    return Objects.hash(uuid, owner, assign, summary, parentUuid, descRich, projectUuid, issueTypeUuid, subIssueTypeUuid, priority, addManhours, fieldValues);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class ProjectApiProjectTeamTeamUUIDTasksAdd2Tasks {\n");
    sb.append("    uuid: ").append(toIndentedString(uuid)).append("\n");
    sb.append("    owner: ").append(toIndentedString(owner)).append("\n");
    sb.append("    assign: ").append(toIndentedString(assign)).append("\n");
    sb.append("    summary: ").append(toIndentedString(summary)).append("\n");
    sb.append("    parentUuid: ").append(toIndentedString(parentUuid)).append("\n");
    sb.append("    descRich: ").append(toIndentedString(descRich)).append("\n");
    sb.append("    projectUuid: ").append(toIndentedString(projectUuid)).append("\n");
    sb.append("    issueTypeUuid: ").append(toIndentedString(issueTypeUuid)).append("\n");
    sb.append("    subIssueTypeUuid: ").append(toIndentedString(subIssueTypeUuid)).append("\n");
    sb.append("    priority: ").append(toIndentedString(priority)).append("\n");
    sb.append("    addManhours: ").append(toIndentedString(addManhours)).append("\n");
    sb.append("    fieldValues: ").append(toIndentedString(fieldValues)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

