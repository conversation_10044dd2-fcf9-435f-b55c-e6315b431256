package com.yaduo.devopstools.ones.model;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serializable;


@Data
public class TestCaseInfo implements Serializable {

    @ExcelProperty("项目")
    private String projectName;
    @ExcelProperty("测试计划")
    private String planName;
    @ExcelProperty("计划创建人")
    private String planOwner;
    @ExcelProperty("备注")
    private String note;
    @ExcelProperty("planId")
    private String plan_uuid;
    @ExcelProperty("创建时间")
    private String create_time;
    @ExcelProperty("用例库ID")
    private String library_uuid;
    @ExcelProperty("用例库")
    private String libraryName;
    @ExcelProperty("模块")
    private String moduleName;

    private String module_uuid;

    @ExcelProperty("优先级")
    private String priority;
    @ExcelProperty("类型")
    private String type;

    private String uuid;

    @ExcelProperty("执行结果")
    private String result;

    @ExcelProperty("编号")
    private Integer number;

    @ExcelProperty("执行人")
    private String executor;

    @ExcelProperty("用例")
    private String name;

    @ExcelProperty("创建人")
    private String assign;

    @ExcelProperty("描述")
    private String desc;

}
