package com.yaduo.devopstools.ones.model;

import com.fasterxml.jackson.annotation.JsonProperty;

import javax.validation.constraints.NotNull;
import java.util.Objects;

/**
 * ProjectApiProjectTeamTeamUUIDTasksAdd2FieldValues
 */
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.SpringCodegen", date = "2022-04-17T14:07:26.169847+08:00[Asia/Shanghai]")

public class TasksAdd2FieldValues {
  @JsonProperty("field_uuid")
  private String fieldUuid;

  @JsonProperty("type")
  private Integer type;

  @JsonProperty("value")
  private Object value;

  public TasksAdd2FieldValues fieldUuid(String fieldUuid) {
    this.fieldUuid = fieldUuid;
    return this;
  }

  /**
   * Get fieldUuid
   * @return fieldUuid
  */
//  @ApiModelProperty(required = true, value = "")
  @NotNull


  public String getFieldUuid() {
    return fieldUuid;
  }

  public void setFieldUuid(String fieldUuid) {
    this.fieldUuid = fieldUuid;
  }

  public TasksAdd2FieldValues type(Integer type) {
    this.type = type;
    return this;
  }

  /**
   * Get type
   * @return type
  */
//  @ApiModelProperty(required = true, value = "")
  @NotNull


  public Integer getType() {
    return type;
  }

  public void setType(Integer type) {
    this.type = type;
  }

  public TasksAdd2FieldValues value(Object value) {
    this.value = value;
    return this;
  }

  /**
   * Get value
   * @return value
  */
//  @ApiModelProperty(required = true, value = "")
  @NotNull


  public Object getValue() {
    return value;
  }

  public void setValue(Object value) {
    this.value = value;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    TasksAdd2FieldValues projectApiProjectTeamTeamUUIDTasksAdd2FieldValues = (TasksAdd2FieldValues) o;
    return Objects.equals(this.fieldUuid, projectApiProjectTeamTeamUUIDTasksAdd2FieldValues.fieldUuid) &&
        Objects.equals(this.type, projectApiProjectTeamTeamUUIDTasksAdd2FieldValues.type) &&
        Objects.equals(this.value, projectApiProjectTeamTeamUUIDTasksAdd2FieldValues.value);
  }

  @Override
  public int hashCode() {
    return Objects.hash(fieldUuid, type, value);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class ProjectApiProjectTeamTeamUUIDTasksAdd2FieldValues {\n");
    
    sb.append("    fieldUuid: ").append(toIndentedString(fieldUuid)).append("\n");
    sb.append("    type: ").append(toIndentedString(type)).append("\n");
    sb.append("    value: ").append(toIndentedString(value)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

