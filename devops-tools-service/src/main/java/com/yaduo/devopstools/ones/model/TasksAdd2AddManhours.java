package com.yaduo.devopstools.ones.model;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.Objects;

/**
 * ProjectApiProjectTeamTeamUUIDTasksAdd2AddManhours
 */
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.SpringCodegen", date = "2022-04-17T14:07:26.169847+08:00[Asia/Shanghai]")

public class TasksAdd2AddManhours {
  @JsonProperty("task")
  private String task;

  @JsonProperty("owner")
  private String owner;

  @JsonProperty("type")
  private String type;

  @JsonProperty("hours")
  private Integer hours;

  @JsonProperty("from")
  private Long from;

  @JsonProperty("to")
  private Long to;

  @JsonProperty("hours_format")
  private String hoursFormat;

  @JsonProperty("mode")
  private String mode;

  public TasksAdd2AddManhours task(String task) {
    this.task = task;
    return this;
  }

  /**
   * Get task
   * @return task
  */
//  @ApiModelProperty(value = "")


  public String getTask() {
    return task;
  }

  public void setTask(String task) {
    this.task = task;
  }

  public TasksAdd2AddManhours owner(String owner) {
    this.owner = owner;
    return this;
  }

  /**
   * Get owner
   * @return owner
  */
//  @ApiModelProperty(value = "")


  public String getOwner() {
    return owner;
  }

  public void setOwner(String owner) {
    this.owner = owner;
  }

  public TasksAdd2AddManhours type(String type) {
    this.type = type;
    return this;
  }

  /**
   * Get type
   * @return type
  */
//  @ApiModelProperty(value = "")


  public String getType() {
    return type;
  }

  public void setType(String type) {
    this.type = type;
  }

  public TasksAdd2AddManhours hours(Integer hours) {
    this.hours = hours;
    return this;
  }

  /**
   * Get hours
   * @return hours
  */
//  @ApiModelProperty(value = "")


  public Integer getHours() {
    return hours;
  }

  public void setHours(Integer hours) {
    this.hours = hours;
  }

  public TasksAdd2AddManhours from(Long from) {
    this.from = from;
    return this;
  }

  /**
   * Get from
   * @return from
  */
//  @ApiModelProperty(value = "")


  public Long getFrom() {
    return from;
  }

  public void setFrom(Long from) {
    this.from = from;
  }

  public TasksAdd2AddManhours to(Long to) {
    this.to = to;
    return this;
  }

  /**
   * Get to
   * @return to
  */
//  @ApiModelProperty(value = "")


  public long getTo() {
    return to;
  }

  public void setTo(long to) {
    this.to = to;
  }

  public TasksAdd2AddManhours hoursFormat(String hoursFormat) {
    this.hoursFormat = hoursFormat;
    return this;
  }

  /**
   * Get hoursFormat
   * @return hoursFormat
  */
//  @ApiModelProperty(value = "")


  public String getHoursFormat() {
    return hoursFormat;
  }

  public void setHoursFormat(String hoursFormat) {
    this.hoursFormat = hoursFormat;
  }

  public TasksAdd2AddManhours mode(String mode) {
    this.mode = mode;
    return this;
  }

  /**
   * Get mode
   * @return mode
  */
//  @ApiModelProperty(value = "")


  public String getMode() {
    return mode;
  }

  public void setMode(String mode) {
    this.mode = mode;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    TasksAdd2AddManhours projectApiProjectTeamTeamUUIDTasksAdd2AddManhours = (TasksAdd2AddManhours) o;
    return Objects.equals(this.task, projectApiProjectTeamTeamUUIDTasksAdd2AddManhours.task) &&
        Objects.equals(this.owner, projectApiProjectTeamTeamUUIDTasksAdd2AddManhours.owner) &&
        Objects.equals(this.type, projectApiProjectTeamTeamUUIDTasksAdd2AddManhours.type) &&
        Objects.equals(this.hours, projectApiProjectTeamTeamUUIDTasksAdd2AddManhours.hours) &&
        Objects.equals(this.from, projectApiProjectTeamTeamUUIDTasksAdd2AddManhours.from) &&
        Objects.equals(this.to, projectApiProjectTeamTeamUUIDTasksAdd2AddManhours.to) &&
        Objects.equals(this.hoursFormat, projectApiProjectTeamTeamUUIDTasksAdd2AddManhours.hoursFormat) &&
        Objects.equals(this.mode, projectApiProjectTeamTeamUUIDTasksAdd2AddManhours.mode);
  }

  @Override
  public int hashCode() {
    return Objects.hash(task, owner, type, hours, from, to, hoursFormat, mode);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class ProjectApiProjectTeamTeamUUIDTasksAdd2AddManhours {\n");
    
    sb.append("    task: ").append(toIndentedString(task)).append("\n");
    sb.append("    owner: ").append(toIndentedString(owner)).append("\n");
    sb.append("    type: ").append(toIndentedString(type)).append("\n");
    sb.append("    hours: ").append(toIndentedString(hours)).append("\n");
    sb.append("    from: ").append(toIndentedString(from)).append("\n");
    sb.append("    to: ").append(toIndentedString(to)).append("\n");
    sb.append("    hoursFormat: ").append(toIndentedString(hoursFormat)).append("\n");
    sb.append("    mode: ").append(toIndentedString(mode)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

