package com.yaduo.devopstools.ones.model;

import lombok.Data;

import java.util.List;

@Data
public class Issue {
    private Fields fields;
    private HighlightFields highlightFields;

    @Data
    public static class Fields {
        private String assign;
        private String desc;
        private Integer issueTypeIcon;
        private String issueTypeName;
        private String issueTypeUuid;
        private Integer number;
        private String owner;
        private String projectName;
        private String projectUuid;
        private String subIssueTypeUuid;
        private String summary;
        private String uuid;
    }

    @Data
    public static class HighlightFields {
        private List<String> desc;
        private List<String> number;
        private List<String> summary;
    }
}