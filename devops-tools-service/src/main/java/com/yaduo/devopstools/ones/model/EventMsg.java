package com.yaduo.devopstools.ones.model;

import com.yaduo.devopstools.ones.model.EventMsg.Message.Raw_message.Ext.User;

import java.io.Serializable;
import java.util.List;

public class EventMsg {
    private List<? extends Message> messages;

    private String id;

    public List<? extends Message> getMessages() {
        return this.messages;
    }

    public void setMessages(List<? extends Message> messages) {
        this.messages = messages;
    }

    public String getId() {
        return this.id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public static class Message {
        private String issue_type_name;

        private String event_type;

        private Raw_message raw_message;

        private String issue_type_uuid;

        private String event_name;

        private String title;

        private String task_uuid;

        private String url;

        private User from_user;

        private List<User> to_users;

        public List<User> getTo_users() {
            return to_users;
        }

        public void setTo_users(List<User> to_users) {
            this.to_users = to_users;
        }

        private String desc;

        public String getIssue_type_name() {
            return this.issue_type_name;
        }

        public void setIssue_type_name(String issue_type_name) {
            this.issue_type_name = issue_type_name;
        }

        public String getEvent_type() {
            return this.event_type;
        }

        public void setEvent_type(String event_type) {
            this.event_type = event_type;
        }

        public Raw_message getRaw_message() {
            return this.raw_message;
        }

        public void setRaw_message(Raw_message raw_message) {
            this.raw_message = raw_message;
        }

        public String getIssue_type_uuid() {
            return this.issue_type_uuid;
        }

        public void setIssue_type_uuid(String issue_type_uuid) {
            this.issue_type_uuid = issue_type_uuid;
        }

        public String getEvent_name() {
            return this.event_name;
        }

        public void setEvent_name(String event_name) {
            this.event_name = event_name;
        }

        public String getTitle() {
            return this.title;
        }

        public void setTitle(String title) {
            this.title = title;
        }

        public String getTask_uuid() {
            return this.task_uuid;
        }

        public void setTask_uuid(String task_uuid) {
            this.task_uuid = task_uuid;
        }

        public String getUrl() {
            return this.url;
        }

        public void setUrl(String url) {
            this.url = url;
        }

        public User getFrom_user() {
            return this.from_user;
        }

        public void setFrom_user(User from_user) {
            this.from_user = from_user;
        }

        public String getDesc() {
            return this.desc;
        }

        public void setDesc(String desc) {
            this.desc = desc;
        }

        public static class Raw_message {
            private String ref_id;

            private String subject_id;

            private Ext ext;

            private String subject_type;

            private String object_type;

            private String object_attr;

            private String type;

            private String uuid;

            private String object_id;

            private Long send_time;

            private Boolean is_can_show_richtext_diff;

            private String object_name;

            private String action;

            private String team_uuid;

            private String from;

            private String old_value;

            private String to;

            private String ref_type;

            private String new_value;

            public String getRef_id() {
                return this.ref_id;
            }

            public void setRef_id(String ref_id) {
                this.ref_id = ref_id;
            }

            public String getSubject_id() {
                return this.subject_id;
            }

            public void setSubject_id(String subject_id) {
                this.subject_id = subject_id;
            }

            public Ext getExt() {
                return this.ext;
            }

            public void setExt(Ext ext) {
                this.ext = ext;
            }

            public String getSubject_type() {
                return this.subject_type;
            }

            public void setSubject_type(String subject_type) {
                this.subject_type = subject_type;
            }

            public String getObject_type() {
                return this.object_type;
            }

            public void setObject_type(String object_type) {
                this.object_type = object_type;
            }

            public String getObject_attr() {
                return this.object_attr;
            }

            public void setObject_attr(String object_attr) {
                this.object_attr = object_attr;
            }

            public String getType() {
                return this.type;
            }

            public void setType(String type) {
                this.type = type;
            }

            public String getUuid() {
                return this.uuid;
            }

            public void setUuid(String uuid) {
                this.uuid = uuid;
            }

            public String getObject_id() {
                return this.object_id;
            }

            public void setObject_id(String object_id) {
                this.object_id = object_id;
            }

            public Long getSend_time() {
                return this.send_time;
            }

            public void setSend_time(Long send_time) {
                this.send_time = send_time;
            }

            public Boolean getIs_can_show_richtext_diff() {
                return this.is_can_show_richtext_diff;
            }

            public void setIs_can_show_richtext_diff(Boolean is_can_show_richtext_diff) {
                this.is_can_show_richtext_diff = is_can_show_richtext_diff;
            }

            public String getObject_name() {
                return this.object_name;
            }

            public void setObject_name(String object_name) {
                this.object_name = object_name;
            }

            public String getAction() {
                return this.action;
            }

            public void setAction(String action) {
                this.action = action;
            }

            public String getTeam_uuid() {
                return this.team_uuid;
            }

            public void setTeam_uuid(String team_uuid) {
                this.team_uuid = team_uuid;
            }

            public String getFrom() {
                return this.from;
            }

            public void setFrom(String from) {
                this.from = from;
            }

            public String getOld_value() {
                return this.old_value;
            }

            public void setOld_value(String old_value) {
                this.old_value = old_value;
            }

            public String getTo() {
                return this.to;
            }

            public void setTo(String to) {
                this.to = to;
            }

            public String getRef_type() {
                return this.ref_type;
            }

            public void setRef_type(String ref_type) {
                this.ref_type = ref_type;
            }

            public String getNew_value() {
                return this.new_value;
            }

            public void setNew_value(String new_value) {
                this.new_value = new_value;
            }

            public static class Ext {
                private User new_option;

                private String field_uuid;

                private String parent_message_uuid;

                private String old_value;

                private User old_option;

                private Integer field_type;

                private String new_value;

                private String field_name;

                public User getNew_option() {
                    return this.new_option;
                }

                public void setNew_option(User new_option) {
                    this.new_option = new_option;
                }

                public String getField_uuid() {
                    return this.field_uuid;
                }

                public void setField_uuid(String field_uuid) {
                    this.field_uuid = field_uuid;
                }

                public String getParent_message_uuid() {
                    return this.parent_message_uuid;
                }

                public void setParent_message_uuid(String parent_message_uuid) {
                    this.parent_message_uuid = parent_message_uuid;
                }

                public String getOld_value() {
                    return this.old_value;
                }

                public void setOld_value(String old_value) {
                    this.old_value = old_value;
                }

                public User getOld_option() {
                    return this.old_option;
                }

                public void setOld_option(User old_option) {
                    this.old_option = old_option;
                }

                public Integer getField_type() {
                    return this.field_type;
                }

                public void setField_type(Integer field_type) {
                    this.field_type = field_type;
                }

                public String getNew_value() {
                    return this.new_value;
                }

                public void setNew_value(String new_value) {
                    this.new_value = new_value;
                }

                public String getField_name() {
                    return this.field_name;
                }

                public void setField_name(String field_name) {
                    this.field_name = field_name;
                }

                public static class User implements Serializable {
                    private String name;

                    private String uuid;

                    public String getName() {
                        return this.name;
                    }

                    public void setName(String name) {
                        this.name = name;
                    }

                    public String getUuid() {
                        return this.uuid;
                    }

                    public void setUuid(String uuid) {
                        this.uuid = uuid;
                    }
                }
            }
        }
    }
}
