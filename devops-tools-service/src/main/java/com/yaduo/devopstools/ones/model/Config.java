package com.yaduo.devopstools.ones.model;

import com.fasterxml.jackson.annotation.JsonProperty;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.Objects;

/**
 * Config
 */
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.SpringCodegen", date = "2022-04-17T00:46:55.176358+08:00[Asia/Shanghai]")

public class Config   {
  @JsonProperty("sprint")
  private ConfigSprint sprint;

  public Config sprint(ConfigSprint sprint) {
    this.sprint = sprint;
    return this;
  }

  /**
   * Get sprint
   * @return sprint
  */
//  @ApiModelProperty(required = true, value = "")
  @NotNull

  @Valid

  public ConfigSprint getSprint() {
    return sprint;
  }

  public void setSprint(ConfigSprint sprint) {
    this.sprint = sprint;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    Config config = (Config) o;
    return Objects.equals(this.sprint, config.sprint);
  }

  @Override
  public int hashCode() {
    return Objects.hash(sprint);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class Config {\n");
    
    sb.append("    sprint: ").append(toIndentedString(sprint)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

