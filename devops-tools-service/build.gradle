plugins {
    id 'org.springframework.boot'
}
apply plugin: 'kotlin-spring'
import org.apache.tools.ant.filters.ReplaceTokens
//Begin-把jar包输出到项目根目录下，并且添加上AppID
// Spring 2.5会自动生成plain.jar，所以这里enable设置false
jar {
    enabled = false
}
def appId=getAppId()
archivesBaseName = appId
private String getAppId() {
    def props = new Properties()
    file('src/main/resources/application.properties').withInputStream {
        props.load(it)
    }
    String app_id = props['app.id']
    println("read properties " + app_id)
    // 这里统一将输出目录迁移到output，是将多module的工程的产出物输出到一个目录
    File file = new File(rootProject.getProjectDir().getAbsolutePath() + "/output")
    println("rootProject " + file.getAbsolutePath())
    project.setBuildDir(file)
    return props['app.id']
}
//处理资源文件的配置，有些跨文件的配置通过编译时进行输出处理，例如cat
processResources {
    filesMatching('**/app.properties') {
        //处理cat的appid
        println("yqn start processResources filesMatching app.properties")
        filter ReplaceTokens, tokens: [
                cat_app_id: appId
        ]
    }
}
//End-把jar包输出到项目根目录下，并且添加上AppID

dependencies {
    implementation project(':devops-tools-api')
    implementation("com.yaduo.infras:core-lib:${yaduoCoreLibVersion}")
    implementation("com.yaduo.infras:core-model:${yaduoCoreLibVersion}")
    implementation("com.yaduo.infras:log-adapter:1.0.2.RELEASE")
    implementation("org.springframework.cloud:spring-cloud-starter")
    implementation("org.springframework.cloud:spring-cloud-starter-netflix-eureka-client")
    implementation("org.springframework.cloud:spring-cloud-starter-openfeign")
//    implementation("org.springframework.cloud:spring-cloud-starter-sleuth")
//    implementation("org.springframework.cloud:spring-cloud-sleuth-zipkin")

    implementation("org.springframework.boot:spring-boot-starter-web")
    implementation("org.springframework.boot:spring-boot-starter-actuator")    
    implementation("org.springframework.boot:spring-boot-starter-test")
    implementation("org.springframework.boot:spring-boot-starter-validation")
//    implementation("org.springframework.boot:spring-boot-starter-data-redis")
    implementation("org.springframework.boot:spring-boot-starter-jdbc")
    implementation("org.mybatis.spring.boot:mybatis-spring-boot-starter:2.2.2")
    runtimeOnly("mysql:mysql-connector-java:8.0.29")

    implementation("com.alibaba:druid-spring-boot-starter:1.2.9")
//    implementation("com.tencentcloudapi:tencentcloud-sdk-java:4.0.11")
//    implementation("org.springframework.boot:spring-boot-starter-data-elasticsearch")

    // apollo
    implementation("com.ctrip.framework.apollo:apollo-client:1.9.2")

    implementation("org.springdoc:springdoc-openapi-ui:1.6.8")
    implementation("com.github.xiaoymin:knife4j-springdoc-ui:3.0.3")
// https://mvnrepository.com/artifact/com.alibaba/easyexcel
    implementation 'com.alibaba:easyexcel:3.1.0'
    implementation 'org.apache.commons:commons-collections4:4.4'
    // https://mvnrepository.com/artifact/com.google.guava/guava
    implementation 'com.google.guava:guava:31.1-jre'
    // https://mvnrepository.com/artifact/com.squareup.okhttp3/okhttp
    implementation 'com.squareup.okhttp3:okhttp:4.9.3'
//    implementation 'com.atour:corp-wechat-api:0.0.56'
    implementation("com.yaduo.infras:message-api:1.0.0.RELEASE")

    testImplementation('org.springframework.boot:spring-boot-starter-test') {
        exclude group: 'org.junit.vintage', module: 'junit-vintage-engine'
    }
    implementation 'com.ctrip.framework.apollo:apollo-openapi:1.9.2'
    // https://mvnrepository.com/artifact/cn.hutool/hutool-all
    implementation group: 'cn.hutool', name: 'hutool-all', version: '5.8.35'
    implementation('com.yaduo.user:passport-api:1.3.6.RELEASE'){
        exclude group: "*", module: "*"
    }
    implementation 'com.louislivi.fastdep:fastdep-redis:1.0.7'
}

test {
    useJUnitPlatform()
}

// 添加运行Kotlin演示的任务
task runKotlinDemo(type: JavaExec) {
    group = 'application'
    description = 'Run Kotlin Demo'
    classpath = sourceSets.main.runtimeClasspath
    main = 'com.yaduo.devopstools.hello.KotlinDemoKt'
}
