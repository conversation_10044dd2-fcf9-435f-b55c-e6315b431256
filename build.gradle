import java.util.concurrent.TimeUnit

buildscript {
	ext {
		springCloudFrameVersion="2021.0.8"
		springBootVersion= "2.7.18"
		yaduoCoreLibVersion = "1.1.29.RELEASE"
		kotlinVersion = "1.8.22"
	}
	repositories {
		maven { url = "https://nexus.corp.yaduo.com/repository/maven-public/" }
		gradlePluginPortal()
		mavenCentral()
	}
	dependencies {
		classpath("org.springframework.boot:spring-boot-gradle-plugin:${springBootVersion}")
		classpath("org.jetbrains.kotlin:kotlin-gradle-plugin:${kotlinVersion}")
		classpath("org.jetbrains.kotlin:kotlin-allopen:${kotlinVersion}")
	}
}
plugins {
	id 'io.spring.dependency-management' version '1.0.11.RELEASE'
	id 'java'
}
allprojects {
	apply plugin: 'java'
	apply plugin: 'kotlin'
	apply plugin: 'io.spring.dependency-management'
	group = 'com.yaduo.one'
	version = ''
	sourceCompatibility = '11'
	targetCompatibility = '11'
    tasks.withType(JavaCompile) {
        options.encoding = "UTF-8"
    }
    tasks.withType(org.jetbrains.kotlin.gradle.tasks.KotlinCompile) {
        kotlinOptions {
            jvmTarget = "11"
            freeCompilerArgs = ["-Xjsr305=strict"]
        }
    }
	repositories {
		maven { url = "https://nexus.corp.yaduo.com/repository/maven-public/" }
		mavenCentral()
	}
	dependencyManagement {
		imports {
			mavenBom "org.springframework.cloud:spring-cloud-dependencies:${springCloudFrameVersion}"
		}
	}
}
subprojects {
	configurations.all {
		resolutionStrategy {
			cacheDynamicVersionsFor 5, TimeUnit.SECONDS
			cacheChangingModulesFor 5, TimeUnit.SECONDS
		}
	}
	dependencies {
		compileOnly('org.projectlombok:lombok:1.18.24')
		annotationProcessor 'org.projectlombok:lombok:1.18.24'
		implementation("org.jetbrains.kotlin:kotlin-stdlib-jdk8")
		implementation("org.jetbrains.kotlin:kotlin-reflect")
	}
}
